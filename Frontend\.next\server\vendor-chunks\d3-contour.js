"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-contour";
exports.ids = ["vendor-chunks/d3-contour"];
exports.modules = {

/***/ "(ssr)/../../node_modules/d3-contour/src/area.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-contour/src/area.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring) {\n    var i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n    while(++i < n)area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n    return area;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2FyZWEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxJQUFJO0lBQzFCLElBQUlDLElBQUksR0FBR0MsSUFBSUYsS0FBS0csTUFBTSxFQUFFQyxPQUFPSixJQUFJLENBQUNFLElBQUksRUFBRSxDQUFDLEVBQUUsR0FBR0YsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLElBQUksQ0FBQ0UsSUFBSSxFQUFFLENBQUMsRUFBRSxHQUFHRixJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUU7SUFDNUYsTUFBTyxFQUFFQyxJQUFJQyxFQUFHRSxRQUFRSixJQUFJLENBQUNDLElBQUksRUFBRSxDQUFDLEVBQUUsR0FBR0QsSUFBSSxDQUFDQyxFQUFFLENBQUMsRUFBRSxHQUFHRCxJQUFJLENBQUNDLElBQUksRUFBRSxDQUFDLEVBQUUsR0FBR0QsSUFBSSxDQUFDQyxFQUFFLENBQUMsRUFBRTtJQUNqRixPQUFPRztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2FyZWEuanM/NDFlNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihyaW5nKSB7XG4gIHZhciBpID0gMCwgbiA9IHJpbmcubGVuZ3RoLCBhcmVhID0gcmluZ1tuIC0gMV1bMV0gKiByaW5nWzBdWzBdIC0gcmluZ1tuIC0gMV1bMF0gKiByaW5nWzBdWzFdO1xuICB3aGlsZSAoKytpIDwgbikgYXJlYSArPSByaW5nW2kgLSAxXVsxXSAqIHJpbmdbaV1bMF0gLSByaW5nW2kgLSAxXVswXSAqIHJpbmdbaV1bMV07XG4gIHJldHVybiBhcmVhO1xufVxuIl0sIm5hbWVzIjpbInJpbmciLCJpIiwibiIsImxlbmd0aCIsImFyZWEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/area.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/array.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-contour/src/array.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\nvar slice = array.slice;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2FycmF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxRQUFRQyxNQUFNQyxTQUFTO0FBRXBCLElBQUlDLFFBQVFILE1BQU1HLEtBQUssQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9hcnJheS5qcz8zNzM0Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBhcnJheSA9IEFycmF5LnByb3RvdHlwZTtcblxuZXhwb3J0IHZhciBzbGljZSA9IGFycmF5LnNsaWNlO1xuIl0sIm5hbWVzIjpbImFycmF5IiwiQXJyYXkiLCJwcm90b3R5cGUiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/array.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/ascending.js":
/*!******************************************************!*\
  !*** ../../node_modules/d3-contour/src/ascending.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    return a - b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2FzY2VuZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLENBQUMsRUFBRUMsQ0FBQztJQUMxQixPQUFPRCxJQUFJQztBQUNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2FzY2VuZGluZy5qcz8yMzQyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGEsIGIpIHtcbiAgcmV0dXJuIGEgLSBiO1xufVxuIl0sIm5hbWVzIjpbImEiLCJiIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/constant.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-contour/src/constant.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((x)=>()=>x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZUEsQ0FBQUEsSUFBSyxJQUFNQSxDQUFBQSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2NvbnN0YW50LmpzP2I3MGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgeCA9PiAoKSA9PiB4O1xuIl0sIm5hbWVzIjpbIngiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/constant.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/contains.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-contour/src/contains.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring, hole) {\n    var i = -1, n = hole.length, c;\n    while(++i < n)if (c = ringContains(ring, hole[i])) return c;\n    return 0;\n}\nfunction ringContains(ring, point) {\n    var x = point[0], y = point[1], contains = -1;\n    for(var i = 0, n = ring.length, j = n - 1; i < n; j = i++){\n        var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n        if (segmentContains(pi, pj, point)) return 0;\n        if (yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi) contains = -contains;\n    }\n    return contains;\n}\nfunction segmentContains(a, b, c) {\n    var i;\n    return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\nfunction collinear(a, b, c) {\n    return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\nfunction within(p, q, r) {\n    return p <= q && q <= r || r <= q && q <= p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/contains.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/contours.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-contour/src/contours.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/extent.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-contour/src/ascending.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./area.js */ \"(ssr)/../../node_modules/d3-contour/src/area.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/../../node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/../../node_modules/d3-contour/src/contains.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/../../node_modules/d3-contour/src/noop.js\");\n\n\n\n\n\n\n\nvar cases = [\n    [],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ],\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                0.5\n            ],\n            [\n                0.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ],\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.5,\n                1.0\n            ],\n            [\n                1.0,\n                0.5\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                1.0,\n                1.5\n            ],\n            [\n                1.5,\n                1.0\n            ]\n        ]\n    ],\n    [\n        [\n            [\n                0.5,\n                1.0\n            ],\n            [\n                1.0,\n                1.5\n            ]\n        ]\n    ],\n    []\n];\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var dx = 1, dy = 1, threshold = d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"], smooth = smoothLinear;\n    function contours(values) {\n        var tz = threshold(values);\n        // Convert number of thresholds into uniform thresholds.\n        if (!Array.isArray(tz)) {\n            const e = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, finite);\n            tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(...(0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(e[0], e[1], tz), tz);\n            while(tz[tz.length - 1] >= e[1])tz.pop();\n            while(tz[1] < e[0])tz.shift();\n        } else {\n            tz = tz.slice().sort(_ascending_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n        }\n        return tz.map((value)=>contour(values, value));\n    }\n    // Accumulate, smooth contour rings, assign holes to exterior rings.\n    // Based on https://github.com/mbostock/shapefile/blob/v0.6.2/shp/polygon.js\n    function contour(values, value) {\n        const v = value == null ? NaN : +value;\n        if (isNaN(v)) throw new Error(`invalid value: ${value}`);\n        var polygons = [], holes = [];\n        isorings(values, v, function(ring) {\n            smooth(ring, values, v);\n            if ((0,_area_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ring) > 0) polygons.push([\n                ring\n            ]);\n            else holes.push(ring);\n        });\n        holes.forEach(function(hole) {\n            for(var i = 0, n = polygons.length, polygon; i < n; ++i){\n                if ((0,_contains_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((polygon = polygons[i])[0], hole) !== -1) {\n                    polygon.push(hole);\n                    return;\n                }\n            }\n        });\n        return {\n            type: \"MultiPolygon\",\n            value: value,\n            coordinates: polygons\n        };\n    }\n    // Marching squares with isolines stitched into rings.\n    // Based on https://github.com/topojson/topojson-client/blob/v3.0.0/src/stitch.js\n    function isorings(values, value, callback) {\n        var fragmentByStart = new Array, fragmentByEnd = new Array, x, y, t0, t1, t2, t3;\n        // Special case for the first row (y = -1, t2 = t3 = 0).\n        x = y = -1;\n        t1 = above(values[0], value);\n        cases[t1 << 1].forEach(stitch);\n        while(++x < dx - 1){\n            t0 = t1, t1 = above(values[x + 1], value);\n            cases[t0 | t1 << 1].forEach(stitch);\n        }\n        cases[t1 << 0].forEach(stitch);\n        // General case for the intermediate rows.\n        while(++y < dy - 1){\n            x = -1;\n            t1 = above(values[y * dx + dx], value);\n            t2 = above(values[y * dx], value);\n            cases[t1 << 1 | t2 << 2].forEach(stitch);\n            while(++x < dx - 1){\n                t0 = t1, t1 = above(values[y * dx + dx + x + 1], value);\n                t3 = t2, t2 = above(values[y * dx + x + 1], value);\n                cases[t0 | t1 << 1 | t2 << 2 | t3 << 3].forEach(stitch);\n            }\n            cases[t1 | t2 << 3].forEach(stitch);\n        }\n        // Special case for the last row (y = dy - 1, t0 = t1 = 0).\n        x = -1;\n        t2 = values[y * dx] >= value;\n        cases[t2 << 2].forEach(stitch);\n        while(++x < dx - 1){\n            t3 = t2, t2 = above(values[y * dx + x + 1], value);\n            cases[t2 << 2 | t3 << 3].forEach(stitch);\n        }\n        cases[t2 << 3].forEach(stitch);\n        function stitch(line) {\n            var start = [\n                line[0][0] + x,\n                line[0][1] + y\n            ], end = [\n                line[1][0] + x,\n                line[1][1] + y\n            ], startIndex = index(start), endIndex = index(end), f, g;\n            if (f = fragmentByEnd[startIndex]) {\n                if (g = fragmentByStart[endIndex]) {\n                    delete fragmentByEnd[f.end];\n                    delete fragmentByStart[g.start];\n                    if (f === g) {\n                        f.ring.push(end);\n                        callback(f.ring);\n                    } else {\n                        fragmentByStart[f.start] = fragmentByEnd[g.end] = {\n                            start: f.start,\n                            end: g.end,\n                            ring: f.ring.concat(g.ring)\n                        };\n                    }\n                } else {\n                    delete fragmentByEnd[f.end];\n                    f.ring.push(end);\n                    fragmentByEnd[f.end = endIndex] = f;\n                }\n            } else if (f = fragmentByStart[endIndex]) {\n                if (g = fragmentByEnd[startIndex]) {\n                    delete fragmentByStart[f.start];\n                    delete fragmentByEnd[g.end];\n                    if (f === g) {\n                        f.ring.push(end);\n                        callback(f.ring);\n                    } else {\n                        fragmentByStart[g.start] = fragmentByEnd[f.end] = {\n                            start: g.start,\n                            end: f.end,\n                            ring: g.ring.concat(f.ring)\n                        };\n                    }\n                } else {\n                    delete fragmentByStart[f.start];\n                    f.ring.unshift(start);\n                    fragmentByStart[f.start = startIndex] = f;\n                }\n            } else {\n                fragmentByStart[startIndex] = fragmentByEnd[endIndex] = {\n                    start: startIndex,\n                    end: endIndex,\n                    ring: [\n                        start,\n                        end\n                    ]\n                };\n            }\n        }\n    }\n    function index(point) {\n        return point[0] * 2 + point[1] * (dx + 1) * 4;\n    }\n    function smoothLinear(ring, values, value) {\n        ring.forEach(function(point) {\n            var x = point[0], y = point[1], xt = x | 0, yt = y | 0, v1 = valid(values[yt * dx + xt]);\n            if (x > 0 && x < dx && xt === x) {\n                point[0] = smooth1(x, valid(values[yt * dx + xt - 1]), v1, value);\n            }\n            if (y > 0 && y < dy && yt === y) {\n                point[1] = smooth1(y, valid(values[(yt - 1) * dx + xt]), v1, value);\n            }\n        });\n    }\n    contours.contour = contour;\n    contours.size = function(_) {\n        if (!arguments.length) return [\n            dx,\n            dy\n        ];\n        var _0 = Math.floor(_[0]), _1 = Math.floor(_[1]);\n        if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n        return dx = _0, dy = _1, contours;\n    };\n    contours.thresholds = function(_) {\n        return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_8__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_), contours) : threshold;\n    };\n    contours.smooth = function(_) {\n        return arguments.length ? (smooth = _ ? smoothLinear : _noop_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"], contours) : smooth === smoothLinear;\n    };\n    return contours;\n}\n// When computing the extent, ignore infinite values (as well as invalid ones).\nfunction finite(x) {\n    return isFinite(x) ? x : NaN;\n}\n// Is the (possibly invalid) x greater than or equal to the (known valid) value?\n// Treat any invalid value as below negative infinity.\nfunction above(x, value) {\n    return x == null ? false : +x >= value;\n}\n// During smoothing, treat any invalid value as negative infinity.\nfunction valid(v) {\n    return v == null || isNaN(v = +v) ? -Infinity : v;\n}\nfunction smooth1(x, v0, v1, value) {\n    const a = value - v0;\n    const b = v1 - v0;\n    const d = isFinite(a) || isFinite(b) ? a / b : Math.sign(a) / Math.sign(b);\n    return isNaN(d) ? x : x + d - 0.5;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/contours.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/density.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-contour/src/density.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/blur.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/max.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/../../node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/../../node_modules/d3-contour/src/contours.js\");\n\n\n\n\nfunction defaultX(d) {\n    return d[0];\n}\nfunction defaultY(d) {\n    return d[1];\n}\nfunction defaultWeight() {\n    return 1;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var x = defaultX, y = defaultY, weight = defaultWeight, dx = 960, dy = 500, r = 20, k = 2, o = r * 3, n = dx + o * 2 >> k, m = dy + o * 2 >> k, threshold = (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(20);\n    function grid(data) {\n        var values = new Float32Array(n * m), pow2k = Math.pow(2, -k), i = -1;\n        for (const d of data){\n            var xi = (x(d, ++i, data) + o) * pow2k, yi = (y(d, i, data) + o) * pow2k, wi = +weight(d, i, data);\n            if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n                var x0 = Math.floor(xi), y0 = Math.floor(yi), xt = xi - x0 - 0.5, yt = yi - y0 - 0.5;\n                values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n                values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n                values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n                values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n            }\n        }\n        (0,d3_array__WEBPACK_IMPORTED_MODULE_1__.blur2)({\n            data: values,\n            width: n,\n            height: m\n        }, r * pow2k);\n        return values;\n    }\n    function density(data) {\n        var values = grid(data), tz = threshold(values), pow4k = Math.pow(2, 2 * k);\n        // Convert number of thresholds into uniform thresholds.\n        if (!Array.isArray(tz)) {\n            tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Number.MIN_VALUE, (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k, tz);\n        }\n        return (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().size([\n            n,\n            m\n        ]).thresholds(tz.map((d)=>d * pow4k))(values).map((c, i)=>(c.value = +tz[i], transform(c)));\n    }\n    density.contours = function(data) {\n        var values = grid(data), contours = (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().size([\n            n,\n            m\n        ]), pow4k = Math.pow(2, 2 * k), contour = (value)=>{\n            value = +value;\n            var c = transform(contours.contour(values, value * pow4k));\n            c.value = value; // preserve exact threshold value\n            return c;\n        };\n        Object.defineProperty(contour, \"max\", {\n            get: ()=>(0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k\n        });\n        return contour;\n    };\n    function transform(geometry) {\n        geometry.coordinates.forEach(transformPolygon);\n        return geometry;\n    }\n    function transformPolygon(coordinates) {\n        coordinates.forEach(transformRing);\n    }\n    function transformRing(coordinates) {\n        coordinates.forEach(transformPoint);\n    }\n    // TODO Optimize.\n    function transformPoint(coordinates) {\n        coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n        coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n    }\n    function resize() {\n        o = r * 3;\n        n = dx + o * 2 >> k;\n        m = dy + o * 2 >> k;\n        return density;\n    }\n    density.x = function(_) {\n        return arguments.length ? (x = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : x;\n    };\n    density.y = function(_) {\n        return arguments.length ? (y = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : y;\n    };\n    density.weight = function(_) {\n        return arguments.length ? (weight = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : weight;\n    };\n    density.size = function(_) {\n        if (!arguments.length) return [\n            dx,\n            dy\n        ];\n        var _0 = +_[0], _1 = +_[1];\n        if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n        return dx = _0, dy = _1, resize();\n    };\n    density.cellSize = function(_) {\n        if (!arguments.length) return 1 << k;\n        if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n        return k = Math.floor(Math.log(_) / Math.LN2), resize();\n    };\n    density.thresholds = function(_) {\n        return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_5__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_), density) : threshold;\n    };\n    density.bandwidth = function(_) {\n        if (!arguments.length) return Math.sqrt(r * (r + 1));\n        if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n        return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n    };\n    return density;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2RlbnNpdHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEyQztBQUNWO0FBQ0k7QUFDQTtBQUVyQyxTQUFTTSxTQUFTQyxDQUFDO0lBQ2pCLE9BQU9BLENBQUMsQ0FBQyxFQUFFO0FBQ2I7QUFFQSxTQUFTQyxTQUFTRCxDQUFDO0lBQ2pCLE9BQU9BLENBQUMsQ0FBQyxFQUFFO0FBQ2I7QUFFQSxTQUFTRTtJQUNQLE9BQU87QUFDVDtBQUVBLDZCQUFlLHNDQUFXO0lBQ3hCLElBQUlDLElBQUlKLFVBQ0pLLElBQUlILFVBQ0pJLFNBQVNILGVBQ1RJLEtBQUssS0FDTEMsS0FBSyxLQUNMQyxJQUFJLElBQ0pDLElBQUksR0FDSkMsSUFBSUYsSUFBSSxHQUNSRyxJQUFJLEtBQU1ELElBQUksS0FBTUQsR0FDcEJHLElBQUksS0FBTUYsSUFBSSxLQUFNRCxHQUNwQkksWUFBWWhCLHdEQUFRQSxDQUFDO0lBRXpCLFNBQVNpQixLQUFLQyxJQUFJO1FBQ2hCLElBQUlDLFNBQVMsSUFBSUMsYUFBYU4sSUFBSUMsSUFDOUJNLFFBQVFDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHLENBQUNYLElBQ3JCWSxJQUFJLENBQUM7UUFFVCxLQUFLLE1BQU1yQixLQUFLZSxLQUFNO1lBQ3BCLElBQUlPLEtBQUssQ0FBQ25CLEVBQUVILEdBQUcsRUFBRXFCLEdBQUdOLFFBQVFMLENBQUFBLElBQUtRLE9BQzdCSyxLQUFLLENBQUNuQixFQUFFSixHQUFHcUIsR0FBR04sUUFBUUwsQ0FBQUEsSUFBS1EsT0FDM0JNLEtBQUssQ0FBQ25CLE9BQU9MLEdBQUdxQixHQUFHTjtZQUN2QixJQUFJUyxNQUFNRixNQUFNLEtBQUtBLEtBQUtYLEtBQUtZLE1BQU0sS0FBS0EsS0FBS1gsR0FBRztnQkFDaEQsSUFBSWEsS0FBS04sS0FBS08sS0FBSyxDQUFDSixLQUNoQkssS0FBS1IsS0FBS08sS0FBSyxDQUFDSCxLQUNoQkssS0FBS04sS0FBS0csS0FBSyxLQUNmSSxLQUFLTixLQUFLSSxLQUFLO2dCQUNuQlgsTUFBTSxDQUFDUyxLQUFLRSxLQUFLaEIsRUFBRSxJQUFJLENBQUMsSUFBSWlCLEVBQUMsSUFBTSxLQUFJQyxFQUFDLElBQUtMO2dCQUM3Q1IsTUFBTSxDQUFDUyxLQUFLLElBQUlFLEtBQUtoQixFQUFFLElBQUlpQixLQUFNLEtBQUlDLEVBQUMsSUFBS0w7Z0JBQzNDUixNQUFNLENBQUNTLEtBQUssSUFBSSxDQUFDRSxLQUFLLEtBQUtoQixFQUFFLElBQUlpQixLQUFLQyxLQUFLTDtnQkFDM0NSLE1BQU0sQ0FBQ1MsS0FBSyxDQUFDRSxLQUFLLEtBQUtoQixFQUFFLElBQUksQ0FBQyxJQUFJaUIsRUFBQyxJQUFLQyxLQUFLTDtZQUMvQztRQUNGO1FBRUEvQiwrQ0FBS0EsQ0FBQztZQUFDc0IsTUFBTUM7WUFBUWMsT0FBT25CO1lBQUdvQixRQUFRbkI7UUFBQyxHQUFHSixJQUFJVTtRQUMvQyxPQUFPRjtJQUNUO0lBRUEsU0FBU2dCLFFBQVFqQixJQUFJO1FBQ25CLElBQUlDLFNBQVNGLEtBQUtDLE9BQ2RrQixLQUFLcEIsVUFBVUcsU0FDZmtCLFFBQVFmLEtBQUtDLEdBQUcsQ0FBQyxHQUFHLElBQUlYO1FBRTVCLHdEQUF3RDtRQUN4RCxJQUFJLENBQUMwQixNQUFNQyxPQUFPLENBQUNILEtBQUs7WUFDdEJBLEtBQUt0QyxvREFBS0EsQ0FBQzBDLE9BQU9DLFNBQVMsRUFBRTVDLG9EQUFHQSxDQUFDc0IsVUFBVWtCLE9BQU9EO1FBQ3BEO1FBRUEsT0FBT25DLHdEQUFRQSxHQUNWeUMsSUFBSSxDQUFDO1lBQUM1QjtZQUFHQztTQUFFLEVBQ1g0QixVQUFVLENBQUNQLEdBQUdRLEdBQUcsQ0FBQ3pDLENBQUFBLElBQUtBLElBQUlrQyxRQUM3QmxCLFFBQ0V5QixHQUFHLENBQUMsQ0FBQ0MsR0FBR3JCLElBQU9xQixDQUFBQSxFQUFFQyxLQUFLLEdBQUcsQ0FBQ1YsRUFBRSxDQUFDWixFQUFFLEVBQUV1QixVQUFVRixFQUFDO0lBQ25EO0lBRUFWLFFBQVFhLFFBQVEsR0FBRyxTQUFTOUIsSUFBSTtRQUM5QixJQUFJQyxTQUFTRixLQUFLQyxPQUNkOEIsV0FBVy9DLHdEQUFRQSxHQUFHeUMsSUFBSSxDQUFDO1lBQUM1QjtZQUFHQztTQUFFLEdBQ2pDc0IsUUFBUWYsS0FBS0MsR0FBRyxDQUFDLEdBQUcsSUFBSVgsSUFDeEJxQyxVQUFVSCxDQUFBQTtZQUNSQSxRQUFRLENBQUNBO1lBQ1QsSUFBSUQsSUFBSUUsVUFBVUMsU0FBU0MsT0FBTyxDQUFDOUIsUUFBUTJCLFFBQVFUO1lBQ25EUSxFQUFFQyxLQUFLLEdBQUdBLE9BQU8saUNBQWlDO1lBQ2xELE9BQU9EO1FBQ1Q7UUFDSkssT0FBT0MsY0FBYyxDQUFDRixTQUFTLE9BQU87WUFBQ0csS0FBSyxJQUFNdkQsb0RBQUdBLENBQUNzQixVQUFVa0I7UUFBSztRQUNyRSxPQUFPWTtJQUNUO0lBRUEsU0FBU0YsVUFBVU0sUUFBUTtRQUN6QkEsU0FBU0MsV0FBVyxDQUFDQyxPQUFPLENBQUNDO1FBQzdCLE9BQU9IO0lBQ1Q7SUFFQSxTQUFTRyxpQkFBaUJGLFdBQVc7UUFDbkNBLFlBQVlDLE9BQU8sQ0FBQ0U7SUFDdEI7SUFFQSxTQUFTQSxjQUFjSCxXQUFXO1FBQ2hDQSxZQUFZQyxPQUFPLENBQUNHO0lBQ3RCO0lBRUEsaUJBQWlCO0lBQ2pCLFNBQVNBLGVBQWVKLFdBQVc7UUFDakNBLFdBQVcsQ0FBQyxFQUFFLEdBQUdBLFdBQVcsQ0FBQyxFQUFFLEdBQUdoQyxLQUFLQyxHQUFHLENBQUMsR0FBR1gsS0FBS0M7UUFDbkR5QyxXQUFXLENBQUMsRUFBRSxHQUFHQSxXQUFXLENBQUMsRUFBRSxHQUFHaEMsS0FBS0MsR0FBRyxDQUFDLEdBQUdYLEtBQUtDO0lBQ3JEO0lBRUEsU0FBUzhDO1FBQ1A5QyxJQUFJRixJQUFJO1FBQ1JHLElBQUksS0FBTUQsSUFBSSxLQUFNRDtRQUNwQkcsSUFBSSxLQUFNRixJQUFJLEtBQU1EO1FBQ3BCLE9BQU91QjtJQUNUO0lBRUFBLFFBQVE3QixDQUFDLEdBQUcsU0FBU3NELENBQUM7UUFDcEIsT0FBT0MsVUFBVUMsTUFBTSxHQUFJeEQsQ0FBQUEsSUFBSSxPQUFPc0QsTUFBTSxhQUFhQSxJQUFJNUQsd0RBQVFBLENBQUMsQ0FBQzRELElBQUl6QixPQUFNLElBQUs3QjtJQUN4RjtJQUVBNkIsUUFBUTVCLENBQUMsR0FBRyxTQUFTcUQsQ0FBQztRQUNwQixPQUFPQyxVQUFVQyxNQUFNLEdBQUl2RCxDQUFBQSxJQUFJLE9BQU9xRCxNQUFNLGFBQWFBLElBQUk1RCx3REFBUUEsQ0FBQyxDQUFDNEQsSUFBSXpCLE9BQU0sSUFBSzVCO0lBQ3hGO0lBRUE0QixRQUFRM0IsTUFBTSxHQUFHLFNBQVNvRCxDQUFDO1FBQ3pCLE9BQU9DLFVBQVVDLE1BQU0sR0FBSXRELENBQUFBLFNBQVMsT0FBT29ELE1BQU0sYUFBYUEsSUFBSTVELHdEQUFRQSxDQUFDLENBQUM0RCxJQUFJekIsT0FBTSxJQUFLM0I7SUFDN0Y7SUFFQTJCLFFBQVFPLElBQUksR0FBRyxTQUFTa0IsQ0FBQztRQUN2QixJQUFJLENBQUNDLFVBQVVDLE1BQU0sRUFBRSxPQUFPO1lBQUNyRDtZQUFJQztTQUFHO1FBQ3RDLElBQUlxRCxLQUFLLENBQUNILENBQUMsQ0FBQyxFQUFFLEVBQUVJLEtBQUssQ0FBQ0osQ0FBQyxDQUFDLEVBQUU7UUFDMUIsSUFBSSxDQUFFRyxDQUFBQSxNQUFNLEtBQUtDLE1BQU0sSUFBSSxNQUFNLElBQUlDLE1BQU07UUFDM0MsT0FBT3hELEtBQUtzRCxJQUFJckQsS0FBS3NELElBQUlMO0lBQzNCO0lBRUF4QixRQUFRK0IsUUFBUSxHQUFHLFNBQVNOLENBQUM7UUFDM0IsSUFBSSxDQUFDQyxVQUFVQyxNQUFNLEVBQUUsT0FBTyxLQUFLbEQ7UUFDbkMsSUFBSSxDQUFFLEVBQUNnRCxJQUFJLENBQUNBLENBQUFBLEtBQU0sSUFBSSxNQUFNLElBQUlLLE1BQU07UUFDdEMsT0FBT3JELElBQUlVLEtBQUtPLEtBQUssQ0FBQ1AsS0FBSzZDLEdBQUcsQ0FBQ1AsS0FBS3RDLEtBQUs4QyxHQUFHLEdBQUdUO0lBQ2pEO0lBRUF4QixRQUFRUSxVQUFVLEdBQUcsU0FBU2lCLENBQUM7UUFDN0IsT0FBT0MsVUFBVUMsTUFBTSxHQUFJOUMsQ0FBQUEsWUFBWSxPQUFPNEMsTUFBTSxhQUFhQSxJQUFJdEIsTUFBTUMsT0FBTyxDQUFDcUIsS0FBSzVELHdEQUFRQSxDQUFDRCw0Q0FBS0EsQ0FBQ3NFLElBQUksQ0FBQ1QsTUFBTTVELHdEQUFRQSxDQUFDNEQsSUFBSXpCLE9BQU0sSUFBS25CO0lBQzVJO0lBRUFtQixRQUFRbUMsU0FBUyxHQUFHLFNBQVNWLENBQUM7UUFDNUIsSUFBSSxDQUFDQyxVQUFVQyxNQUFNLEVBQUUsT0FBT3hDLEtBQUtpRCxJQUFJLENBQUM1RCxJQUFLQSxDQUFBQSxJQUFJO1FBQ2pELElBQUksQ0FBRSxFQUFDaUQsSUFBSSxDQUFDQSxDQUFBQSxLQUFNLElBQUksTUFBTSxJQUFJSyxNQUFNO1FBQ3RDLE9BQU90RCxJQUFJLENBQUNXLEtBQUtpRCxJQUFJLENBQUMsSUFBSVgsSUFBSUEsSUFBSSxLQUFLLEtBQUssR0FBR0Q7SUFDakQ7SUFFQSxPQUFPeEI7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9kZW5zaXR5LmpzPzQ4ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtibHVyMiwgbWF4LCB0aWNrc30gZnJvbSBcImQzLWFycmF5XCI7XG5pbXBvcnQge3NsaWNlfSBmcm9tIFwiLi9hcnJheS5qc1wiO1xuaW1wb3J0IGNvbnN0YW50IGZyb20gXCIuL2NvbnN0YW50LmpzXCI7XG5pbXBvcnQgQ29udG91cnMgZnJvbSBcIi4vY29udG91cnMuanNcIjtcblxuZnVuY3Rpb24gZGVmYXVsdFgoZCkge1xuICByZXR1cm4gZFswXTtcbn1cblxuZnVuY3Rpb24gZGVmYXVsdFkoZCkge1xuICByZXR1cm4gZFsxXTtcbn1cblxuZnVuY3Rpb24gZGVmYXVsdFdlaWdodCgpIHtcbiAgcmV0dXJuIDE7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICB2YXIgeCA9IGRlZmF1bHRYLFxuICAgICAgeSA9IGRlZmF1bHRZLFxuICAgICAgd2VpZ2h0ID0gZGVmYXVsdFdlaWdodCxcbiAgICAgIGR4ID0gOTYwLFxuICAgICAgZHkgPSA1MDAsXG4gICAgICByID0gMjAsIC8vIGJsdXIgcmFkaXVzXG4gICAgICBrID0gMiwgLy8gbG9nMihncmlkIGNlbGwgc2l6ZSlcbiAgICAgIG8gPSByICogMywgLy8gZ3JpZCBvZmZzZXQsIHRvIHBhZCBmb3IgYmx1clxuICAgICAgbiA9IChkeCArIG8gKiAyKSA+PiBrLCAvLyBncmlkIHdpZHRoXG4gICAgICBtID0gKGR5ICsgbyAqIDIpID4+IGssIC8vIGdyaWQgaGVpZ2h0XG4gICAgICB0aHJlc2hvbGQgPSBjb25zdGFudCgyMCk7XG5cbiAgZnVuY3Rpb24gZ3JpZChkYXRhKSB7XG4gICAgdmFyIHZhbHVlcyA9IG5ldyBGbG9hdDMyQXJyYXkobiAqIG0pLFxuICAgICAgICBwb3cyayA9IE1hdGgucG93KDIsIC1rKSxcbiAgICAgICAgaSA9IC0xO1xuXG4gICAgZm9yIChjb25zdCBkIG9mIGRhdGEpIHtcbiAgICAgIHZhciB4aSA9ICh4KGQsICsraSwgZGF0YSkgKyBvKSAqIHBvdzJrLFxuICAgICAgICAgIHlpID0gKHkoZCwgaSwgZGF0YSkgKyBvKSAqIHBvdzJrLFxuICAgICAgICAgIHdpID0gK3dlaWdodChkLCBpLCBkYXRhKTtcbiAgICAgIGlmICh3aSAmJiB4aSA+PSAwICYmIHhpIDwgbiAmJiB5aSA+PSAwICYmIHlpIDwgbSkge1xuICAgICAgICB2YXIgeDAgPSBNYXRoLmZsb29yKHhpKSxcbiAgICAgICAgICAgIHkwID0gTWF0aC5mbG9vcih5aSksXG4gICAgICAgICAgICB4dCA9IHhpIC0geDAgLSAwLjUsXG4gICAgICAgICAgICB5dCA9IHlpIC0geTAgLSAwLjU7XG4gICAgICAgIHZhbHVlc1t4MCArIHkwICogbl0gKz0gKDEgLSB4dCkgKiAoMSAtIHl0KSAqIHdpO1xuICAgICAgICB2YWx1ZXNbeDAgKyAxICsgeTAgKiBuXSArPSB4dCAqICgxIC0geXQpICogd2k7XG4gICAgICAgIHZhbHVlc1t4MCArIDEgKyAoeTAgKyAxKSAqIG5dICs9IHh0ICogeXQgKiB3aTtcbiAgICAgICAgdmFsdWVzW3gwICsgKHkwICsgMSkgKiBuXSArPSAoMSAtIHh0KSAqIHl0ICogd2k7XG4gICAgICB9XG4gICAgfVxuXG4gICAgYmx1cjIoe2RhdGE6IHZhbHVlcywgd2lkdGg6IG4sIGhlaWdodDogbX0sIHIgKiBwb3cyayk7XG4gICAgcmV0dXJuIHZhbHVlcztcbiAgfVxuXG4gIGZ1bmN0aW9uIGRlbnNpdHkoZGF0YSkge1xuICAgIHZhciB2YWx1ZXMgPSBncmlkKGRhdGEpLFxuICAgICAgICB0eiA9IHRocmVzaG9sZCh2YWx1ZXMpLFxuICAgICAgICBwb3c0ayA9IE1hdGgucG93KDIsIDIgKiBrKTtcblxuICAgIC8vIENvbnZlcnQgbnVtYmVyIG9mIHRocmVzaG9sZHMgaW50byB1bmlmb3JtIHRocmVzaG9sZHMuXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHR6KSkge1xuICAgICAgdHogPSB0aWNrcyhOdW1iZXIuTUlOX1ZBTFVFLCBtYXgodmFsdWVzKSAvIHBvdzRrLCB0eik7XG4gICAgfVxuXG4gICAgcmV0dXJuIENvbnRvdXJzKClcbiAgICAgICAgLnNpemUoW24sIG1dKVxuICAgICAgICAudGhyZXNob2xkcyh0ei5tYXAoZCA9PiBkICogcG93NGspKVxuICAgICAgKHZhbHVlcylcbiAgICAgICAgLm1hcCgoYywgaSkgPT4gKGMudmFsdWUgPSArdHpbaV0sIHRyYW5zZm9ybShjKSkpO1xuICB9XG5cbiAgZGVuc2l0eS5jb250b3VycyA9IGZ1bmN0aW9uKGRhdGEpIHtcbiAgICB2YXIgdmFsdWVzID0gZ3JpZChkYXRhKSxcbiAgICAgICAgY29udG91cnMgPSBDb250b3VycygpLnNpemUoW24sIG1dKSxcbiAgICAgICAgcG93NGsgPSBNYXRoLnBvdygyLCAyICogayksXG4gICAgICAgIGNvbnRvdXIgPSB2YWx1ZSA9PiB7XG4gICAgICAgICAgdmFsdWUgPSArdmFsdWU7XG4gICAgICAgICAgdmFyIGMgPSB0cmFuc2Zvcm0oY29udG91cnMuY29udG91cih2YWx1ZXMsIHZhbHVlICogcG93NGspKTtcbiAgICAgICAgICBjLnZhbHVlID0gdmFsdWU7IC8vIHByZXNlcnZlIGV4YWN0IHRocmVzaG9sZCB2YWx1ZVxuICAgICAgICAgIHJldHVybiBjO1xuICAgICAgICB9O1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShjb250b3VyLCBcIm1heFwiLCB7Z2V0OiAoKSA9PiBtYXgodmFsdWVzKSAvIHBvdzRrfSk7XG4gICAgcmV0dXJuIGNvbnRvdXI7XG4gIH07XG5cbiAgZnVuY3Rpb24gdHJhbnNmb3JtKGdlb21ldHJ5KSB7XG4gICAgZ2VvbWV0cnkuY29vcmRpbmF0ZXMuZm9yRWFjaCh0cmFuc2Zvcm1Qb2x5Z29uKTtcbiAgICByZXR1cm4gZ2VvbWV0cnk7XG4gIH1cblxuICBmdW5jdGlvbiB0cmFuc2Zvcm1Qb2x5Z29uKGNvb3JkaW5hdGVzKSB7XG4gICAgY29vcmRpbmF0ZXMuZm9yRWFjaCh0cmFuc2Zvcm1SaW5nKTtcbiAgfVxuXG4gIGZ1bmN0aW9uIHRyYW5zZm9ybVJpbmcoY29vcmRpbmF0ZXMpIHtcbiAgICBjb29yZGluYXRlcy5mb3JFYWNoKHRyYW5zZm9ybVBvaW50KTtcbiAgfVxuXG4gIC8vIFRPRE8gT3B0aW1pemUuXG4gIGZ1bmN0aW9uIHRyYW5zZm9ybVBvaW50KGNvb3JkaW5hdGVzKSB7XG4gICAgY29vcmRpbmF0ZXNbMF0gPSBjb29yZGluYXRlc1swXSAqIE1hdGgucG93KDIsIGspIC0gbztcbiAgICBjb29yZGluYXRlc1sxXSA9IGNvb3JkaW5hdGVzWzFdICogTWF0aC5wb3coMiwgaykgLSBvO1xuICB9XG5cbiAgZnVuY3Rpb24gcmVzaXplKCkge1xuICAgIG8gPSByICogMztcbiAgICBuID0gKGR4ICsgbyAqIDIpID4+IGs7XG4gICAgbSA9IChkeSArIG8gKiAyKSA+PiBrO1xuICAgIHJldHVybiBkZW5zaXR5O1xuICB9XG5cbiAgZGVuc2l0eS54ID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHggPSB0eXBlb2YgXyA9PT0gXCJmdW5jdGlvblwiID8gXyA6IGNvbnN0YW50KCtfKSwgZGVuc2l0eSkgOiB4O1xuICB9O1xuXG4gIGRlbnNpdHkueSA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh5ID0gdHlwZW9mIF8gPT09IFwiZnVuY3Rpb25cIiA/IF8gOiBjb25zdGFudCgrXyksIGRlbnNpdHkpIDogeTtcbiAgfTtcblxuICBkZW5zaXR5LndlaWdodCA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh3ZWlnaHQgPSB0eXBlb2YgXyA9PT0gXCJmdW5jdGlvblwiID8gXyA6IGNvbnN0YW50KCtfKSwgZGVuc2l0eSkgOiB3ZWlnaHQ7XG4gIH07XG5cbiAgZGVuc2l0eS5zaXplID0gZnVuY3Rpb24oXykge1xuICAgIGlmICghYXJndW1lbnRzLmxlbmd0aCkgcmV0dXJuIFtkeCwgZHldO1xuICAgIHZhciBfMCA9ICtfWzBdLCBfMSA9ICtfWzFdO1xuICAgIGlmICghKF8wID49IDAgJiYgXzEgPj0gMCkpIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgc2l6ZVwiKTtcbiAgICByZXR1cm4gZHggPSBfMCwgZHkgPSBfMSwgcmVzaXplKCk7XG4gIH07XG5cbiAgZGVuc2l0eS5jZWxsU2l6ZSA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiAxIDw8IGs7XG4gICAgaWYgKCEoKF8gPSArXykgPj0gMSkpIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgY2VsbCBzaXplXCIpO1xuICAgIHJldHVybiBrID0gTWF0aC5mbG9vcihNYXRoLmxvZyhfKSAvIE1hdGguTE4yKSwgcmVzaXplKCk7XG4gIH07XG5cbiAgZGVuc2l0eS50aHJlc2hvbGRzID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHRocmVzaG9sZCA9IHR5cGVvZiBfID09PSBcImZ1bmN0aW9uXCIgPyBfIDogQXJyYXkuaXNBcnJheShfKSA/IGNvbnN0YW50KHNsaWNlLmNhbGwoXykpIDogY29uc3RhbnQoXyksIGRlbnNpdHkpIDogdGhyZXNob2xkO1xuICB9O1xuXG4gIGRlbnNpdHkuYmFuZHdpZHRoID0gZnVuY3Rpb24oXykge1xuICAgIGlmICghYXJndW1lbnRzLmxlbmd0aCkgcmV0dXJuIE1hdGguc3FydChyICogKHIgKyAxKSk7XG4gICAgaWYgKCEoKF8gPSArXykgPj0gMCkpIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgYmFuZHdpZHRoXCIpO1xuICAgIHJldHVybiByID0gKE1hdGguc3FydCg0ICogXyAqIF8gKyAxKSAtIDEpIC8gMiwgcmVzaXplKCk7XG4gIH07XG5cbiAgcmV0dXJuIGRlbnNpdHk7XG59XG4iXSwibmFtZXMiOlsiYmx1cjIiLCJtYXgiLCJ0aWNrcyIsInNsaWNlIiwiY29uc3RhbnQiLCJDb250b3VycyIsImRlZmF1bHRYIiwiZCIsImRlZmF1bHRZIiwiZGVmYXVsdFdlaWdodCIsIngiLCJ5Iiwid2VpZ2h0IiwiZHgiLCJkeSIsInIiLCJrIiwibyIsIm4iLCJtIiwidGhyZXNob2xkIiwiZ3JpZCIsImRhdGEiLCJ2YWx1ZXMiLCJGbG9hdDMyQXJyYXkiLCJwb3cyayIsIk1hdGgiLCJwb3ciLCJpIiwieGkiLCJ5aSIsIndpIiwieDAiLCJmbG9vciIsInkwIiwieHQiLCJ5dCIsIndpZHRoIiwiaGVpZ2h0IiwiZGVuc2l0eSIsInR6IiwicG93NGsiLCJBcnJheSIsImlzQXJyYXkiLCJOdW1iZXIiLCJNSU5fVkFMVUUiLCJzaXplIiwidGhyZXNob2xkcyIsIm1hcCIsImMiLCJ2YWx1ZSIsInRyYW5zZm9ybSIsImNvbnRvdXJzIiwiY29udG91ciIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZ2V0IiwiZ2VvbWV0cnkiLCJjb29yZGluYXRlcyIsImZvckVhY2giLCJ0cmFuc2Zvcm1Qb2x5Z29uIiwidHJhbnNmb3JtUmluZyIsInRyYW5zZm9ybVBvaW50IiwicmVzaXplIiwiXyIsImFyZ3VtZW50cyIsImxlbmd0aCIsIl8wIiwiXzEiLCJFcnJvciIsImNlbGxTaXplIiwibG9nIiwiTE4yIiwiY2FsbCIsImJhbmR3aWR0aCIsInNxcnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/density.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/index.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-contour/src/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contourDensity: () => (/* reexport safe */ _density_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   contours: () => (/* reexport safe */ _contours_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/../../node_modules/d3-contour/src/contours.js\");\n/* harmony import */ var _density_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./density.js */ \"(ssr)/../../node_modules/d3-contour/src/density.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0Q7QUFDSyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9pbmRleC5qcz9kZmNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7ZGVmYXVsdCBhcyBjb250b3Vyc30gZnJvbSBcIi4vY29udG91cnMuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBjb250b3VyRGVuc2l0eX0gZnJvbSBcIi4vZGVuc2l0eS5qc1wiO1xuIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJjb250b3VycyIsImNvbnRvdXJEZW5zaXR5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-contour/src/noop.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-contour/src/noop.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL25vb3AuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLHNDQUFXLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvbm9vcC5qcz8zNGYxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge31cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-contour/src/noop.js\n");

/***/ })

};
;