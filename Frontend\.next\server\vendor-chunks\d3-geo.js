"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-geo";
exports.ids = ["vendor-chunks/d3-geo"];
exports.modules = {

/***/ "(ssr)/../../node_modules/d3-geo/src/area.js":
/*!*********************************************!*\
  !*** ../../node_modules/d3-geo/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areaRingSum: () => (/* binding */ areaRingSum),\n/* harmony export */   areaStream: () => (/* binding */ areaStream),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/../../node_modules/d3-geo/src/stream.js\");\n\n\n\n\nvar areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n// hello?\nvar areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), lambda00, phi00, lambda0, cosPhi0, sinPhi0;\nvar areaStream = {\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    polygonStart: function() {\n        areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        areaStream.lineStart = areaRingStart;\n        areaStream.lineEnd = areaRingEnd;\n    },\n    polygonEnd: function() {\n        var areaRing = +areaRingSum;\n        areaSum.add(areaRing < 0 ? _math_js__WEBPACK_IMPORTED_MODULE_2__.tau + areaRing : areaRing);\n        this.lineStart = this.lineEnd = this.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n    },\n    sphere: function() {\n        areaSum.add(_math_js__WEBPACK_IMPORTED_MODULE_2__.tau);\n    }\n};\nfunction areaRingStart() {\n    areaStream.point = areaPointFirst;\n}\nfunction areaRingEnd() {\n    areaPoint(lambda00, phi00);\n}\nfunction areaPointFirst(lambda, phi) {\n    areaStream.point = areaPoint;\n    lambda00 = lambda, phi00 = phi;\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians;\n    lambda0 = lambda, cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(phi = phi / 2 + _math_js__WEBPACK_IMPORTED_MODULE_2__.quarterPi), sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(phi);\n}\nfunction areaPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians;\n    phi = phi / 2 + _math_js__WEBPACK_IMPORTED_MODULE_2__.quarterPi; // half the angular distance from south pole\n    // Spherical excess E for a spherical triangle with vertices: south pole,\n    // previous point, current point.  Uses a formula derived from Cagnoli’s\n    // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n    var dLambda = lambda - lambda0, sdLambda = dLambda >= 0 ? 1 : -1, adLambda = sdLambda * dLambda, cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(phi), sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(phi), k = sinPhi0 * sinPhi, u = cosPhi0 * cosPhi + k * (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(adLambda), v = k * sdLambda * (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(adLambda);\n    areaRingSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.atan2)(v, u));\n    // Advance the previous points.\n    lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n    areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, areaStream);\n    return areaSum * 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/area.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/bounds.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-geo/src/bounds.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./area.js */ \"(ssr)/../../node_modules/d3-geo/src/area.js\");\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/../../node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/../../node_modules/d3-geo/src/stream.js\");\n\n\n\n\n\nvar lambda0, phi0, lambda1, phi1, lambda2, lambda00, phi00, p0, deltaSum, ranges, range;\nvar boundsStream = {\n    point: boundsPoint,\n    lineStart: boundsLineStart,\n    lineEnd: boundsLineEnd,\n    polygonStart: function() {\n        boundsStream.point = boundsRingPoint;\n        boundsStream.lineStart = boundsRingStart;\n        boundsStream.lineEnd = boundsRingEnd;\n        deltaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.polygonStart();\n    },\n    polygonEnd: function() {\n        _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.polygonEnd();\n        boundsStream.point = boundsPoint;\n        boundsStream.lineStart = boundsLineStart;\n        boundsStream.lineEnd = boundsLineEnd;\n        if (_area_js__WEBPACK_IMPORTED_MODULE_1__.areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n        else if (deltaSum > _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) phi1 = 90;\n        else if (deltaSum < -_math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) phi0 = -90;\n        range[0] = lambda0, range[1] = lambda1;\n    },\n    sphere: function() {\n        lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    }\n};\nfunction boundsPoint(lambda, phi) {\n    ranges.push(range = [\n        lambda0 = lambda,\n        lambda1 = lambda\n    ]);\n    if (phi < phi0) phi0 = phi;\n    if (phi > phi1) phi1 = phi;\n}\nfunction linePoint(lambda, phi) {\n    var p = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)([\n        lambda * _math_js__WEBPACK_IMPORTED_MODULE_2__.radians,\n        phi * _math_js__WEBPACK_IMPORTED_MODULE_2__.radians\n    ]);\n    if (p0) {\n        var normal = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(p0, p), equatorial = [\n            normal[1],\n            -normal[0],\n            0\n        ], inflection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(equatorial, normal);\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianNormalizeInPlace)(inflection);\n        inflection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(inflection);\n        var delta = lambda - lambda2, sign = delta > 0 ? 1 : -1, lambdai = inflection[0] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees * sign, phii, antimeridian = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(delta) > 180;\n        if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n            phii = inflection[1] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees;\n            if (phii > phi1) phi1 = phii;\n        } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n            phii = -inflection[1] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees;\n            if (phii < phi0) phi0 = phii;\n        } else {\n            if (phi < phi0) phi0 = phi;\n            if (phi > phi1) phi1 = phi;\n        }\n        if (antimeridian) {\n            if (lambda < lambda2) {\n                if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n            } else {\n                if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n            }\n        } else {\n            if (lambda1 >= lambda0) {\n                if (lambda < lambda0) lambda0 = lambda;\n                if (lambda > lambda1) lambda1 = lambda;\n            } else {\n                if (lambda > lambda2) {\n                    if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n                } else {\n                    if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n                }\n            }\n        }\n    } else {\n        ranges.push(range = [\n            lambda0 = lambda,\n            lambda1 = lambda\n        ]);\n    }\n    if (phi < phi0) phi0 = phi;\n    if (phi > phi1) phi1 = phi;\n    p0 = p, lambda2 = lambda;\n}\nfunction boundsLineStart() {\n    boundsStream.point = linePoint;\n}\nfunction boundsLineEnd() {\n    range[0] = lambda0, range[1] = lambda1;\n    boundsStream.point = boundsPoint;\n    p0 = null;\n}\nfunction boundsRingPoint(lambda, phi) {\n    if (p0) {\n        var delta = lambda - lambda2;\n        deltaSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n    } else {\n        lambda00 = lambda, phi00 = phi;\n    }\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.point(lambda, phi);\n    linePoint(lambda, phi);\n}\nfunction boundsRingStart() {\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.lineStart();\n}\nfunction boundsRingEnd() {\n    boundsRingPoint(lambda00, phi00);\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.lineEnd();\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(deltaSum) > _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) lambda0 = -(lambda1 = 180);\n    range[0] = lambda0, range[1] = lambda1;\n    p0 = null;\n}\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n    return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\nfunction rangeCompare(a, b) {\n    return a[0] - b[0];\n}\nfunction rangeContains(range, x) {\n    return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(feature) {\n    var i, n, a, b, merged, deltaMax, delta;\n    phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n    ranges = [];\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(feature, boundsStream);\n    // First, sort ranges by their minimum longitudes.\n    if (n = ranges.length) {\n        ranges.sort(rangeCompare);\n        // Then, merge any ranges that overlap.\n        for(i = 1, a = ranges[0], merged = [\n            a\n        ]; i < n; ++i){\n            b = ranges[i];\n            if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n                if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n                if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n            } else {\n                merged.push(a = b);\n            }\n        }\n        // Finally, find the largest gap between the merged ranges.\n        // The final bounding box will be the inverse of this gap.\n        for(deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i){\n            b = merged[i];\n            if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n        }\n    }\n    ranges = range = null;\n    return lambda0 === Infinity || phi0 === Infinity ? [\n        [\n            NaN,\n            NaN\n        ],\n        [\n            NaN,\n            NaN\n        ]\n    ] : [\n        [\n            lambda0,\n            phi0\n        ],\n        [\n            lambda1,\n            phi1\n        ]\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/bounds.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/cartesian.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-geo/src/cartesian.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cartesian: () => (/* binding */ cartesian),\n/* harmony export */   cartesianAddInPlace: () => (/* binding */ cartesianAddInPlace),\n/* harmony export */   cartesianCross: () => (/* binding */ cartesianCross),\n/* harmony export */   cartesianDot: () => (/* binding */ cartesianDot),\n/* harmony export */   cartesianNormalizeInPlace: () => (/* binding */ cartesianNormalizeInPlace),\n/* harmony export */   cartesianScale: () => (/* binding */ cartesianScale),\n/* harmony export */   spherical: () => (/* binding */ spherical)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\nfunction spherical(cartesian) {\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(cartesian[1], cartesian[0]),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(cartesian[2])\n    ];\n}\nfunction cartesian(spherical) {\n    var lambda = spherical[0], phi = spherical[1], cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi);\n    return [\n        cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda),\n        cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi)\n    ];\n}\nfunction cartesianDot(a, b) {\n    return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nfunction cartesianCross(a, b) {\n    return [\n        a[1] * b[2] - a[2] * b[1],\n        a[2] * b[0] - a[0] * b[2],\n        a[0] * b[1] - a[1] * b[0]\n    ];\n}\n// TODO return a\nfunction cartesianAddInPlace(a, b) {\n    a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\nfunction cartesianScale(vector, k) {\n    return [\n        vector[0] * k,\n        vector[1] * k,\n        vector[2] * k\n    ];\n}\n// TODO return d\nfunction cartesianNormalizeInPlace(d) {\n    var l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n    d[0] /= l, d[1] /= l, d[2] /= l;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/cartesian.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/centroid.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-geo/src/centroid.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/../../node_modules/d3-geo/src/stream.js\");\n\n\n\n\nvar W0, W1, X0, Y0, Z0, X1, Y1, Z1, X2, Y2, Z2, lambda00, phi00, x0, y0, z0; // previous point\nvar centroidStream = {\n    sphere: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    point: centroidPoint,\n    lineStart: centroidLineStart,\n    lineEnd: centroidLineEnd,\n    polygonStart: function() {\n        centroidStream.lineStart = centroidRingStart;\n        centroidStream.lineEnd = centroidRingEnd;\n    },\n    polygonEnd: function() {\n        centroidStream.lineStart = centroidLineStart;\n        centroidStream.lineEnd = centroidLineEnd;\n    }\n};\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    centroidPointCartesian(cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda), cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda), (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi));\n}\nfunction centroidPointCartesian(x, y, z) {\n    ++W0;\n    X0 += (x - X0) / W0;\n    Y0 += (y - Y0) / W0;\n    Z0 += (z - Z0) / W0;\n}\nfunction centroidLineStart() {\n    centroidStream.point = centroidLinePointFirst;\n}\nfunction centroidLinePointFirst(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    x0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda);\n    y0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda);\n    z0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi);\n    centroidStream.point = centroidLinePoint;\n    centroidPointCartesian(x0, y0, z0);\n}\nfunction centroidLinePoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi), x = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda), y = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda), z = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), w = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n    W1 += w;\n    X1 += w * (x0 + (x0 = x));\n    Y1 += w * (y0 + (y0 = y));\n    Z1 += w * (z0 + (z0 = z));\n    centroidPointCartesian(x0, y0, z0);\n}\nfunction centroidLineEnd() {\n    centroidStream.point = centroidPoint;\n}\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n    centroidStream.point = centroidRingPointFirst;\n}\nfunction centroidRingEnd() {\n    centroidRingPoint(lambda00, phi00);\n    centroidStream.point = centroidPoint;\n}\nfunction centroidRingPointFirst(lambda, phi) {\n    lambda00 = lambda, phi00 = phi;\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    centroidStream.point = centroidRingPoint;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    x0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda);\n    y0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda);\n    z0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi);\n    centroidPointCartesian(x0, y0, z0);\n}\nfunction centroidRingPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi), x = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda), y = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda), z = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), cx = y0 * z - z0 * y, cy = z0 * x - x0 * z, cz = x0 * y - y0 * x, m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(cx, cy, cz), w = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(m), v = m && -w / m; // area weight multiplier\n    X2.add(v * cx);\n    Y2.add(v * cy);\n    Z2.add(v * cz);\n    W1 += w;\n    X1 += w * (x0 + (x0 = x));\n    Y1 += w * (y0 + (y0 = y));\n    Z1 += w * (z0 + (z0 = z));\n    centroidPointCartesian(x0, y0, z0);\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n    W0 = W1 = X0 = Y0 = Z0 = X1 = Y1 = Z1 = 0;\n    X2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    Y2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    Z2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, centroidStream);\n    var x = +X2, y = +Y2, z = +Z2, m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(x, y, z);\n    // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n    if (m < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2) {\n        x = X1, y = Y1, z = Z1;\n        // If the feature has zero length, fall back to arithmetic mean of point vectors.\n        if (W1 < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) x = X0, y = Y0, z = Z0;\n        m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(x, y, z);\n        // If the feature still has an undefined ccentroid, then return.\n        if (m < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2) return [\n            NaN,\n            NaN\n        ];\n    }\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)(y, x) * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(z / m) * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/centroid.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/circle.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-geo/src/circle.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circleStream: () => (/* binding */ circleStream),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/../../node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/../../node_modules/d3-geo/src/constant.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rotation.js */ \"(ssr)/../../node_modules/d3-geo/src/rotation.js\");\n\n\n\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nfunction circleStream(stream, radius, delta, direction, t0, t1) {\n    if (!delta) return;\n    var cosRadius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(radius), sinRadius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(radius), step = direction * delta;\n    if (t0 == null) {\n        t0 = radius + direction * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n        t1 = radius - step / 2;\n    } else {\n        t0 = circleRadius(cosRadius, t0);\n        t1 = circleRadius(cosRadius, t1);\n        if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n    }\n    for(var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step){\n        point = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.spherical)([\n            cosRadius,\n            -sinRadius * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(t),\n            -sinRadius * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(t)\n        ]);\n        stream.point(point[0], point[1]);\n    }\n}\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n    point = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.cartesian)(point), point[0] -= cosRadius;\n    (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.cartesianNormalizeInPlace)(point);\n    var radius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.acos)(-point[1]);\n    return ((-point[2] < 0 ? -radius : radius) + _math_js__WEBPACK_IMPORTED_MODULE_0__.tau - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) % _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var center = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])([\n        0,\n        0\n    ]), radius = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(90), precision = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(2), ring, rotate, stream = {\n        point: point\n    };\n    function point(x, y) {\n        ring.push(x = rotate(x, y));\n        x[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, x[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees;\n    }\n    function circle() {\n        var c = center.apply(this, arguments), r = radius.apply(this, arguments) * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, p = precision.apply(this, arguments) * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians;\n        ring = [];\n        rotate = (0,_rotation_js__WEBPACK_IMPORTED_MODULE_3__.rotateRadians)(-c[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, -c[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, 0).invert;\n        circleStream(stream, r, p, 1);\n        c = {\n            type: \"Polygon\",\n            coordinates: [\n                ring\n            ]\n        };\n        ring = rotate = null;\n        return c;\n    }\n    circle.center = function(_) {\n        return arguments.length ? (center = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])([\n            +_[0],\n            +_[1]\n        ]), circle) : center;\n    };\n    circle.radius = function(_) {\n        return arguments.length ? (radius = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(+_), circle) : radius;\n    };\n    circle.precision = function(_) {\n        return arguments.length ? (precision = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(+_), circle) : precision;\n    };\n    return circle;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY2lyY2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErRTtBQUMxQztBQUNvQztBQUM3QjtBQUU1Qyw4RUFBOEU7QUFDdkUsU0FBU1ksYUFBYUMsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxFQUFFLEVBQUVDLEVBQUU7SUFDbkUsSUFBSSxDQUFDSCxPQUFPO0lBQ1osSUFBSUksWUFBWWQsNkNBQUdBLENBQUNTLFNBQ2hCTSxZQUFZWCw2Q0FBR0EsQ0FBQ0ssU0FDaEJPLE9BQU9MLFlBQVlEO0lBQ3ZCLElBQUlFLE1BQU0sTUFBTTtRQUNkQSxLQUFLSCxTQUFTRSxZQUFZTix5Q0FBR0E7UUFDN0JRLEtBQUtKLFNBQVNPLE9BQU87SUFDdkIsT0FBTztRQUNMSixLQUFLSyxhQUFhSCxXQUFXRjtRQUM3QkMsS0FBS0ksYUFBYUgsV0FBV0Q7UUFDN0IsSUFBSUYsWUFBWSxJQUFJQyxLQUFLQyxLQUFLRCxLQUFLQyxJQUFJRCxNQUFNRCxZQUFZTix5Q0FBR0E7SUFDOUQ7SUFDQSxJQUFLLElBQUlhLE9BQU9DLElBQUlQLElBQUlELFlBQVksSUFBSVEsSUFBSU4sS0FBS00sSUFBSU4sSUFBSU0sS0FBS0gsS0FBTTtRQUNsRUUsUUFBUXJCLHdEQUFTQSxDQUFDO1lBQUNpQjtZQUFXLENBQUNDLFlBQVlmLDZDQUFHQSxDQUFDbUI7WUFBSSxDQUFDSixZQUFZWCw2Q0FBR0EsQ0FBQ2U7U0FBRztRQUN2RVgsT0FBT1UsS0FBSyxDQUFDQSxLQUFLLENBQUMsRUFBRSxFQUFFQSxLQUFLLENBQUMsRUFBRTtJQUNqQztBQUNGO0FBRUEsK0VBQStFO0FBQy9FLFNBQVNELGFBQWFILFNBQVMsRUFBRUksS0FBSztJQUNwQ0EsUUFBUXZCLHdEQUFTQSxDQUFDdUIsUUFBUUEsS0FBSyxDQUFDLEVBQUUsSUFBSUo7SUFDdENsQix3RUFBeUJBLENBQUNzQjtJQUMxQixJQUFJVCxTQUFTViw4Q0FBSUEsQ0FBQyxDQUFDbUIsS0FBSyxDQUFDLEVBQUU7SUFDM0IsT0FBTyxDQUFDLENBQUMsQ0FBQ0EsS0FBSyxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUNULFNBQVNBLE1BQUssSUFBS0oseUNBQUdBLEdBQUdILDZDQUFNLElBQUtHLHlDQUFHQTtBQUNuRTtBQUVBLDZCQUFlLHNDQUFXO0lBQ3hCLElBQUllLFNBQVN0Qix3REFBUUEsQ0FBQztRQUFDO1FBQUc7S0FBRSxHQUN4QlcsU0FBU1gsd0RBQVFBLENBQUMsS0FDbEJ1QixZQUFZdkIsd0RBQVFBLENBQUMsSUFDckJ3QixNQUNBQyxRQUNBZixTQUFTO1FBQUNVLE9BQU9BO0lBQUs7SUFFMUIsU0FBU0EsTUFBTU0sQ0FBQyxFQUFFQyxDQUFDO1FBQ2pCSCxLQUFLSSxJQUFJLENBQUNGLElBQUlELE9BQU9DLEdBQUdDO1FBQ3hCRCxDQUFDLENBQUMsRUFBRSxJQUFJdkIsNkNBQU9BLEVBQUV1QixDQUFDLENBQUMsRUFBRSxJQUFJdkIsNkNBQU9BO0lBQ2xDO0lBRUEsU0FBUzBCO1FBQ1AsSUFBSUMsSUFBSVIsT0FBT1MsS0FBSyxDQUFDLElBQUksRUFBRUMsWUFDdkJDLElBQUl0QixPQUFPb0IsS0FBSyxDQUFDLElBQUksRUFBRUMsYUFBYTNCLDZDQUFPQSxFQUMzQzZCLElBQUlYLFVBQVVRLEtBQUssQ0FBQyxJQUFJLEVBQUVDLGFBQWEzQiw2Q0FBT0E7UUFDbERtQixPQUFPLEVBQUU7UUFDVEMsU0FBU2pCLDJEQUFhQSxDQUFDLENBQUNzQixDQUFDLENBQUMsRUFBRSxHQUFHekIsNkNBQU9BLEVBQUUsQ0FBQ3lCLENBQUMsQ0FBQyxFQUFFLEdBQUd6Qiw2Q0FBT0EsRUFBRSxHQUFHOEIsTUFBTTtRQUNsRTFCLGFBQWFDLFFBQVF1QixHQUFHQyxHQUFHO1FBQzNCSixJQUFJO1lBQUNNLE1BQU07WUFBV0MsYUFBYTtnQkFBQ2I7YUFBSztRQUFBO1FBQ3pDQSxPQUFPQyxTQUFTO1FBQ2hCLE9BQU9LO0lBQ1Q7SUFFQUQsT0FBT1AsTUFBTSxHQUFHLFNBQVNnQixDQUFDO1FBQ3hCLE9BQU9OLFVBQVVPLE1BQU0sR0FBSWpCLENBQUFBLFNBQVMsT0FBT2dCLE1BQU0sYUFBYUEsSUFBSXRDLHdEQUFRQSxDQUFDO1lBQUMsQ0FBQ3NDLENBQUMsQ0FBQyxFQUFFO1lBQUUsQ0FBQ0EsQ0FBQyxDQUFDLEVBQUU7U0FBQyxHQUFHVCxNQUFLLElBQUtQO0lBQ3hHO0lBRUFPLE9BQU9sQixNQUFNLEdBQUcsU0FBUzJCLENBQUM7UUFDeEIsT0FBT04sVUFBVU8sTUFBTSxHQUFJNUIsQ0FBQUEsU0FBUyxPQUFPMkIsTUFBTSxhQUFhQSxJQUFJdEMsd0RBQVFBLENBQUMsQ0FBQ3NDLElBQUlULE1BQUssSUFBS2xCO0lBQzVGO0lBRUFrQixPQUFPTixTQUFTLEdBQUcsU0FBU2UsQ0FBQztRQUMzQixPQUFPTixVQUFVTyxNQUFNLEdBQUloQixDQUFBQSxZQUFZLE9BQU9lLE1BQU0sYUFBYUEsSUFBSXRDLHdEQUFRQSxDQUFDLENBQUNzQyxJQUFJVCxNQUFLLElBQUtOO0lBQy9GO0lBRUEsT0FBT007QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL2NpcmNsZS5qcz8zNjQxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y2FydGVzaWFuLCBjYXJ0ZXNpYW5Ob3JtYWxpemVJblBsYWNlLCBzcGhlcmljYWx9IGZyb20gXCIuL2NhcnRlc2lhbi5qc1wiO1xuaW1wb3J0IGNvbnN0YW50IGZyb20gXCIuL2NvbnN0YW50LmpzXCI7XG5pbXBvcnQge2Fjb3MsIGNvcywgZGVncmVlcywgZXBzaWxvbiwgcmFkaWFucywgc2luLCB0YXV9IGZyb20gXCIuL21hdGguanNcIjtcbmltcG9ydCB7cm90YXRlUmFkaWFuc30gZnJvbSBcIi4vcm90YXRpb24uanNcIjtcblxuLy8gR2VuZXJhdGVzIGEgY2lyY2xlIGNlbnRlcmVkIGF0IFswwrAsIDDCsF0sIHdpdGggYSBnaXZlbiByYWRpdXMgYW5kIHByZWNpc2lvbi5cbmV4cG9ydCBmdW5jdGlvbiBjaXJjbGVTdHJlYW0oc3RyZWFtLCByYWRpdXMsIGRlbHRhLCBkaXJlY3Rpb24sIHQwLCB0MSkge1xuICBpZiAoIWRlbHRhKSByZXR1cm47XG4gIHZhciBjb3NSYWRpdXMgPSBjb3MocmFkaXVzKSxcbiAgICAgIHNpblJhZGl1cyA9IHNpbihyYWRpdXMpLFxuICAgICAgc3RlcCA9IGRpcmVjdGlvbiAqIGRlbHRhO1xuICBpZiAodDAgPT0gbnVsbCkge1xuICAgIHQwID0gcmFkaXVzICsgZGlyZWN0aW9uICogdGF1O1xuICAgIHQxID0gcmFkaXVzIC0gc3RlcCAvIDI7XG4gIH0gZWxzZSB7XG4gICAgdDAgPSBjaXJjbGVSYWRpdXMoY29zUmFkaXVzLCB0MCk7XG4gICAgdDEgPSBjaXJjbGVSYWRpdXMoY29zUmFkaXVzLCB0MSk7XG4gICAgaWYgKGRpcmVjdGlvbiA+IDAgPyB0MCA8IHQxIDogdDAgPiB0MSkgdDAgKz0gZGlyZWN0aW9uICogdGF1O1xuICB9XG4gIGZvciAodmFyIHBvaW50LCB0ID0gdDA7IGRpcmVjdGlvbiA+IDAgPyB0ID4gdDEgOiB0IDwgdDE7IHQgLT0gc3RlcCkge1xuICAgIHBvaW50ID0gc3BoZXJpY2FsKFtjb3NSYWRpdXMsIC1zaW5SYWRpdXMgKiBjb3ModCksIC1zaW5SYWRpdXMgKiBzaW4odCldKTtcbiAgICBzdHJlYW0ucG9pbnQocG9pbnRbMF0sIHBvaW50WzFdKTtcbiAgfVxufVxuXG4vLyBSZXR1cm5zIHRoZSBzaWduZWQgYW5nbGUgb2YgYSBjYXJ0ZXNpYW4gcG9pbnQgcmVsYXRpdmUgdG8gW2Nvc1JhZGl1cywgMCwgMF0uXG5mdW5jdGlvbiBjaXJjbGVSYWRpdXMoY29zUmFkaXVzLCBwb2ludCkge1xuICBwb2ludCA9IGNhcnRlc2lhbihwb2ludCksIHBvaW50WzBdIC09IGNvc1JhZGl1cztcbiAgY2FydGVzaWFuTm9ybWFsaXplSW5QbGFjZShwb2ludCk7XG4gIHZhciByYWRpdXMgPSBhY29zKC1wb2ludFsxXSk7XG4gIHJldHVybiAoKC1wb2ludFsyXSA8IDAgPyAtcmFkaXVzIDogcmFkaXVzKSArIHRhdSAtIGVwc2lsb24pICUgdGF1O1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgdmFyIGNlbnRlciA9IGNvbnN0YW50KFswLCAwXSksXG4gICAgICByYWRpdXMgPSBjb25zdGFudCg5MCksXG4gICAgICBwcmVjaXNpb24gPSBjb25zdGFudCgyKSxcbiAgICAgIHJpbmcsXG4gICAgICByb3RhdGUsXG4gICAgICBzdHJlYW0gPSB7cG9pbnQ6IHBvaW50fTtcblxuICBmdW5jdGlvbiBwb2ludCh4LCB5KSB7XG4gICAgcmluZy5wdXNoKHggPSByb3RhdGUoeCwgeSkpO1xuICAgIHhbMF0gKj0gZGVncmVlcywgeFsxXSAqPSBkZWdyZWVzO1xuICB9XG5cbiAgZnVuY3Rpb24gY2lyY2xlKCkge1xuICAgIHZhciBjID0gY2VudGVyLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyksXG4gICAgICAgIHIgPSByYWRpdXMuYXBwbHkodGhpcywgYXJndW1lbnRzKSAqIHJhZGlhbnMsXG4gICAgICAgIHAgPSBwcmVjaXNpb24uYXBwbHkodGhpcywgYXJndW1lbnRzKSAqIHJhZGlhbnM7XG4gICAgcmluZyA9IFtdO1xuICAgIHJvdGF0ZSA9IHJvdGF0ZVJhZGlhbnMoLWNbMF0gKiByYWRpYW5zLCAtY1sxXSAqIHJhZGlhbnMsIDApLmludmVydDtcbiAgICBjaXJjbGVTdHJlYW0oc3RyZWFtLCByLCBwLCAxKTtcbiAgICBjID0ge3R5cGU6IFwiUG9seWdvblwiLCBjb29yZGluYXRlczogW3JpbmddfTtcbiAgICByaW5nID0gcm90YXRlID0gbnVsbDtcbiAgICByZXR1cm4gYztcbiAgfVxuXG4gIGNpcmNsZS5jZW50ZXIgPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAoY2VudGVyID0gdHlwZW9mIF8gPT09IFwiZnVuY3Rpb25cIiA/IF8gOiBjb25zdGFudChbK19bMF0sICtfWzFdXSksIGNpcmNsZSkgOiBjZW50ZXI7XG4gIH07XG5cbiAgY2lyY2xlLnJhZGl1cyA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChyYWRpdXMgPSB0eXBlb2YgXyA9PT0gXCJmdW5jdGlvblwiID8gXyA6IGNvbnN0YW50KCtfKSwgY2lyY2xlKSA6IHJhZGl1cztcbiAgfTtcblxuICBjaXJjbGUucHJlY2lzaW9uID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHByZWNpc2lvbiA9IHR5cGVvZiBfID09PSBcImZ1bmN0aW9uXCIgPyBfIDogY29uc3RhbnQoK18pLCBjaXJjbGUpIDogcHJlY2lzaW9uO1xuICB9O1xuXG4gIHJldHVybiBjaXJjbGU7XG59XG4iXSwibmFtZXMiOlsiY2FydGVzaWFuIiwiY2FydGVzaWFuTm9ybWFsaXplSW5QbGFjZSIsInNwaGVyaWNhbCIsImNvbnN0YW50IiwiYWNvcyIsImNvcyIsImRlZ3JlZXMiLCJlcHNpbG9uIiwicmFkaWFucyIsInNpbiIsInRhdSIsInJvdGF0ZVJhZGlhbnMiLCJjaXJjbGVTdHJlYW0iLCJzdHJlYW0iLCJyYWRpdXMiLCJkZWx0YSIsImRpcmVjdGlvbiIsInQwIiwidDEiLCJjb3NSYWRpdXMiLCJzaW5SYWRpdXMiLCJzdGVwIiwiY2lyY2xlUmFkaXVzIiwicG9pbnQiLCJ0IiwiY2VudGVyIiwicHJlY2lzaW9uIiwicmluZyIsInJvdGF0ZSIsIngiLCJ5IiwicHVzaCIsImNpcmNsZSIsImMiLCJhcHBseSIsImFyZ3VtZW50cyIsInIiLCJwIiwiaW52ZXJ0IiwidHlwZSIsImNvb3JkaW5hdGVzIiwiXyIsImxlbmd0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/circle.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/antimeridian.js":
/*!**********************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/antimeridian.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function() {\n    return true;\n}, clipAntimeridianLine, clipAntimeridianInterpolate, [\n    -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi,\n    -_math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi\n]));\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n    var lambda0 = NaN, phi0 = NaN, sign0 = NaN, clean; // no intersections\n    return {\n        lineStart: function() {\n            stream.lineStart();\n            clean = 1;\n        },\n        point: function(lambda1, phi1) {\n            var sign1 = lambda1 > 0 ? _math_js__WEBPACK_IMPORTED_MODULE_1__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, delta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda1 - lambda0);\n            if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(delta - _math_js__WEBPACK_IMPORTED_MODULE_1__.pi) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) {\n                stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi);\n                stream.point(sign0, phi0);\n                stream.lineEnd();\n                stream.lineStart();\n                stream.point(sign1, phi0);\n                stream.point(lambda1, phi0);\n                clean = 0;\n            } else if (sign0 !== sign1 && delta >= _math_js__WEBPACK_IMPORTED_MODULE_1__.pi) {\n                if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda0 - sign0) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) lambda0 -= sign0 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon; // handle degeneracies\n                if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda1 - sign1) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) lambda1 -= sign1 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n                phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n                stream.point(sign0, phi0);\n                stream.lineEnd();\n                stream.lineStart();\n                stream.point(sign1, phi0);\n                clean = 0;\n            }\n            stream.point(lambda0 = lambda1, phi0 = phi1);\n            sign0 = sign1;\n        },\n        lineEnd: function() {\n            stream.lineEnd();\n            lambda0 = phi0 = NaN;\n        },\n        clean: function() {\n            return 2 - clean; // if intersections, rejoin first and last segments\n        }\n    };\n}\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n    var cosPhi0, cosPhi1, sinLambda0Lambda1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda0 - lambda1);\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(sinLambda0Lambda1) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon ? (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan)(((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi0) * (cosPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi1)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda1) - (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi1) * (cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi0)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda0)) / (cosPhi0 * cosPhi1 * sinLambda0Lambda1)) : (phi0 + phi1) / 2;\n}\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n    var phi;\n    if (from == null) {\n        phi = direction * _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi;\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n        stream.point(0, phi);\n        stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n        stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, 0);\n        stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, -phi);\n        stream.point(0, -phi);\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, -phi);\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, 0);\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n    } else if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(from[0] - to[0]) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) {\n        var lambda = from[0] < to[0] ? _math_js__WEBPACK_IMPORTED_MODULE_1__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi;\n        phi = direction * lambda / 2;\n        stream.point(-lambda, phi);\n        stream.point(0, phi);\n        stream.point(lambda, phi);\n    } else {\n        stream.point(to[0], to[1]);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/antimeridian.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/buffer.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/buffer.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var lines = [], line;\n    return {\n        point: function(x, y, m) {\n            line.push([\n                x,\n                y,\n                m\n            ]);\n        },\n        lineStart: function() {\n            lines.push(line = []);\n        },\n        lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        rejoin: function() {\n            if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n        },\n        result: function() {\n            var result = lines;\n            lines = [];\n            line = null;\n            return result;\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY2xpcC9idWZmZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEI7QUFFOUIsNkJBQWUsc0NBQVc7SUFDeEIsSUFBSUMsUUFBUSxFQUFFLEVBQ1ZDO0lBQ0osT0FBTztRQUNMQyxPQUFPLFNBQVNDLENBQUMsRUFBRUMsQ0FBQyxFQUFFQyxDQUFDO1lBQ3JCSixLQUFLSyxJQUFJLENBQUM7Z0JBQUNIO2dCQUFHQztnQkFBR0M7YUFBRTtRQUNyQjtRQUNBRSxXQUFXO1lBQ1RQLE1BQU1NLElBQUksQ0FBQ0wsT0FBTyxFQUFFO1FBQ3RCO1FBQ0FPLFNBQVNULGdEQUFJQTtRQUNiVSxRQUFRO1lBQ04sSUFBSVQsTUFBTVUsTUFBTSxHQUFHLEdBQUdWLE1BQU1NLElBQUksQ0FBQ04sTUFBTVcsR0FBRyxHQUFHQyxNQUFNLENBQUNaLE1BQU1hLEtBQUs7UUFDakU7UUFDQUMsUUFBUTtZQUNOLElBQUlBLFNBQVNkO1lBQ2JBLFFBQVEsRUFBRTtZQUNWQyxPQUFPO1lBQ1AsT0FBT2E7UUFDVDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jbGlwL2J1ZmZlci5qcz9jMDM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBub29wIGZyb20gXCIuLi9ub29wLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICB2YXIgbGluZXMgPSBbXSxcbiAgICAgIGxpbmU7XG4gIHJldHVybiB7XG4gICAgcG9pbnQ6IGZ1bmN0aW9uKHgsIHksIG0pIHtcbiAgICAgIGxpbmUucHVzaChbeCwgeSwgbV0pO1xuICAgIH0sXG4gICAgbGluZVN0YXJ0OiBmdW5jdGlvbigpIHtcbiAgICAgIGxpbmVzLnB1c2gobGluZSA9IFtdKTtcbiAgICB9LFxuICAgIGxpbmVFbmQ6IG5vb3AsXG4gICAgcmVqb2luOiBmdW5jdGlvbigpIHtcbiAgICAgIGlmIChsaW5lcy5sZW5ndGggPiAxKSBsaW5lcy5wdXNoKGxpbmVzLnBvcCgpLmNvbmNhdChsaW5lcy5zaGlmdCgpKSk7XG4gICAgfSxcbiAgICByZXN1bHQ6IGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIHJlc3VsdCA9IGxpbmVzO1xuICAgICAgbGluZXMgPSBbXTtcbiAgICAgIGxpbmUgPSBudWxsO1xuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gIH07XG59XG4iXSwibmFtZXMiOlsibm9vcCIsImxpbmVzIiwibGluZSIsInBvaW50IiwieCIsInkiLCJtIiwicHVzaCIsImxpbmVTdGFydCIsImxpbmVFbmQiLCJyZWpvaW4iLCJsZW5ndGgiLCJwb3AiLCJjb25jYXQiLCJzaGlmdCIsInJlc3VsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/buffer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/circle.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/circle.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cartesian.js */ \"(ssr)/../../node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _circle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../circle.js */ \"(ssr)/../../node_modules/d3-geo/src/circle.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _pointEqual_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pointEqual.js */ \"(ssr)/../../node_modules/d3-geo/src/pointEqual.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/index.js\");\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(radius) {\n    var cr = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(radius), delta = 2 * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, smallRadius = cr > 0, notHemisphere = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(cr) > _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon; // TODO optimise for this common case\n    function interpolate(from, to, direction, stream) {\n        (0,_circle_js__WEBPACK_IMPORTED_MODULE_1__.circleStream)(stream, radius, delta, direction, from, to);\n    }\n    function visible(lambda, phi) {\n        return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi) > cr;\n    }\n    // Takes a line and cuts into visible segments. Return values used for polygon\n    // clipping: 0 - there were intersections or the line was empty; 1 - no\n    // intersections 2 - there were intersections, and the first and last segments\n    // should be rejoined.\n    function clipLine(stream) {\n        var point0, c0, v0, v00, clean; // no intersections\n        return {\n            lineStart: function() {\n                v00 = v0 = false;\n                clean = 1;\n            },\n            point: function(lambda, phi) {\n                var point1 = [\n                    lambda,\n                    phi\n                ], point2, v = visible(lambda, phi), c = smallRadius ? v ? 0 : code(lambda, phi) : v ? code(lambda + (lambda < 0 ? _math_js__WEBPACK_IMPORTED_MODULE_0__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_0__.pi), phi) : 0;\n                if (!point0 && (v00 = v0 = v)) stream.lineStart();\n                if (v !== v0) {\n                    point2 = intersect(point0, point1);\n                    if (!point2 || (0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point0, point2) || (0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point1, point2)) point1[2] = 1;\n                }\n                if (v !== v0) {\n                    clean = 0;\n                    if (v) {\n                        // outside going in\n                        stream.lineStart();\n                        point2 = intersect(point1, point0);\n                        stream.point(point2[0], point2[1]);\n                    } else {\n                        // inside going out\n                        point2 = intersect(point0, point1);\n                        stream.point(point2[0], point2[1], 2);\n                        stream.lineEnd();\n                    }\n                    point0 = point2;\n                } else if (notHemisphere && point0 && smallRadius ^ v) {\n                    var t;\n                    // If the codes for two points are different, or are both zero,\n                    // and there this segment intersects with the small circle.\n                    if (!(c & c0) && (t = intersect(point1, point0, true))) {\n                        clean = 0;\n                        if (smallRadius) {\n                            stream.lineStart();\n                            stream.point(t[0][0], t[0][1]);\n                            stream.point(t[1][0], t[1][1]);\n                            stream.lineEnd();\n                        } else {\n                            stream.point(t[1][0], t[1][1]);\n                            stream.lineEnd();\n                            stream.lineStart();\n                            stream.point(t[0][0], t[0][1], 3);\n                        }\n                    }\n                }\n                if (v && (!point0 || !(0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point0, point1))) {\n                    stream.point(point1[0], point1[1]);\n                }\n                point0 = point1, v0 = v, c0 = c;\n            },\n            lineEnd: function() {\n                if (v0) stream.lineEnd();\n                point0 = null;\n            },\n            // Rejoin first and last segments if there were intersections and the first\n            // and last points were visible.\n            clean: function() {\n                return clean | (v00 && v0) << 1;\n            }\n        };\n    }\n    // Intersects the great circle between a and b with the clip circle.\n    function intersect(a, b, two) {\n        var pa = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)(a), pb = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)(b);\n        // We have two planes, n1.p = d1 and n2.p = d2.\n        // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n        var n1 = [\n            1,\n            0,\n            0\n        ], n2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(pa, pb), n2n2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(n2, n2), n1n2 = n2[0], determinant = n2n2 - n1n2 * n1n2;\n        // Two polar points.\n        if (!determinant) return !two && a;\n        var c1 = cr * n2n2 / determinant, c2 = -cr * n1n2 / determinant, n1xn2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(n1, n2), A = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(n1, c1), B = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(n2, c2);\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(A, B);\n        // Solve |p(t)|^2 = 1.\n        var u = n1xn2, w = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(A, u), uu = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(u, u), t2 = w * w - uu * ((0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(A, A) - 1);\n        if (t2 < 0) return;\n        var t = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(t2), q = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(u, (-w - t) / uu);\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(q, A);\n        q = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(q);\n        if (!two) return q;\n        // Two intersection points.\n        var lambda0 = a[0], lambda1 = b[0], phi0 = a[1], phi1 = b[1], z;\n        if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n        var delta = lambda1 - lambda0, polar = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon, meridian = polar || delta < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n        if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n        // Check that the first point is between a and b.\n        if (meridian ? polar ? phi0 + phi1 > 0 ^ q[1] < ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(q[0] - lambda0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? phi0 : phi1) : phi0 <= q[1] && q[1] <= phi1 : delta > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n            var q1 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(u, (-w + t) / uu);\n            (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(q1, A);\n            return [\n                q,\n                (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(q1)\n            ];\n        }\n    }\n    // Generates a 4-bit vector representing the location of a point relative to\n    // the small circle's bounding box.\n    function code(lambda, phi) {\n        var r = smallRadius ? radius : _math_js__WEBPACK_IMPORTED_MODULE_0__.pi - radius, code = 0;\n        if (lambda < -r) code |= 1; // left\n        else if (lambda > r) code |= 2; // right\n        if (phi < -r) code |= 4; // below\n        else if (phi > r) code |= 8; // above\n        return code;\n    }\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(visible, clipLine, interpolate, smallRadius ? [\n        0,\n        -radius\n    ] : [\n        -_math_js__WEBPACK_IMPORTED_MODULE_0__.pi,\n        radius - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/circle.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/extent.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/extent.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rectangle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rectangle.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/rectangle.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var x0 = 0, y0 = 0, x1 = 960, y1 = 500, cache, cacheStream, clip;\n    return clip = {\n        stream: function(stream) {\n            return cache && cacheStream === stream ? cache : cache = (0,_rectangle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(x0, y0, x1, y1)(cacheStream = stream);\n        },\n        extent: function(_) {\n            return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [\n                [\n                    x0,\n                    y0\n                ],\n                [\n                    x1,\n                    y1\n                ]\n            ];\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY2xpcC9leHRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkM7QUFFM0MsNkJBQWUsc0NBQVc7SUFDeEIsSUFBSUMsS0FBSyxHQUNMQyxLQUFLLEdBQ0xDLEtBQUssS0FDTEMsS0FBSyxLQUNMQyxPQUNBQyxhQUNBQztJQUVKLE9BQU9BLE9BQU87UUFDWkMsUUFBUSxTQUFTQSxNQUFNO1lBQ3JCLE9BQU9ILFNBQVNDLGdCQUFnQkUsU0FBU0gsUUFBUUEsUUFBUUwseURBQWFBLENBQUNDLElBQUlDLElBQUlDLElBQUlDLElBQUlFLGNBQWNFO1FBQ3ZHO1FBQ0FDLFFBQVEsU0FBU0MsQ0FBQztZQUNoQixPQUFPQyxVQUFVQyxNQUFNLEdBQUlYLENBQUFBLEtBQUssQ0FBQ1MsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUVSLEtBQUssQ0FBQ1EsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUVQLEtBQUssQ0FBQ08sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUVOLEtBQUssQ0FBQ00sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUVMLFFBQVFDLGNBQWMsTUFBTUMsSUFBRyxJQUFLO2dCQUFDO29CQUFDTjtvQkFBSUM7aUJBQUc7Z0JBQUU7b0JBQUNDO29CQUFJQztpQkFBRzthQUFDO1FBQ2pKO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL2NsaXAvZXh0ZW50LmpzPzE4NDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNsaXBSZWN0YW5nbGUgZnJvbSBcIi4vcmVjdGFuZ2xlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICB2YXIgeDAgPSAwLFxuICAgICAgeTAgPSAwLFxuICAgICAgeDEgPSA5NjAsXG4gICAgICB5MSA9IDUwMCxcbiAgICAgIGNhY2hlLFxuICAgICAgY2FjaGVTdHJlYW0sXG4gICAgICBjbGlwO1xuXG4gIHJldHVybiBjbGlwID0ge1xuICAgIHN0cmVhbTogZnVuY3Rpb24oc3RyZWFtKSB7XG4gICAgICByZXR1cm4gY2FjaGUgJiYgY2FjaGVTdHJlYW0gPT09IHN0cmVhbSA/IGNhY2hlIDogY2FjaGUgPSBjbGlwUmVjdGFuZ2xlKHgwLCB5MCwgeDEsIHkxKShjYWNoZVN0cmVhbSA9IHN0cmVhbSk7XG4gICAgfSxcbiAgICBleHRlbnQ6IGZ1bmN0aW9uKF8pIHtcbiAgICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHgwID0gK19bMF1bMF0sIHkwID0gK19bMF1bMV0sIHgxID0gK19bMV1bMF0sIHkxID0gK19bMV1bMV0sIGNhY2hlID0gY2FjaGVTdHJlYW0gPSBudWxsLCBjbGlwKSA6IFtbeDAsIHkwXSwgW3gxLCB5MV1dO1xuICAgIH1cbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJjbGlwUmVjdGFuZ2xlIiwieDAiLCJ5MCIsIngxIiwieTEiLCJjYWNoZSIsImNhY2hlU3RyZWFtIiwiY2xpcCIsInN0cmVhbSIsImV4dGVudCIsIl8iLCJhcmd1bWVudHMiLCJsZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/extent.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/index.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/buffer.js\");\n/* harmony import */ var _rejoin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rejoin.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/rejoin.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _polygonContains_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../polygonContains.js */ \"(ssr)/../../node_modules/d3-geo/src/polygonContains.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/merge.js\");\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(pointVisible, clipLine, interpolate, start) {\n    return function(sink) {\n        var line = clipLine(sink), ringBuffer = (0,_buffer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), ringSink = clipLine(ringBuffer), polygonStarted = false, polygon, segments, ring;\n        var clip = {\n            point: point,\n            lineStart: lineStart,\n            lineEnd: lineEnd,\n            polygonStart: function() {\n                clip.point = pointRing;\n                clip.lineStart = ringStart;\n                clip.lineEnd = ringEnd;\n                segments = [];\n                polygon = [];\n            },\n            polygonEnd: function() {\n                clip.point = point;\n                clip.lineStart = lineStart;\n                clip.lineEnd = lineEnd;\n                segments = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(segments);\n                var startInside = (0,_polygonContains_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(polygon, start);\n                if (segments.length) {\n                    if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n                    (0,_rejoin_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(segments, compareIntersection, startInside, interpolate, sink);\n                } else if (startInside) {\n                    if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n                    sink.lineStart();\n                    interpolate(null, null, 1, sink);\n                    sink.lineEnd();\n                }\n                if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n                segments = polygon = null;\n            },\n            sphere: function() {\n                sink.polygonStart();\n                sink.lineStart();\n                interpolate(null, null, 1, sink);\n                sink.lineEnd();\n                sink.polygonEnd();\n            }\n        };\n        function point(lambda, phi) {\n            if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n        }\n        function pointLine(lambda, phi) {\n            line.point(lambda, phi);\n        }\n        function lineStart() {\n            clip.point = pointLine;\n            line.lineStart();\n        }\n        function lineEnd() {\n            clip.point = point;\n            line.lineEnd();\n        }\n        function pointRing(lambda, phi) {\n            ring.push([\n                lambda,\n                phi\n            ]);\n            ringSink.point(lambda, phi);\n        }\n        function ringStart() {\n            ringSink.lineStart();\n            ring = [];\n        }\n        function ringEnd() {\n            pointRing(ring[0][0], ring[0][1]);\n            ringSink.lineEnd();\n            var clean = ringSink.clean(), ringSegments = ringBuffer.result(), i, n = ringSegments.length, m, segment, point;\n            ring.pop();\n            polygon.push(ring);\n            ring = null;\n            if (!n) return;\n            // No intersections.\n            if (clean & 1) {\n                segment = ringSegments[0];\n                if ((m = segment.length - 1) > 0) {\n                    if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n                    sink.lineStart();\n                    for(i = 0; i < m; ++i)sink.point((point = segment[i])[0], point[1]);\n                    sink.lineEnd();\n                }\n                return;\n            }\n            // Rejoin connected segments.\n            // TODO reuse ringBuffer.rejoin()?\n            if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n            segments.push(ringSegments.filter(validSegment));\n        }\n        return clip;\n    };\n}\nfunction validSegment(segment) {\n    return segment.length > 1;\n}\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n    return ((a = a.x)[0] < 0 ? a[1] - _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_4__.epsilon : _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - a[1]) - ((b = b.x)[0] < 0 ? b[1] - _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_4__.epsilon : _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - b[1]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY2xpcC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUM7QUFDQTtBQUNNO0FBQ1M7QUFDckI7QUFFL0IsNkJBQWUsb0NBQVNNLFlBQVksRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUVDLEtBQUs7SUFDaEUsT0FBTyxTQUFTQyxJQUFJO1FBQ2xCLElBQUlDLE9BQU9KLFNBQVNHLE9BQ2hCRSxhQUFhWixzREFBVUEsSUFDdkJhLFdBQVdOLFNBQVNLLGFBQ3BCRSxpQkFBaUIsT0FDakJDLFNBQ0FDLFVBQ0FDO1FBRUosSUFBSUMsT0FBTztZQUNUQyxPQUFPQTtZQUNQQyxXQUFXQTtZQUNYQyxTQUFTQTtZQUNUQyxjQUFjO2dCQUNaSixLQUFLQyxLQUFLLEdBQUdJO2dCQUNiTCxLQUFLRSxTQUFTLEdBQUdJO2dCQUNqQk4sS0FBS0csT0FBTyxHQUFHSTtnQkFDZlQsV0FBVyxFQUFFO2dCQUNiRCxVQUFVLEVBQUU7WUFDZDtZQUNBVyxZQUFZO2dCQUNWUixLQUFLQyxLQUFLLEdBQUdBO2dCQUNiRCxLQUFLRSxTQUFTLEdBQUdBO2dCQUNqQkYsS0FBS0csT0FBTyxHQUFHQTtnQkFDZkwsV0FBV1gsb0RBQUtBLENBQUNXO2dCQUNqQixJQUFJVyxjQUFjdkIsK0RBQWVBLENBQUNXLFNBQVNOO2dCQUMzQyxJQUFJTyxTQUFTWSxNQUFNLEVBQUU7b0JBQ25CLElBQUksQ0FBQ2QsZ0JBQWdCSixLQUFLWSxZQUFZLElBQUlSLGlCQUFpQjtvQkFDM0RiLHNEQUFVQSxDQUFDZSxVQUFVYSxxQkFBcUJGLGFBQWFuQixhQUFhRTtnQkFDdEUsT0FBTyxJQUFJaUIsYUFBYTtvQkFDdEIsSUFBSSxDQUFDYixnQkFBZ0JKLEtBQUtZLFlBQVksSUFBSVIsaUJBQWlCO29CQUMzREosS0FBS1UsU0FBUztvQkFDZFosWUFBWSxNQUFNLE1BQU0sR0FBR0U7b0JBQzNCQSxLQUFLVyxPQUFPO2dCQUNkO2dCQUNBLElBQUlQLGdCQUFnQkosS0FBS2dCLFVBQVUsSUFBSVosaUJBQWlCO2dCQUN4REUsV0FBV0QsVUFBVTtZQUN2QjtZQUNBZSxRQUFRO2dCQUNOcEIsS0FBS1ksWUFBWTtnQkFDakJaLEtBQUtVLFNBQVM7Z0JBQ2RaLFlBQVksTUFBTSxNQUFNLEdBQUdFO2dCQUMzQkEsS0FBS1csT0FBTztnQkFDWlgsS0FBS2dCLFVBQVU7WUFDakI7UUFDRjtRQUVBLFNBQVNQLE1BQU1ZLE1BQU0sRUFBRUMsR0FBRztZQUN4QixJQUFJMUIsYUFBYXlCLFFBQVFDLE1BQU10QixLQUFLUyxLQUFLLENBQUNZLFFBQVFDO1FBQ3BEO1FBRUEsU0FBU0MsVUFBVUYsTUFBTSxFQUFFQyxHQUFHO1lBQzVCckIsS0FBS1EsS0FBSyxDQUFDWSxRQUFRQztRQUNyQjtRQUVBLFNBQVNaO1lBQ1BGLEtBQUtDLEtBQUssR0FBR2M7WUFDYnRCLEtBQUtTLFNBQVM7UUFDaEI7UUFFQSxTQUFTQztZQUNQSCxLQUFLQyxLQUFLLEdBQUdBO1lBQ2JSLEtBQUtVLE9BQU87UUFDZDtRQUVBLFNBQVNFLFVBQVVRLE1BQU0sRUFBRUMsR0FBRztZQUM1QmYsS0FBS2lCLElBQUksQ0FBQztnQkFBQ0g7Z0JBQVFDO2FBQUk7WUFDdkJuQixTQUFTTSxLQUFLLENBQUNZLFFBQVFDO1FBQ3pCO1FBRUEsU0FBU1I7WUFDUFgsU0FBU08sU0FBUztZQUNsQkgsT0FBTyxFQUFFO1FBQ1g7UUFFQSxTQUFTUTtZQUNQRixVQUFVTixJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRUEsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFO1lBQ2hDSixTQUFTUSxPQUFPO1lBRWhCLElBQUljLFFBQVF0QixTQUFTc0IsS0FBSyxJQUN0QkMsZUFBZXhCLFdBQVd5QixNQUFNLElBQ2hDQyxHQUFHQyxJQUFJSCxhQUFhUixNQUFNLEVBQUVZLEdBQzVCQyxTQUNBdEI7WUFFSkYsS0FBS3lCLEdBQUc7WUFDUjNCLFFBQVFtQixJQUFJLENBQUNqQjtZQUNiQSxPQUFPO1lBRVAsSUFBSSxDQUFDc0IsR0FBRztZQUVSLG9CQUFvQjtZQUNwQixJQUFJSixRQUFRLEdBQUc7Z0JBQ2JNLFVBQVVMLFlBQVksQ0FBQyxFQUFFO2dCQUN6QixJQUFJLENBQUNJLElBQUlDLFFBQVFiLE1BQU0sR0FBRyxLQUFLLEdBQUc7b0JBQ2hDLElBQUksQ0FBQ2QsZ0JBQWdCSixLQUFLWSxZQUFZLElBQUlSLGlCQUFpQjtvQkFDM0RKLEtBQUtVLFNBQVM7b0JBQ2QsSUFBS2tCLElBQUksR0FBR0EsSUFBSUUsR0FBRyxFQUFFRixFQUFHNUIsS0FBS1MsS0FBSyxDQUFDLENBQUNBLFFBQVFzQixPQUFPLENBQUNILEVBQUUsQ0FBQyxDQUFDLEVBQUUsRUFBRW5CLEtBQUssQ0FBQyxFQUFFO29CQUNwRVQsS0FBS1csT0FBTztnQkFDZDtnQkFDQTtZQUNGO1lBRUEsNkJBQTZCO1lBQzdCLGtDQUFrQztZQUNsQyxJQUFJa0IsSUFBSSxLQUFLSixRQUFRLEdBQUdDLGFBQWFGLElBQUksQ0FBQ0UsYUFBYU0sR0FBRyxHQUFHQyxNQUFNLENBQUNQLGFBQWFRLEtBQUs7WUFFdEY1QixTQUFTa0IsSUFBSSxDQUFDRSxhQUFhUyxNQUFNLENBQUNDO1FBQ3BDO1FBRUEsT0FBTzVCO0lBQ1Q7QUFDRjtBQUVBLFNBQVM0QixhQUFhTCxPQUFPO0lBQzNCLE9BQU9BLFFBQVFiLE1BQU0sR0FBRztBQUMxQjtBQUVBLDhFQUE4RTtBQUM5RSxvREFBb0Q7QUFDcEQsU0FBU0Msb0JBQW9Ca0IsQ0FBQyxFQUFFQyxDQUFDO0lBQy9CLE9BQU8sQ0FBQyxDQUFDRCxJQUFJQSxFQUFFRSxDQUFDLENBQUMsQ0FBQyxFQUFFLEdBQUcsSUFBSUYsQ0FBQyxDQUFDLEVBQUUsR0FBRzVDLDRDQUFNQSxHQUFHRCw2Q0FBT0EsR0FBR0MsNENBQU1BLEdBQUc0QyxDQUFDLENBQUMsRUFBRSxJQUMxRCxFQUFDQyxJQUFJQSxFQUFFQyxDQUFDLENBQUMsQ0FBQyxFQUFFLEdBQUcsSUFBSUQsQ0FBQyxDQUFDLEVBQUUsR0FBRzdDLDRDQUFNQSxHQUFHRCw2Q0FBT0EsR0FBR0MsNENBQU1BLEdBQUc2QyxDQUFDLENBQUMsRUFBRTtBQUNwRSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL2NsaXAvaW5kZXguanM/MzFiMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY2xpcEJ1ZmZlciBmcm9tIFwiLi9idWZmZXIuanNcIjtcbmltcG9ydCBjbGlwUmVqb2luIGZyb20gXCIuL3Jlam9pbi5qc1wiO1xuaW1wb3J0IHtlcHNpbG9uLCBoYWxmUGl9IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5pbXBvcnQgcG9seWdvbkNvbnRhaW5zIGZyb20gXCIuLi9wb2x5Z29uQ29udGFpbnMuanNcIjtcbmltcG9ydCB7bWVyZ2V9IGZyb20gXCJkMy1hcnJheVwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwb2ludFZpc2libGUsIGNsaXBMaW5lLCBpbnRlcnBvbGF0ZSwgc3RhcnQpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHNpbmspIHtcbiAgICB2YXIgbGluZSA9IGNsaXBMaW5lKHNpbmspLFxuICAgICAgICByaW5nQnVmZmVyID0gY2xpcEJ1ZmZlcigpLFxuICAgICAgICByaW5nU2luayA9IGNsaXBMaW5lKHJpbmdCdWZmZXIpLFxuICAgICAgICBwb2x5Z29uU3RhcnRlZCA9IGZhbHNlLFxuICAgICAgICBwb2x5Z29uLFxuICAgICAgICBzZWdtZW50cyxcbiAgICAgICAgcmluZztcblxuICAgIHZhciBjbGlwID0ge1xuICAgICAgcG9pbnQ6IHBvaW50LFxuICAgICAgbGluZVN0YXJ0OiBsaW5lU3RhcnQsXG4gICAgICBsaW5lRW5kOiBsaW5lRW5kLFxuICAgICAgcG9seWdvblN0YXJ0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgY2xpcC5wb2ludCA9IHBvaW50UmluZztcbiAgICAgICAgY2xpcC5saW5lU3RhcnQgPSByaW5nU3RhcnQ7XG4gICAgICAgIGNsaXAubGluZUVuZCA9IHJpbmdFbmQ7XG4gICAgICAgIHNlZ21lbnRzID0gW107XG4gICAgICAgIHBvbHlnb24gPSBbXTtcbiAgICAgIH0sXG4gICAgICBwb2x5Z29uRW5kOiBmdW5jdGlvbigpIHtcbiAgICAgICAgY2xpcC5wb2ludCA9IHBvaW50O1xuICAgICAgICBjbGlwLmxpbmVTdGFydCA9IGxpbmVTdGFydDtcbiAgICAgICAgY2xpcC5saW5lRW5kID0gbGluZUVuZDtcbiAgICAgICAgc2VnbWVudHMgPSBtZXJnZShzZWdtZW50cyk7XG4gICAgICAgIHZhciBzdGFydEluc2lkZSA9IHBvbHlnb25Db250YWlucyhwb2x5Z29uLCBzdGFydCk7XG4gICAgICAgIGlmIChzZWdtZW50cy5sZW5ndGgpIHtcbiAgICAgICAgICBpZiAoIXBvbHlnb25TdGFydGVkKSBzaW5rLnBvbHlnb25TdGFydCgpLCBwb2x5Z29uU3RhcnRlZCA9IHRydWU7XG4gICAgICAgICAgY2xpcFJlam9pbihzZWdtZW50cywgY29tcGFyZUludGVyc2VjdGlvbiwgc3RhcnRJbnNpZGUsIGludGVycG9sYXRlLCBzaW5rKTtcbiAgICAgICAgfSBlbHNlIGlmIChzdGFydEluc2lkZSkge1xuICAgICAgICAgIGlmICghcG9seWdvblN0YXJ0ZWQpIHNpbmsucG9seWdvblN0YXJ0KCksIHBvbHlnb25TdGFydGVkID0gdHJ1ZTtcbiAgICAgICAgICBzaW5rLmxpbmVTdGFydCgpO1xuICAgICAgICAgIGludGVycG9sYXRlKG51bGwsIG51bGwsIDEsIHNpbmspO1xuICAgICAgICAgIHNpbmsubGluZUVuZCgpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwb2x5Z29uU3RhcnRlZCkgc2luay5wb2x5Z29uRW5kKCksIHBvbHlnb25TdGFydGVkID0gZmFsc2U7XG4gICAgICAgIHNlZ21lbnRzID0gcG9seWdvbiA9IG51bGw7XG4gICAgICB9LFxuICAgICAgc3BoZXJlOiBmdW5jdGlvbigpIHtcbiAgICAgICAgc2luay5wb2x5Z29uU3RhcnQoKTtcbiAgICAgICAgc2luay5saW5lU3RhcnQoKTtcbiAgICAgICAgaW50ZXJwb2xhdGUobnVsbCwgbnVsbCwgMSwgc2luayk7XG4gICAgICAgIHNpbmsubGluZUVuZCgpO1xuICAgICAgICBzaW5rLnBvbHlnb25FbmQoKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZnVuY3Rpb24gcG9pbnQobGFtYmRhLCBwaGkpIHtcbiAgICAgIGlmIChwb2ludFZpc2libGUobGFtYmRhLCBwaGkpKSBzaW5rLnBvaW50KGxhbWJkYSwgcGhpKTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiBwb2ludExpbmUobGFtYmRhLCBwaGkpIHtcbiAgICAgIGxpbmUucG9pbnQobGFtYmRhLCBwaGkpO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGxpbmVTdGFydCgpIHtcbiAgICAgIGNsaXAucG9pbnQgPSBwb2ludExpbmU7XG4gICAgICBsaW5lLmxpbmVTdGFydCgpO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGxpbmVFbmQoKSB7XG4gICAgICBjbGlwLnBvaW50ID0gcG9pbnQ7XG4gICAgICBsaW5lLmxpbmVFbmQoKTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiBwb2ludFJpbmcobGFtYmRhLCBwaGkpIHtcbiAgICAgIHJpbmcucHVzaChbbGFtYmRhLCBwaGldKTtcbiAgICAgIHJpbmdTaW5rLnBvaW50KGxhbWJkYSwgcGhpKTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiByaW5nU3RhcnQoKSB7XG4gICAgICByaW5nU2luay5saW5lU3RhcnQoKTtcbiAgICAgIHJpbmcgPSBbXTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiByaW5nRW5kKCkge1xuICAgICAgcG9pbnRSaW5nKHJpbmdbMF1bMF0sIHJpbmdbMF1bMV0pO1xuICAgICAgcmluZ1NpbmsubGluZUVuZCgpO1xuXG4gICAgICB2YXIgY2xlYW4gPSByaW5nU2luay5jbGVhbigpLFxuICAgICAgICAgIHJpbmdTZWdtZW50cyA9IHJpbmdCdWZmZXIucmVzdWx0KCksXG4gICAgICAgICAgaSwgbiA9IHJpbmdTZWdtZW50cy5sZW5ndGgsIG0sXG4gICAgICAgICAgc2VnbWVudCxcbiAgICAgICAgICBwb2ludDtcblxuICAgICAgcmluZy5wb3AoKTtcbiAgICAgIHBvbHlnb24ucHVzaChyaW5nKTtcbiAgICAgIHJpbmcgPSBudWxsO1xuXG4gICAgICBpZiAoIW4pIHJldHVybjtcblxuICAgICAgLy8gTm8gaW50ZXJzZWN0aW9ucy5cbiAgICAgIGlmIChjbGVhbiAmIDEpIHtcbiAgICAgICAgc2VnbWVudCA9IHJpbmdTZWdtZW50c1swXTtcbiAgICAgICAgaWYgKChtID0gc2VnbWVudC5sZW5ndGggLSAxKSA+IDApIHtcbiAgICAgICAgICBpZiAoIXBvbHlnb25TdGFydGVkKSBzaW5rLnBvbHlnb25TdGFydCgpLCBwb2x5Z29uU3RhcnRlZCA9IHRydWU7XG4gICAgICAgICAgc2luay5saW5lU3RhcnQoKTtcbiAgICAgICAgICBmb3IgKGkgPSAwOyBpIDwgbTsgKytpKSBzaW5rLnBvaW50KChwb2ludCA9IHNlZ21lbnRbaV0pWzBdLCBwb2ludFsxXSk7XG4gICAgICAgICAgc2luay5saW5lRW5kKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBSZWpvaW4gY29ubmVjdGVkIHNlZ21lbnRzLlxuICAgICAgLy8gVE9ETyByZXVzZSByaW5nQnVmZmVyLnJlam9pbigpP1xuICAgICAgaWYgKG4gPiAxICYmIGNsZWFuICYgMikgcmluZ1NlZ21lbnRzLnB1c2gocmluZ1NlZ21lbnRzLnBvcCgpLmNvbmNhdChyaW5nU2VnbWVudHMuc2hpZnQoKSkpO1xuXG4gICAgICBzZWdtZW50cy5wdXNoKHJpbmdTZWdtZW50cy5maWx0ZXIodmFsaWRTZWdtZW50KSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGNsaXA7XG4gIH07XG59XG5cbmZ1bmN0aW9uIHZhbGlkU2VnbWVudChzZWdtZW50KSB7XG4gIHJldHVybiBzZWdtZW50Lmxlbmd0aCA+IDE7XG59XG5cbi8vIEludGVyc2VjdGlvbnMgYXJlIHNvcnRlZCBhbG9uZyB0aGUgY2xpcCBlZGdlLiBGb3IgYm90aCBhbnRpbWVyaWRpYW4gY3V0dGluZ1xuLy8gYW5kIGNpcmNsZSBjbGlwcGluZywgdGhlIHNhbWUgY29tcGFyaXNvbiBpcyB1c2VkLlxuZnVuY3Rpb24gY29tcGFyZUludGVyc2VjdGlvbihhLCBiKSB7XG4gIHJldHVybiAoKGEgPSBhLngpWzBdIDwgMCA/IGFbMV0gLSBoYWxmUGkgLSBlcHNpbG9uIDogaGFsZlBpIC0gYVsxXSlcbiAgICAgICAtICgoYiA9IGIueClbMF0gPCAwID8gYlsxXSAtIGhhbGZQaSAtIGVwc2lsb24gOiBoYWxmUGkgLSBiWzFdKTtcbn1cbiJdLCJuYW1lcyI6WyJjbGlwQnVmZmVyIiwiY2xpcFJlam9pbiIsImVwc2lsb24iLCJoYWxmUGkiLCJwb2x5Z29uQ29udGFpbnMiLCJtZXJnZSIsInBvaW50VmlzaWJsZSIsImNsaXBMaW5lIiwiaW50ZXJwb2xhdGUiLCJzdGFydCIsInNpbmsiLCJsaW5lIiwicmluZ0J1ZmZlciIsInJpbmdTaW5rIiwicG9seWdvblN0YXJ0ZWQiLCJwb2x5Z29uIiwic2VnbWVudHMiLCJyaW5nIiwiY2xpcCIsInBvaW50IiwibGluZVN0YXJ0IiwibGluZUVuZCIsInBvbHlnb25TdGFydCIsInBvaW50UmluZyIsInJpbmdTdGFydCIsInJpbmdFbmQiLCJwb2x5Z29uRW5kIiwic3RhcnRJbnNpZGUiLCJsZW5ndGgiLCJjb21wYXJlSW50ZXJzZWN0aW9uIiwic3BoZXJlIiwibGFtYmRhIiwicGhpIiwicG9pbnRMaW5lIiwicHVzaCIsImNsZWFuIiwicmluZ1NlZ21lbnRzIiwicmVzdWx0IiwiaSIsIm4iLCJtIiwic2VnbWVudCIsInBvcCIsImNvbmNhdCIsInNoaWZ0IiwiZmlsdGVyIiwidmFsaWRTZWdtZW50IiwiYSIsImIiLCJ4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/line.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/line.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b, x0, y0, x1, y1) {\n    var ax = a[0], ay = a[1], bx = b[0], by = b[1], t0 = 0, t1 = 1, dx = bx - ax, dy = by - ay, r;\n    r = x0 - ax;\n    if (!dx && r > 0) return;\n    r /= dx;\n    if (dx < 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    } else if (dx > 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    }\n    r = x1 - ax;\n    if (!dx && r < 0) return;\n    r /= dx;\n    if (dx < 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    } else if (dx > 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    }\n    r = y0 - ay;\n    if (!dy && r > 0) return;\n    r /= dy;\n    if (dy < 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    } else if (dy > 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    }\n    r = y1 - ay;\n    if (!dy && r < 0) return;\n    r /= dy;\n    if (dy < 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    } else if (dy > 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    }\n    if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n    if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/line.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/rectangle.js":
/*!*******************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/rectangle.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ clipRectangle)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/buffer.js\");\n/* harmony import */ var _line_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./line.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/line.js\");\n/* harmony import */ var _rejoin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rejoin.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/rejoin.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/merge.js\");\n\n\n\n\n\nvar clipMax = 1e9, clipMin = -clipMax;\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\nfunction clipRectangle(x0, y0, x1, y1) {\n    function visible(x, y) {\n        return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n    }\n    function interpolate(from, to, direction, stream) {\n        var a = 0, a1 = 0;\n        if (from == null || (a = corner(from, direction)) !== (a1 = corner(to, direction)) || comparePoint(from, to) < 0 ^ direction > 0) {\n            do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n            while ((a = (a + direction + 4) % 4) !== a1);\n        } else {\n            stream.point(to[0], to[1]);\n        }\n    }\n    function corner(p, direction) {\n        return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[0] - x0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 0 : 3 : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[0] - x1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 2 : 1 : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[1] - y0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 1 : 0 : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n    }\n    function compareIntersection(a, b) {\n        return comparePoint(a.x, b.x);\n    }\n    function comparePoint(a, b) {\n        var ca = corner(a, 1), cb = corner(b, 1);\n        return ca !== cb ? ca - cb : ca === 0 ? b[1] - a[1] : ca === 1 ? a[0] - b[0] : ca === 2 ? a[1] - b[1] : b[0] - a[0];\n    }\n    return function(stream) {\n        var activeStream = stream, bufferStream = (0,_buffer_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), segments, polygon, ring, x__, y__, v__, x_, y_, v_, first, clean;\n        var clipStream = {\n            point: point,\n            lineStart: lineStart,\n            lineEnd: lineEnd,\n            polygonStart: polygonStart,\n            polygonEnd: polygonEnd\n        };\n        function point(x, y) {\n            if (visible(x, y)) activeStream.point(x, y);\n        }\n        function polygonInside() {\n            var winding = 0;\n            for(var i = 0, n = polygon.length; i < n; ++i){\n                for(var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j){\n                    a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n                    if (a1 <= y1) {\n                        if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding;\n                    } else {\n                        if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding;\n                    }\n                }\n            }\n            return winding;\n        }\n        // Buffer geometry within a polygon and then clip it en masse.\n        function polygonStart() {\n            activeStream = bufferStream, segments = [], polygon = [], clean = true;\n        }\n        function polygonEnd() {\n            var startInside = polygonInside(), cleanInside = clean && startInside, visible = (segments = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(segments)).length;\n            if (cleanInside || visible) {\n                stream.polygonStart();\n                if (cleanInside) {\n                    stream.lineStart();\n                    interpolate(null, null, 1, stream);\n                    stream.lineEnd();\n                }\n                if (visible) {\n                    (0,_rejoin_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(segments, compareIntersection, startInside, interpolate, stream);\n                }\n                stream.polygonEnd();\n            }\n            activeStream = stream, segments = polygon = ring = null;\n        }\n        function lineStart() {\n            clipStream.point = linePoint;\n            if (polygon) polygon.push(ring = []);\n            first = true;\n            v_ = false;\n            x_ = y_ = NaN;\n        }\n        // TODO rather than special-case polygons, simply handle them separately.\n        // Ideally, coincident intersection points should be jittered to avoid\n        // clipping issues.\n        function lineEnd() {\n            if (segments) {\n                linePoint(x__, y__);\n                if (v__ && v_) bufferStream.rejoin();\n                segments.push(bufferStream.result());\n            }\n            clipStream.point = point;\n            if (v_) activeStream.lineEnd();\n        }\n        function linePoint(x, y) {\n            var v = visible(x, y);\n            if (polygon) ring.push([\n                x,\n                y\n            ]);\n            if (first) {\n                x__ = x, y__ = y, v__ = v;\n                first = false;\n                if (v) {\n                    activeStream.lineStart();\n                    activeStream.point(x, y);\n                }\n            } else {\n                if (v && v_) activeStream.point(x, y);\n                else {\n                    var a = [\n                        x_ = Math.max(clipMin, Math.min(clipMax, x_)),\n                        y_ = Math.max(clipMin, Math.min(clipMax, y_))\n                    ], b = [\n                        x = Math.max(clipMin, Math.min(clipMax, x)),\n                        y = Math.max(clipMin, Math.min(clipMax, y))\n                    ];\n                    if ((0,_line_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(a, b, x0, y0, x1, y1)) {\n                        if (!v_) {\n                            activeStream.lineStart();\n                            activeStream.point(a[0], a[1]);\n                        }\n                        activeStream.point(b[0], b[1]);\n                        if (!v) activeStream.lineEnd();\n                        clean = false;\n                    } else if (v) {\n                        activeStream.lineStart();\n                        activeStream.point(x, y);\n                        clean = false;\n                    }\n                }\n            }\n            x_ = x, y_ = y, v_ = v;\n        }\n        return clipStream;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/rectangle.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/clip/rejoin.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-geo/src/clip/rejoin.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _pointEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../pointEqual.js */ \"(ssr)/../../node_modules/d3-geo/src/pointEqual.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\nfunction Intersection(point, points, other, entry) {\n    this.x = point;\n    this.z = points;\n    this.o = other; // another intersection\n    this.e = entry; // is an entry?\n    this.v = false; // visited\n    this.n = this.p = null; // next & previous\n}\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(segments, compareIntersection, startInside, interpolate, stream) {\n    var subject = [], clip = [], i, n;\n    segments.forEach(function(segment) {\n        if ((n = segment.length - 1) <= 0) return;\n        var n, p0 = segment[0], p1 = segment[n], x;\n        if ((0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(p0, p1)) {\n            if (!p0[2] && !p1[2]) {\n                stream.lineStart();\n                for(i = 0; i < n; ++i)stream.point((p0 = segment[i])[0], p0[1]);\n                stream.lineEnd();\n                return;\n            }\n            // handle degenerate cases by moving the point\n            p1[0] += 2 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n        }\n        subject.push(x = new Intersection(p0, segment, null, true));\n        clip.push(x.o = new Intersection(p0, null, x, false));\n        subject.push(x = new Intersection(p1, segment, null, false));\n        clip.push(x.o = new Intersection(p1, null, x, true));\n    });\n    if (!subject.length) return;\n    clip.sort(compareIntersection);\n    link(subject);\n    link(clip);\n    for(i = 0, n = clip.length; i < n; ++i){\n        clip[i].e = startInside = !startInside;\n    }\n    var start = subject[0], points, point;\n    while(1){\n        // Find first unvisited intersection.\n        var current = start, isSubject = true;\n        while(current.v)if ((current = current.n) === start) return;\n        points = current.z;\n        stream.lineStart();\n        do {\n            current.v = current.o.v = true;\n            if (current.e) {\n                if (isSubject) {\n                    for(i = 0, n = points.length; i < n; ++i)stream.point((point = points[i])[0], point[1]);\n                } else {\n                    interpolate(current.x, current.n.x, 1, stream);\n                }\n                current = current.n;\n            } else {\n                if (isSubject) {\n                    points = current.p.z;\n                    for(i = points.length - 1; i >= 0; --i)stream.point((point = points[i])[0], point[1]);\n                } else {\n                    interpolate(current.x, current.p.x, -1, stream);\n                }\n                current = current.p;\n            }\n            current = current.o;\n            points = current.z;\n            isSubject = !isSubject;\n        }while (!current.v);\n        stream.lineEnd();\n    }\n}\nfunction link(array) {\n    if (!(n = array.length)) return;\n    var n, i = 0, a = array[0], b;\n    while(++i < n){\n        a.n = b = array[i];\n        b.p = a;\n        a = b;\n    }\n    a.n = b = array[0];\n    b.p = a;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/clip/rejoin.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/compose.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-geo/src/compose.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    function compose(x, y) {\n        return x = a(x, y), b(x[0], x[1]);\n    }\n    if (a.invert && b.invert) compose.invert = function(x, y) {\n        return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n    };\n    return compose;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY29tcG9zZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLENBQUMsRUFBRUMsQ0FBQztJQUUxQixTQUFTQyxRQUFRQyxDQUFDLEVBQUVDLENBQUM7UUFDbkIsT0FBT0QsSUFBSUgsRUFBRUcsR0FBR0MsSUFBSUgsRUFBRUUsQ0FBQyxDQUFDLEVBQUUsRUFBRUEsQ0FBQyxDQUFDLEVBQUU7SUFDbEM7SUFFQSxJQUFJSCxFQUFFSyxNQUFNLElBQUlKLEVBQUVJLE1BQU0sRUFBRUgsUUFBUUcsTUFBTSxHQUFHLFNBQVNGLENBQUMsRUFBRUMsQ0FBQztRQUN0RCxPQUFPRCxJQUFJRixFQUFFSSxNQUFNLENBQUNGLEdBQUdDLElBQUlELEtBQUtILEVBQUVLLE1BQU0sQ0FBQ0YsQ0FBQyxDQUFDLEVBQUUsRUFBRUEsQ0FBQyxDQUFDLEVBQUU7SUFDckQ7SUFFQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY29tcG9zZS5qcz8wZDQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGEsIGIpIHtcblxuICBmdW5jdGlvbiBjb21wb3NlKHgsIHkpIHtcbiAgICByZXR1cm4geCA9IGEoeCwgeSksIGIoeFswXSwgeFsxXSk7XG4gIH1cblxuICBpZiAoYS5pbnZlcnQgJiYgYi5pbnZlcnQpIGNvbXBvc2UuaW52ZXJ0ID0gZnVuY3Rpb24oeCwgeSkge1xuICAgIHJldHVybiB4ID0gYi5pbnZlcnQoeCwgeSksIHggJiYgYS5pbnZlcnQoeFswXSwgeFsxXSk7XG4gIH07XG5cbiAgcmV0dXJuIGNvbXBvc2U7XG59XG4iXSwibmFtZXMiOlsiYSIsImIiLCJjb21wb3NlIiwieCIsInkiLCJpbnZlcnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/compose.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/constant.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-geo/src/constant.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return function() {\n        return x;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY29uc3RhbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxDQUFDO0lBQ3ZCLE9BQU87UUFDTCxPQUFPQTtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jb25zdGFudC5qcz82YmZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHgpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB4O1xuICB9O1xufVxuIl0sIm5hbWVzIjpbIngiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/constant.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/contains.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-geo/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _polygonContains_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./polygonContains.js */ \"(ssr)/../../node_modules/d3-geo/src/polygonContains.js\");\n/* harmony import */ var _distance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./distance.js */ \"(ssr)/../../node_modules/d3-geo/src/distance.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\n\nvar containsObjectType = {\n    Feature: function(object, point) {\n        return containsGeometry(object.geometry, point);\n    },\n    FeatureCollection: function(object, point) {\n        var features = object.features, i = -1, n = features.length;\n        while(++i < n)if (containsGeometry(features[i].geometry, point)) return true;\n        return false;\n    }\n};\nvar containsGeometryType = {\n    Sphere: function() {\n        return true;\n    },\n    Point: function(object, point) {\n        return containsPoint(object.coordinates, point);\n    },\n    MultiPoint: function(object, point) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)if (containsPoint(coordinates[i], point)) return true;\n        return false;\n    },\n    LineString: function(object, point) {\n        return containsLine(object.coordinates, point);\n    },\n    MultiLineString: function(object, point) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)if (containsLine(coordinates[i], point)) return true;\n        return false;\n    },\n    Polygon: function(object, point) {\n        return containsPolygon(object.coordinates, point);\n    },\n    MultiPolygon: function(object, point) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)if (containsPolygon(coordinates[i], point)) return true;\n        return false;\n    },\n    GeometryCollection: function(object, point) {\n        var geometries = object.geometries, i = -1, n = geometries.length;\n        while(++i < n)if (containsGeometry(geometries[i], point)) return true;\n        return false;\n    }\n};\nfunction containsGeometry(geometry, point) {\n    return geometry && containsGeometryType.hasOwnProperty(geometry.type) ? containsGeometryType[geometry.type](geometry, point) : false;\n}\nfunction containsPoint(coordinates, point) {\n    return (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates, point) === 0;\n}\nfunction containsLine(coordinates, point) {\n    var ao, bo, ab;\n    for(var i = 0, n = coordinates.length; i < n; i++){\n        bo = (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates[i], point);\n        if (bo === 0) return true;\n        if (i > 0) {\n            ab = (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates[i], coordinates[i - 1]);\n            if (ab > 0 && ao <= ab && bo <= ab && (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2 * ab) return true;\n        }\n        ao = bo;\n    }\n    return false;\n}\nfunction containsPolygon(coordinates, point) {\n    return !!(0,_polygonContains_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(coordinates.map(ringRadians), pointRadians(point));\n}\nfunction ringRadians(ring) {\n    return ring = ring.map(pointRadians), ring.pop(), ring;\n}\nfunction pointRadians(point) {\n    return [\n        point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians,\n        point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians\n    ];\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object, point) {\n    return (object && containsObjectType.hasOwnProperty(object.type) ? containsObjectType[object.type] : containsGeometry)(object, point);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/contains.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/distance.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-geo/src/distance.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./length.js */ \"(ssr)/../../node_modules/d3-geo/src/length.js\");\n\nvar coordinates = [\n    null,\n    null\n], object = {\n    type: \"LineString\",\n    coordinates: coordinates\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    coordinates[0] = a;\n    coordinates[1] = b;\n    return (0,_length_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvZGlzdGFuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUM7QUFFakMsSUFBSUMsY0FBYztJQUFDO0lBQU07Q0FBSyxFQUMxQkMsU0FBUztJQUFDQyxNQUFNO0lBQWNGLGFBQWFBO0FBQVc7QUFFMUQsNkJBQWUsb0NBQVNHLENBQUMsRUFBRUMsQ0FBQztJQUMxQkosV0FBVyxDQUFDLEVBQUUsR0FBR0c7SUFDakJILFdBQVcsQ0FBQyxFQUFFLEdBQUdJO0lBQ2pCLE9BQU9MLHNEQUFNQSxDQUFDRTtBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL2Rpc3RhbmNlLmpzP2VmNjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGxlbmd0aCBmcm9tIFwiLi9sZW5ndGguanNcIjtcblxudmFyIGNvb3JkaW5hdGVzID0gW251bGwsIG51bGxdLFxuICAgIG9iamVjdCA9IHt0eXBlOiBcIkxpbmVTdHJpbmdcIiwgY29vcmRpbmF0ZXM6IGNvb3JkaW5hdGVzfTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oYSwgYikge1xuICBjb29yZGluYXRlc1swXSA9IGE7XG4gIGNvb3JkaW5hdGVzWzFdID0gYjtcbiAgcmV0dXJuIGxlbmd0aChvYmplY3QpO1xufVxuIl0sIm5hbWVzIjpbImxlbmd0aCIsImNvb3JkaW5hdGVzIiwib2JqZWN0IiwidHlwZSIsImEiLCJiIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/distance.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/graticule.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-geo/src/graticule.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ graticule),\n/* harmony export */   graticule10: () => (/* binding */ graticule10)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/range.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\nfunction graticuleX(y0, y1, dy) {\n    var y = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(y0, y1 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon, dy).concat(y1);\n    return function(x) {\n        return y.map(function(y) {\n            return [\n                x,\n                y\n            ];\n        });\n    };\n}\nfunction graticuleY(x0, x1, dx) {\n    var x = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(x0, x1 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon, dx).concat(x1);\n    return function(y) {\n        return x.map(function(x) {\n            return [\n                x,\n                y\n            ];\n        });\n    };\n}\nfunction graticule() {\n    var x1, x0, X1, X0, y1, y0, Y1, Y0, dx = 10, dy = dx, DX = 90, DY = 360, x, y, X, Y, precision = 2.5;\n    function graticule() {\n        return {\n            type: \"MultiLineString\",\n            coordinates: lines()\n        };\n    }\n    function lines() {\n        return (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(X0 / DX) * DX, X1, DX).map(X).concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(Y0 / DY) * DY, Y1, DY).map(Y)).concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(x0 / dx) * dx, x1, dx).filter(function(x) {\n            return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(x % DX) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n        }).map(x)).concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(y0 / dy) * dy, y1, dy).filter(function(y) {\n            return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(y % DY) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n        }).map(y));\n    }\n    graticule.lines = function() {\n        return lines().map(function(coordinates) {\n            return {\n                type: \"LineString\",\n                coordinates: coordinates\n            };\n        });\n    };\n    graticule.outline = function() {\n        return {\n            type: \"Polygon\",\n            coordinates: [\n                X(X0).concat(Y(Y1).slice(1), X(X1).reverse().slice(1), Y(Y0).reverse().slice(1))\n            ]\n        };\n    };\n    graticule.extent = function(_) {\n        if (!arguments.length) return graticule.extentMinor();\n        return graticule.extentMajor(_).extentMinor(_);\n    };\n    graticule.extentMajor = function(_) {\n        if (!arguments.length) return [\n            [\n                X0,\n                Y0\n            ],\n            [\n                X1,\n                Y1\n            ]\n        ];\n        X0 = +_[0][0], X1 = +_[1][0];\n        Y0 = +_[0][1], Y1 = +_[1][1];\n        if (X0 > X1) _ = X0, X0 = X1, X1 = _;\n        if (Y0 > Y1) _ = Y0, Y0 = Y1, Y1 = _;\n        return graticule.precision(precision);\n    };\n    graticule.extentMinor = function(_) {\n        if (!arguments.length) return [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n        x0 = +_[0][0], x1 = +_[1][0];\n        y0 = +_[0][1], y1 = +_[1][1];\n        if (x0 > x1) _ = x0, x0 = x1, x1 = _;\n        if (y0 > y1) _ = y0, y0 = y1, y1 = _;\n        return graticule.precision(precision);\n    };\n    graticule.step = function(_) {\n        if (!arguments.length) return graticule.stepMinor();\n        return graticule.stepMajor(_).stepMinor(_);\n    };\n    graticule.stepMajor = function(_) {\n        if (!arguments.length) return [\n            DX,\n            DY\n        ];\n        DX = +_[0], DY = +_[1];\n        return graticule;\n    };\n    graticule.stepMinor = function(_) {\n        if (!arguments.length) return [\n            dx,\n            dy\n        ];\n        dx = +_[0], dy = +_[1];\n        return graticule;\n    };\n    graticule.precision = function(_) {\n        if (!arguments.length) return precision;\n        precision = +_;\n        x = graticuleX(y0, y1, 90);\n        y = graticuleY(x0, x1, precision);\n        X = graticuleX(Y0, Y1, 90);\n        Y = graticuleY(X0, X1, precision);\n        return graticule;\n    };\n    return graticule.extentMajor([\n        [\n            -180,\n            -90 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ],\n        [\n            180,\n            90 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ]\n    ]).extentMinor([\n        [\n            -180,\n            -80 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ],\n        [\n            180,\n            80 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ]\n    ]);\n}\nfunction graticule10() {\n    return graticule()();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/graticule.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/identity.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-geo/src/identity.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((x)=>x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvaWRlbnRpdHkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlQSxDQUFBQSxJQUFLQSxDQUFBQSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvaWRlbnRpdHkuanM/YmU2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB4ID0+IHg7XG4iXSwibmFtZXMiOlsieCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/identity.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/index.js":
/*!**********************************************!*\
  !*** ../../node_modules/d3-geo/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   geoAlbers: () => (/* reexport safe */ _projection_albers_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   geoAlbersUsa: () => (/* reexport safe */ _projection_albersUsa_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   geoArea: () => (/* reexport safe */ _area_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   geoAzimuthalEqualArea: () => (/* reexport safe */ _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   geoAzimuthalEqualAreaRaw: () => (/* reexport safe */ _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__.azimuthalEqualAreaRaw),\n/* harmony export */   geoAzimuthalEquidistant: () => (/* reexport safe */ _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   geoAzimuthalEquidistantRaw: () => (/* reexport safe */ _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__.azimuthalEquidistantRaw),\n/* harmony export */   geoBounds: () => (/* reexport safe */ _bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   geoCentroid: () => (/* reexport safe */ _centroid_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   geoCircle: () => (/* reexport safe */ _circle_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   geoClipAntimeridian: () => (/* reexport safe */ _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   geoClipCircle: () => (/* reexport safe */ _clip_circle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   geoClipExtent: () => (/* reexport safe */ _clip_extent_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   geoClipRectangle: () => (/* reexport safe */ _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   geoConicConformal: () => (/* reexport safe */ _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   geoConicConformalRaw: () => (/* reexport safe */ _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__.conicConformalRaw),\n/* harmony export */   geoConicEqualArea: () => (/* reexport safe */ _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   geoConicEqualAreaRaw: () => (/* reexport safe */ _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__.conicEqualAreaRaw),\n/* harmony export */   geoConicEquidistant: () => (/* reexport safe */ _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   geoConicEquidistantRaw: () => (/* reexport safe */ _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__.conicEquidistantRaw),\n/* harmony export */   geoContains: () => (/* reexport safe */ _contains_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   geoDistance: () => (/* reexport safe */ _distance_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   geoEqualEarth: () => (/* reexport safe */ _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   geoEqualEarthRaw: () => (/* reexport safe */ _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__.equalEarthRaw),\n/* harmony export */   geoEquirectangular: () => (/* reexport safe */ _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   geoEquirectangularRaw: () => (/* reexport safe */ _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__.equirectangularRaw),\n/* harmony export */   geoGnomonic: () => (/* reexport safe */ _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   geoGnomonicRaw: () => (/* reexport safe */ _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__.gnomonicRaw),\n/* harmony export */   geoGraticule: () => (/* reexport safe */ _graticule_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   geoGraticule10: () => (/* reexport safe */ _graticule_js__WEBPACK_IMPORTED_MODULE_10__.graticule10),\n/* harmony export */   geoIdentity: () => (/* reexport safe */ _projection_identity_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   geoInterpolate: () => (/* reexport safe */ _interpolate_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   geoLength: () => (/* reexport safe */ _length_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   geoMercator: () => (/* reexport safe */ _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   geoMercatorRaw: () => (/* reexport safe */ _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__.mercatorRaw),\n/* harmony export */   geoNaturalEarth1: () => (/* reexport safe */ _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   geoNaturalEarth1Raw: () => (/* reexport safe */ _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__.naturalEarth1Raw),\n/* harmony export */   geoOrthographic: () => (/* reexport safe */ _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   geoOrthographicRaw: () => (/* reexport safe */ _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__.orthographicRaw),\n/* harmony export */   geoPath: () => (/* reexport safe */ _path_index_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   geoProjection: () => (/* reexport safe */ _projection_index_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   geoProjectionMutator: () => (/* reexport safe */ _projection_index_js__WEBPACK_IMPORTED_MODULE_25__.projectionMutator),\n/* harmony export */   geoRotation: () => (/* reexport safe */ _rotation_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   geoStereographic: () => (/* reexport safe */ _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   geoStereographicRaw: () => (/* reexport safe */ _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__.stereographicRaw),\n/* harmony export */   geoStream: () => (/* reexport safe */ _stream_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"]),\n/* harmony export */   geoTransform: () => (/* reexport safe */ _transform_js__WEBPACK_IMPORTED_MODULE_33__[\"default\"]),\n/* harmony export */   geoTransverseMercator: () => (/* reexport safe */ _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   geoTransverseMercatorRaw: () => (/* reexport safe */ _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__.transverseMercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./area.js */ \"(ssr)/../../node_modules/d3-geo/src/area.js\");\n/* harmony import */ var _bounds_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bounds.js */ \"(ssr)/../../node_modules/d3-geo/src/bounds.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/../../node_modules/d3-geo/src/centroid.js\");\n/* harmony import */ var _circle_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./circle.js */ \"(ssr)/../../node_modules/d3-geo/src/circle.js\");\n/* harmony import */ var _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./clip/antimeridian.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/antimeridian.js\");\n/* harmony import */ var _clip_circle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./clip/circle.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/circle.js\");\n/* harmony import */ var _clip_extent_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./clip/extent.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/extent.js\");\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./clip/rectangle.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/../../node_modules/d3-geo/src/contains.js\");\n/* harmony import */ var _distance_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./distance.js */ \"(ssr)/../../node_modules/d3-geo/src/distance.js\");\n/* harmony import */ var _graticule_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./graticule.js */ \"(ssr)/../../node_modules/d3-geo/src/graticule.js\");\n/* harmony import */ var _interpolate_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./interpolate.js */ \"(ssr)/../../node_modules/d3-geo/src/interpolate.js\");\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./length.js */ \"(ssr)/../../node_modules/d3-geo/src/length.js\");\n/* harmony import */ var _path_index_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./path/index.js */ \"(ssr)/../../node_modules/d3-geo/src/path/index.js\");\n/* harmony import */ var _projection_albers_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./projection/albers.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/albers.js\");\n/* harmony import */ var _projection_albersUsa_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./projection/albersUsa.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/albersUsa.js\");\n/* harmony import */ var _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./projection/azimuthalEqualArea.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/azimuthalEqualArea.js\");\n/* harmony import */ var _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./projection/azimuthalEquidistant.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/azimuthalEquidistant.js\");\n/* harmony import */ var _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./projection/conicConformal.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conicConformal.js\");\n/* harmony import */ var _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./projection/conicEqualArea.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conicEqualArea.js\");\n/* harmony import */ var _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./projection/conicEquidistant.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conicEquidistant.js\");\n/* harmony import */ var _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./projection/equalEarth.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/equalEarth.js\");\n/* harmony import */ var _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./projection/equirectangular.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/equirectangular.js\");\n/* harmony import */ var _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./projection/gnomonic.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/gnomonic.js\");\n/* harmony import */ var _projection_identity_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./projection/identity.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/identity.js\");\n/* harmony import */ var _projection_index_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./projection/index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./projection/mercator.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/mercator.js\");\n/* harmony import */ var _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./projection/naturalEarth1.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/naturalEarth1.js\");\n/* harmony import */ var _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./projection/orthographic.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/orthographic.js\");\n/* harmony import */ var _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./projection/stereographic.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/stereographic.js\");\n/* harmony import */ var _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./projection/transverseMercator.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/transverseMercator.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./rotation.js */ \"(ssr)/../../node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/../../node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./transform.js */ \"(ssr)/../../node_modules/d3-geo/src/transform.js\");\n\n\n\n\n\n\n // DEPRECATED! Use d3.geoIdentity().clipExtent(…).\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/interpolate.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-geo/src/interpolate.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    var x0 = a[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, y0 = a[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, x1 = b[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, y1 = b[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0), sy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0), cy1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1), sy1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y1), kx0 = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x0), ky0 = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x0), kx1 = cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x1), ky1 = cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x1), d = 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.haversin)(y1 - y0) + cy0 * cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.haversin)(x1 - x0))), k = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(d);\n    var interpolate = d ? function(t) {\n        var B = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(t *= d) / k, A = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(d - t) / k, x = A * kx0 + B * kx1, y = A * ky0 + B * ky1, z = A * sy0 + B * sy1;\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y, x) * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(z, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + y * y)) * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees\n        ];\n    } : function() {\n        return [\n            x0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees,\n            y0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees\n        ];\n    };\n    interpolate.distance = d;\n    return interpolate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/interpolate.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/length.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-geo/src/length.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/../../node_modules/d3-geo/src/stream.js\");\n\n\n\n\nvar lengthSum, lambda0, sinPhi0, cosPhi0;\nvar lengthStream = {\n    sphere: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    lineStart: lengthLineStart,\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n};\nfunction lengthLineStart() {\n    lengthStream.point = lengthPointFirst;\n    lengthStream.lineEnd = lengthLineEnd;\n}\nfunction lengthLineEnd() {\n    lengthStream.point = lengthStream.lineEnd = _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n}\nfunction lengthPointFirst(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    lambda0 = lambda, sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    lengthStream.point = lengthPoint;\n}\nfunction lengthPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi), delta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda - lambda0), cosDelta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(delta), sinDelta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(delta), x = cosPhi * sinDelta, y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta, z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n    lengthSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(x * x + y * y), z));\n    lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n    lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, lengthStream);\n    return +lengthSum;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/length.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/math.js":
/*!*********************************************!*\
  !*** ../../node_modules/d3-geo/src/math.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   abs: () => (/* binding */ abs),\n/* harmony export */   acos: () => (/* binding */ acos),\n/* harmony export */   asin: () => (/* binding */ asin),\n/* harmony export */   atan: () => (/* binding */ atan),\n/* harmony export */   atan2: () => (/* binding */ atan2),\n/* harmony export */   ceil: () => (/* binding */ ceil),\n/* harmony export */   cos: () => (/* binding */ cos),\n/* harmony export */   degrees: () => (/* binding */ degrees),\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   epsilon2: () => (/* binding */ epsilon2),\n/* harmony export */   exp: () => (/* binding */ exp),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   halfPi: () => (/* binding */ halfPi),\n/* harmony export */   haversin: () => (/* binding */ haversin),\n/* harmony export */   hypot: () => (/* binding */ hypot),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   pi: () => (/* binding */ pi),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   quarterPi: () => (/* binding */ quarterPi),\n/* harmony export */   radians: () => (/* binding */ radians),\n/* harmony export */   sign: () => (/* binding */ sign),\n/* harmony export */   sin: () => (/* binding */ sin),\n/* harmony export */   sqrt: () => (/* binding */ sqrt),\n/* harmony export */   tan: () => (/* binding */ tan),\n/* harmony export */   tau: () => (/* binding */ tau)\n/* harmony export */ });\nvar epsilon = 1e-6;\nvar epsilon2 = 1e-12;\nvar pi = Math.PI;\nvar halfPi = pi / 2;\nvar quarterPi = pi / 4;\nvar tau = pi * 2;\nvar degrees = 180 / pi;\nvar radians = pi / 180;\nvar abs = Math.abs;\nvar atan = Math.atan;\nvar atan2 = Math.atan2;\nvar cos = Math.cos;\nvar ceil = Math.ceil;\nvar exp = Math.exp;\nvar floor = Math.floor;\nvar hypot = Math.hypot;\nvar log = Math.log;\nvar pow = Math.pow;\nvar sin = Math.sin;\nvar sign = Math.sign || function(x) {\n    return x > 0 ? 1 : x < 0 ? -1 : 0;\n};\nvar sqrt = Math.sqrt;\nvar tan = Math.tan;\nfunction acos(x) {\n    return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\nfunction asin(x) {\n    return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\nfunction haversin(x) {\n    return (x = sin(x / 2)) * x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/math.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/noop.js":
/*!*********************************************!*\
  !*** ../../node_modules/d3-geo/src/noop.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ noop)\n/* harmony export */ });\nfunction noop() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvbm9vcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsUUFBUSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL25vb3AuanM/NzdmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBub29wKCkge31cbiJdLCJuYW1lcyI6WyJub29wIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/noop.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/path/area.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-geo/src/path/area.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n\n\n\nvar areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), x00, y00, x0, y0;\nvar areaStream = {\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    polygonStart: function() {\n        areaStream.lineStart = areaRingStart;\n        areaStream.lineEnd = areaRingEnd;\n    },\n    polygonEnd: function() {\n        areaStream.lineStart = areaStream.lineEnd = areaStream.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        areaSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(areaRingSum));\n        areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    },\n    result: function() {\n        var area = areaSum / 2;\n        areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        return area;\n    }\n};\nfunction areaRingStart() {\n    areaStream.point = areaPointFirst;\n}\nfunction areaPointFirst(x, y) {\n    areaStream.point = areaPoint;\n    x00 = x0 = x, y00 = y0 = y;\n}\nfunction areaPoint(x, y) {\n    areaRingSum.add(y0 * x - x0 * y);\n    x0 = x, y0 = y;\n}\nfunction areaRingEnd() {\n    areaPoint(x00, y00);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (areaStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/path/area.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/path/bounds.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-geo/src/path/bounds.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n\nvar x0 = Infinity, y0 = x0, x1 = -x0, y1 = x1;\nvar boundsStream = {\n    point: boundsPoint,\n    lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    result: function() {\n        var bounds = [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n        x1 = y1 = -(y0 = x0 = Infinity);\n        return bounds;\n    }\n};\nfunction boundsPoint(x, y) {\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (boundsStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcGF0aC9ib3VuZHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEI7QUFFOUIsSUFBSUMsS0FBS0MsVUFDTEMsS0FBS0YsSUFDTEcsS0FBSyxDQUFDSCxJQUNOSSxLQUFLRDtBQUVULElBQUlFLGVBQWU7SUFDakJDLE9BQU9DO0lBQ1BDLFdBQVdULGdEQUFJQTtJQUNmVSxTQUFTVixnREFBSUE7SUFDYlcsY0FBY1gsZ0RBQUlBO0lBQ2xCWSxZQUFZWixnREFBSUE7SUFDaEJhLFFBQVE7UUFDTixJQUFJQyxTQUFTO1lBQUM7Z0JBQUNiO2dCQUFJRTthQUFHO1lBQUU7Z0JBQUNDO2dCQUFJQzthQUFHO1NBQUM7UUFDakNELEtBQUtDLEtBQUssQ0FBRUYsQ0FBQUEsS0FBS0YsS0FBS0MsUUFBTztRQUM3QixPQUFPWTtJQUNUO0FBQ0Y7QUFFQSxTQUFTTixZQUFZTyxDQUFDLEVBQUVDLENBQUM7SUFDdkIsSUFBSUQsSUFBSWQsSUFBSUEsS0FBS2M7SUFDakIsSUFBSUEsSUFBSVgsSUFBSUEsS0FBS1c7SUFDakIsSUFBSUMsSUFBSWIsSUFBSUEsS0FBS2E7SUFDakIsSUFBSUEsSUFBSVgsSUFBSUEsS0FBS1c7QUFDbkI7QUFFQSxpRUFBZVYsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3BhdGgvYm91bmRzLmpzP2E4MDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG5vb3AgZnJvbSBcIi4uL25vb3AuanNcIjtcblxudmFyIHgwID0gSW5maW5pdHksXG4gICAgeTAgPSB4MCxcbiAgICB4MSA9IC14MCxcbiAgICB5MSA9IHgxO1xuXG52YXIgYm91bmRzU3RyZWFtID0ge1xuICBwb2ludDogYm91bmRzUG9pbnQsXG4gIGxpbmVTdGFydDogbm9vcCxcbiAgbGluZUVuZDogbm9vcCxcbiAgcG9seWdvblN0YXJ0OiBub29wLFxuICBwb2x5Z29uRW5kOiBub29wLFxuICByZXN1bHQ6IGZ1bmN0aW9uKCkge1xuICAgIHZhciBib3VuZHMgPSBbW3gwLCB5MF0sIFt4MSwgeTFdXTtcbiAgICB4MSA9IHkxID0gLSh5MCA9IHgwID0gSW5maW5pdHkpO1xuICAgIHJldHVybiBib3VuZHM7XG4gIH1cbn07XG5cbmZ1bmN0aW9uIGJvdW5kc1BvaW50KHgsIHkpIHtcbiAgaWYgKHggPCB4MCkgeDAgPSB4O1xuICBpZiAoeCA+IHgxKSB4MSA9IHg7XG4gIGlmICh5IDwgeTApIHkwID0geTtcbiAgaWYgKHkgPiB5MSkgeTEgPSB5O1xufVxuXG5leHBvcnQgZGVmYXVsdCBib3VuZHNTdHJlYW07XG4iXSwibmFtZXMiOlsibm9vcCIsIngwIiwiSW5maW5pdHkiLCJ5MCIsIngxIiwieTEiLCJib3VuZHNTdHJlYW0iLCJwb2ludCIsImJvdW5kc1BvaW50IiwibGluZVN0YXJ0IiwibGluZUVuZCIsInBvbHlnb25TdGFydCIsInBvbHlnb25FbmQiLCJyZXN1bHQiLCJib3VuZHMiLCJ4IiwieSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/path/bounds.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/path/centroid.js":
/*!******************************************************!*\
  !*** ../../node_modules/d3-geo/src/path/centroid.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n// TODO Enforce positive area for exterior, negative area for interior?\nvar X0 = 0, Y0 = 0, Z0 = 0, X1 = 0, Y1 = 0, Z1 = 0, X2 = 0, Y2 = 0, Z2 = 0, x00, y00, x0, y0;\nvar centroidStream = {\n    point: centroidPoint,\n    lineStart: centroidLineStart,\n    lineEnd: centroidLineEnd,\n    polygonStart: function() {\n        centroidStream.lineStart = centroidRingStart;\n        centroidStream.lineEnd = centroidRingEnd;\n    },\n    polygonEnd: function() {\n        centroidStream.point = centroidPoint;\n        centroidStream.lineStart = centroidLineStart;\n        centroidStream.lineEnd = centroidLineEnd;\n    },\n    result: function() {\n        var centroid = Z2 ? [\n            X2 / Z2,\n            Y2 / Z2\n        ] : Z1 ? [\n            X1 / Z1,\n            Y1 / Z1\n        ] : Z0 ? [\n            X0 / Z0,\n            Y0 / Z0\n        ] : [\n            NaN,\n            NaN\n        ];\n        X0 = Y0 = Z0 = X1 = Y1 = Z1 = X2 = Y2 = Z2 = 0;\n        return centroid;\n    }\n};\nfunction centroidPoint(x, y) {\n    X0 += x;\n    Y0 += y;\n    ++Z0;\n}\nfunction centroidLineStart() {\n    centroidStream.point = centroidPointFirstLine;\n}\nfunction centroidPointFirstLine(x, y) {\n    centroidStream.point = centroidPointLine;\n    centroidPoint(x0 = x, y0 = y);\n}\nfunction centroidPointLine(x, y) {\n    var dx = x - x0, dy = y - y0, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(dx * dx + dy * dy);\n    X1 += z * (x0 + x) / 2;\n    Y1 += z * (y0 + y) / 2;\n    Z1 += z;\n    centroidPoint(x0 = x, y0 = y);\n}\nfunction centroidLineEnd() {\n    centroidStream.point = centroidPoint;\n}\nfunction centroidRingStart() {\n    centroidStream.point = centroidPointFirstRing;\n}\nfunction centroidRingEnd() {\n    centroidPointRing(x00, y00);\n}\nfunction centroidPointFirstRing(x, y) {\n    centroidStream.point = centroidPointRing;\n    centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\nfunction centroidPointRing(x, y) {\n    var dx = x - x0, dy = y - y0, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(dx * dx + dy * dy);\n    X1 += z * (x0 + x) / 2;\n    Y1 += z * (y0 + y) / 2;\n    Z1 += z;\n    z = y0 * x - x0 * y;\n    X2 += z * (x0 + x);\n    Y2 += z * (y0 + y);\n    Z2 += z * 3;\n    centroidPoint(x0 = x, y0 = y);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (centroidStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/path/centroid.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/path/context.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-geo/src/path/context.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PathContext)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n\n\nfunction PathContext(context) {\n    this._context = context;\n}\nPathContext.prototype = {\n    _radius: 4.5,\n    pointRadius: function(_) {\n        return this._radius = _, this;\n    },\n    polygonStart: function() {\n        this._line = 0;\n    },\n    polygonEnd: function() {\n        this._line = NaN;\n    },\n    lineStart: function() {\n        this._point = 0;\n    },\n    lineEnd: function() {\n        if (this._line === 0) this._context.closePath();\n        this._point = NaN;\n    },\n    point: function(x, y) {\n        switch(this._point){\n            case 0:\n                {\n                    this._context.moveTo(x, y);\n                    this._point = 1;\n                    break;\n                }\n            case 1:\n                {\n                    this._context.lineTo(x, y);\n                    break;\n                }\n            default:\n                {\n                    this._context.moveTo(x + this._radius, y);\n                    this._context.arc(x, y, this._radius, 0, _math_js__WEBPACK_IMPORTED_MODULE_0__.tau);\n                    break;\n                }\n        }\n    },\n    result: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/path/context.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/path/index.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-geo/src/path/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/../../node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stream.js */ \"(ssr)/../../node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./area.js */ \"(ssr)/../../node_modules/d3-geo/src/path/area.js\");\n/* harmony import */ var _bounds_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bounds.js */ \"(ssr)/../../node_modules/d3-geo/src/path/bounds.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/../../node_modules/d3-geo/src/path/centroid.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./context.js */ \"(ssr)/../../node_modules/d3-geo/src/path/context.js\");\n/* harmony import */ var _measure_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./measure.js */ \"(ssr)/../../node_modules/d3-geo/src/path/measure.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./string.js */ \"(ssr)/../../node_modules/d3-geo/src/path/string.js\");\n\n\n\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(projection, context) {\n    let digits = 3, pointRadius = 4.5, projectionStream, contextStream;\n    function path(object) {\n        if (object) {\n            if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n            (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(contextStream));\n        }\n        return contextStream.result();\n    }\n    path.area = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_area_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n        return _area_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].result();\n    };\n    path.measure = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_measure_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]));\n        return _measure_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].result();\n    };\n    path.bounds = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_bounds_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]));\n        return _bounds_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].result();\n    };\n    path.centroid = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_centroid_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]));\n        return _centroid_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].result();\n    };\n    path.projection = function(_) {\n        if (!arguments.length) return projection;\n        projectionStream = _ == null ? (projection = null, _identity_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]) : (projection = _).stream;\n        return path;\n    };\n    path.context = function(_) {\n        if (!arguments.length) return context;\n        contextStream = _ == null ? (context = null, new _string_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"](digits)) : new _context_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"](context = _);\n        if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n        return path;\n    };\n    path.pointRadius = function(_) {\n        if (!arguments.length) return pointRadius;\n        pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n        return path;\n    };\n    path.digits = function(_) {\n        if (!arguments.length) return digits;\n        if (_ == null) digits = null;\n        else {\n            const d = Math.floor(_);\n            if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n            digits = d;\n        }\n        if (context === null) contextStream = new _string_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"](digits);\n        return path;\n    };\n    return path.projection(projection).digits(digits).context(context);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/path/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/path/measure.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-geo/src/path/measure.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/../../node_modules/d3-geo/src/noop.js\");\n\n\n\nvar lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), lengthRing, x00, y00, x0, y0;\nvar lengthStream = {\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineStart: function() {\n        lengthStream.point = lengthPointFirst;\n    },\n    lineEnd: function() {\n        if (lengthRing) lengthPoint(x00, y00);\n        lengthStream.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n    },\n    polygonStart: function() {\n        lengthRing = true;\n    },\n    polygonEnd: function() {\n        lengthRing = null;\n    },\n    result: function() {\n        var length = +lengthSum;\n        lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        return length;\n    }\n};\nfunction lengthPointFirst(x, y) {\n    lengthStream.point = lengthPoint;\n    x00 = x0 = x, y00 = y0 = y;\n}\nfunction lengthPoint(x, y) {\n    x0 -= x, y0 -= y;\n    lengthSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sqrt)(x0 * x0 + y0 * y0));\n    x0 = x, y0 = y;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (lengthStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/path/measure.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/path/string.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-geo/src/path/string.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PathString)\n/* harmony export */ });\n// Simple caching for constant-radius points.\nlet cacheDigits, cacheAppend, cacheRadius, cacheCircle;\nclass PathString {\n    constructor(digits){\n        this._append = digits == null ? append : appendRound(digits);\n        this._radius = 4.5;\n        this._ = \"\";\n    }\n    pointRadius(_) {\n        this._radius = +_;\n        return this;\n    }\n    polygonStart() {\n        this._line = 0;\n    }\n    polygonEnd() {\n        this._line = NaN;\n    }\n    lineStart() {\n        this._point = 0;\n    }\n    lineEnd() {\n        if (this._line === 0) this._ += \"Z\";\n        this._point = NaN;\n    }\n    point(x, y) {\n        switch(this._point){\n            case 0:\n                {\n                    this._append`M${x},${y}`;\n                    this._point = 1;\n                    break;\n                }\n            case 1:\n                {\n                    this._append`L${x},${y}`;\n                    break;\n                }\n            default:\n                {\n                    this._append`M${x},${y}`;\n                    if (this._radius !== cacheRadius || this._append !== cacheAppend) {\n                        const r = this._radius;\n                        const s = this._;\n                        this._ = \"\"; // stash the old string so we can cache the circle path fragment\n                        this._append`m0,${r}a${r},${r} 0 1,1 0,${-2 * r}a${r},${r} 0 1,1 0,${2 * r}z`;\n                        cacheRadius = r;\n                        cacheAppend = this._append;\n                        cacheCircle = this._;\n                        this._ = s;\n                    }\n                    this._ += cacheCircle;\n                    break;\n                }\n        }\n    }\n    result() {\n        const result = this._;\n        this._ = \"\";\n        return result.length ? result : null;\n    }\n}\nfunction append(strings) {\n    let i = 1;\n    this._ += strings[0];\n    for(const j = strings.length; i < j; ++i){\n        this._ += arguments[i] + strings[i];\n    }\n}\nfunction appendRound(digits) {\n    const d = Math.floor(digits);\n    if (!(d >= 0)) throw new RangeError(`invalid digits: ${digits}`);\n    if (d > 15) return append;\n    if (d !== cacheDigits) {\n        const k = 10 ** d;\n        cacheDigits = d;\n        cacheAppend = function append(strings) {\n            let i = 1;\n            this._ += strings[0];\n            for(const j = strings.length; i < j; ++i){\n                this._ += Math.round(arguments[i] * k) / k + strings[i];\n            }\n        };\n    }\n    return cacheAppend;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcGF0aC9zdHJpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZDQUE2QztBQUM3QyxJQUFJQSxhQUFhQyxhQUFhQyxhQUFhQztBQUU1QixNQUFNQztJQUNuQkMsWUFBWUMsTUFBTSxDQUFFO1FBQ2xCLElBQUksQ0FBQ0MsT0FBTyxHQUFHRCxVQUFVLE9BQU9FLFNBQVNDLFlBQVlIO1FBQ3JELElBQUksQ0FBQ0ksT0FBTyxHQUFHO1FBQ2YsSUFBSSxDQUFDQyxDQUFDLEdBQUc7SUFDWDtJQUNBQyxZQUFZRCxDQUFDLEVBQUU7UUFDYixJQUFJLENBQUNELE9BQU8sR0FBRyxDQUFDQztRQUNoQixPQUFPLElBQUk7SUFDYjtJQUNBRSxlQUFlO1FBQ2IsSUFBSSxDQUFDQyxLQUFLLEdBQUc7SUFDZjtJQUNBQyxhQUFhO1FBQ1gsSUFBSSxDQUFDRCxLQUFLLEdBQUdFO0lBQ2Y7SUFDQUMsWUFBWTtRQUNWLElBQUksQ0FBQ0MsTUFBTSxHQUFHO0lBQ2hCO0lBQ0FDLFVBQVU7UUFDUixJQUFJLElBQUksQ0FBQ0wsS0FBSyxLQUFLLEdBQUcsSUFBSSxDQUFDSCxDQUFDLElBQUk7UUFDaEMsSUFBSSxDQUFDTyxNQUFNLEdBQUdGO0lBQ2hCO0lBQ0FJLE1BQU1DLENBQUMsRUFBRUMsQ0FBQyxFQUFFO1FBQ1YsT0FBUSxJQUFJLENBQUNKLE1BQU07WUFDakIsS0FBSztnQkFBRztvQkFDTixJQUFJLENBQUNYLE9BQU8sQ0FBQyxDQUFDLEVBQUVjLEVBQUUsQ0FBQyxFQUFFQyxFQUFFLENBQUM7b0JBQ3hCLElBQUksQ0FBQ0osTUFBTSxHQUFHO29CQUNkO2dCQUNGO1lBQ0EsS0FBSztnQkFBRztvQkFDTixJQUFJLENBQUNYLE9BQU8sQ0FBQyxDQUFDLEVBQUVjLEVBQUUsQ0FBQyxFQUFFQyxFQUFFLENBQUM7b0JBQ3hCO2dCQUNGO1lBQ0E7Z0JBQVM7b0JBQ1AsSUFBSSxDQUFDZixPQUFPLENBQUMsQ0FBQyxFQUFFYyxFQUFFLENBQUMsRUFBRUMsRUFBRSxDQUFDO29CQUN4QixJQUFJLElBQUksQ0FBQ1osT0FBTyxLQUFLUixlQUFlLElBQUksQ0FBQ0ssT0FBTyxLQUFLTixhQUFhO3dCQUNoRSxNQUFNc0IsSUFBSSxJQUFJLENBQUNiLE9BQU87d0JBQ3RCLE1BQU1jLElBQUksSUFBSSxDQUFDYixDQUFDO3dCQUNoQixJQUFJLENBQUNBLENBQUMsR0FBRyxJQUFJLGdFQUFnRTt3QkFDN0UsSUFBSSxDQUFDSixPQUFPLENBQUMsR0FBRyxFQUFFZ0IsRUFBRSxDQUFDLEVBQUVBLEVBQUUsQ0FBQyxFQUFFQSxFQUFFLFNBQVMsRUFBRSxDQUFDLElBQUlBLEVBQUUsQ0FBQyxFQUFFQSxFQUFFLENBQUMsRUFBRUEsRUFBRSxTQUFTLEVBQUUsSUFBSUEsRUFBRSxDQUFDLENBQUM7d0JBQzdFckIsY0FBY3FCO3dCQUNkdEIsY0FBYyxJQUFJLENBQUNNLE9BQU87d0JBQzFCSixjQUFjLElBQUksQ0FBQ1EsQ0FBQzt3QkFDcEIsSUFBSSxDQUFDQSxDQUFDLEdBQUdhO29CQUNYO29CQUNBLElBQUksQ0FBQ2IsQ0FBQyxJQUFJUjtvQkFDVjtnQkFDRjtRQUNGO0lBQ0Y7SUFDQXNCLFNBQVM7UUFDUCxNQUFNQSxTQUFTLElBQUksQ0FBQ2QsQ0FBQztRQUNyQixJQUFJLENBQUNBLENBQUMsR0FBRztRQUNULE9BQU9jLE9BQU9DLE1BQU0sR0FBR0QsU0FBUztJQUNsQztBQUNGO0FBRUEsU0FBU2pCLE9BQU9tQixPQUFPO0lBQ3JCLElBQUlDLElBQUk7SUFDUixJQUFJLENBQUNqQixDQUFDLElBQUlnQixPQUFPLENBQUMsRUFBRTtJQUNwQixJQUFLLE1BQU1FLElBQUlGLFFBQVFELE1BQU0sRUFBRUUsSUFBSUMsR0FBRyxFQUFFRCxFQUFHO1FBQ3pDLElBQUksQ0FBQ2pCLENBQUMsSUFBSW1CLFNBQVMsQ0FBQ0YsRUFBRSxHQUFHRCxPQUFPLENBQUNDLEVBQUU7SUFDckM7QUFDRjtBQUVBLFNBQVNuQixZQUFZSCxNQUFNO0lBQ3pCLE1BQU15QixJQUFJQyxLQUFLQyxLQUFLLENBQUMzQjtJQUNyQixJQUFJLENBQUV5QixDQUFBQSxLQUFLLElBQUksTUFBTSxJQUFJRyxXQUFXLENBQUMsZ0JBQWdCLEVBQUU1QixPQUFPLENBQUM7SUFDL0QsSUFBSXlCLElBQUksSUFBSSxPQUFPdkI7SUFDbkIsSUFBSXVCLE1BQU0vQixhQUFhO1FBQ3JCLE1BQU1tQyxJQUFJLE1BQU1KO1FBQ2hCL0IsY0FBYytCO1FBQ2Q5QixjQUFjLFNBQVNPLE9BQU9tQixPQUFPO1lBQ25DLElBQUlDLElBQUk7WUFDUixJQUFJLENBQUNqQixDQUFDLElBQUlnQixPQUFPLENBQUMsRUFBRTtZQUNwQixJQUFLLE1BQU1FLElBQUlGLFFBQVFELE1BQU0sRUFBRUUsSUFBSUMsR0FBRyxFQUFFRCxFQUFHO2dCQUN6QyxJQUFJLENBQUNqQixDQUFDLElBQUlxQixLQUFLSSxLQUFLLENBQUNOLFNBQVMsQ0FBQ0YsRUFBRSxHQUFHTyxLQUFLQSxJQUFJUixPQUFPLENBQUNDLEVBQUU7WUFDekQ7UUFDRjtJQUNGO0lBQ0EsT0FBTzNCO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wYXRoL3N0cmluZy5qcz9kYTg2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSBjYWNoaW5nIGZvciBjb25zdGFudC1yYWRpdXMgcG9pbnRzLlxubGV0IGNhY2hlRGlnaXRzLCBjYWNoZUFwcGVuZCwgY2FjaGVSYWRpdXMsIGNhY2hlQ2lyY2xlO1xuXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBQYXRoU3RyaW5nIHtcbiAgY29uc3RydWN0b3IoZGlnaXRzKSB7XG4gICAgdGhpcy5fYXBwZW5kID0gZGlnaXRzID09IG51bGwgPyBhcHBlbmQgOiBhcHBlbmRSb3VuZChkaWdpdHMpO1xuICAgIHRoaXMuX3JhZGl1cyA9IDQuNTtcbiAgICB0aGlzLl8gPSBcIlwiO1xuICB9XG4gIHBvaW50UmFkaXVzKF8pIHtcbiAgICB0aGlzLl9yYWRpdXMgPSArXztcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBwb2x5Z29uU3RhcnQoKSB7XG4gICAgdGhpcy5fbGluZSA9IDA7XG4gIH1cbiAgcG9seWdvbkVuZCgpIHtcbiAgICB0aGlzLl9saW5lID0gTmFOO1xuICB9XG4gIGxpbmVTdGFydCgpIHtcbiAgICB0aGlzLl9wb2ludCA9IDA7XG4gIH1cbiAgbGluZUVuZCgpIHtcbiAgICBpZiAodGhpcy5fbGluZSA9PT0gMCkgdGhpcy5fICs9IFwiWlwiO1xuICAgIHRoaXMuX3BvaW50ID0gTmFOO1xuICB9XG4gIHBvaW50KHgsIHkpIHtcbiAgICBzd2l0Y2ggKHRoaXMuX3BvaW50KSB7XG4gICAgICBjYXNlIDA6IHtcbiAgICAgICAgdGhpcy5fYXBwZW5kYE0ke3h9LCR7eX1gO1xuICAgICAgICB0aGlzLl9wb2ludCA9IDE7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY2FzZSAxOiB7XG4gICAgICAgIHRoaXMuX2FwcGVuZGBMJHt4fSwke3l9YDtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBkZWZhdWx0OiB7XG4gICAgICAgIHRoaXMuX2FwcGVuZGBNJHt4fSwke3l9YDtcbiAgICAgICAgaWYgKHRoaXMuX3JhZGl1cyAhPT0gY2FjaGVSYWRpdXMgfHwgdGhpcy5fYXBwZW5kICE9PSBjYWNoZUFwcGVuZCkge1xuICAgICAgICAgIGNvbnN0IHIgPSB0aGlzLl9yYWRpdXM7XG4gICAgICAgICAgY29uc3QgcyA9IHRoaXMuXztcbiAgICAgICAgICB0aGlzLl8gPSBcIlwiOyAvLyBzdGFzaCB0aGUgb2xkIHN0cmluZyBzbyB3ZSBjYW4gY2FjaGUgdGhlIGNpcmNsZSBwYXRoIGZyYWdtZW50XG4gICAgICAgICAgdGhpcy5fYXBwZW5kYG0wLCR7cn1hJHtyfSwke3J9IDAgMSwxIDAsJHstMiAqIHJ9YSR7cn0sJHtyfSAwIDEsMSAwLCR7MiAqIHJ9emA7XG4gICAgICAgICAgY2FjaGVSYWRpdXMgPSByO1xuICAgICAgICAgIGNhY2hlQXBwZW5kID0gdGhpcy5fYXBwZW5kO1xuICAgICAgICAgIGNhY2hlQ2lyY2xlID0gdGhpcy5fO1xuICAgICAgICAgIHRoaXMuXyA9IHM7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fICs9IGNhY2hlQ2lyY2xlO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmVzdWx0KCkge1xuICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuXztcbiAgICB0aGlzLl8gPSBcIlwiO1xuICAgIHJldHVybiByZXN1bHQubGVuZ3RoID8gcmVzdWx0IDogbnVsbDtcbiAgfVxufVxuXG5mdW5jdGlvbiBhcHBlbmQoc3RyaW5ncykge1xuICBsZXQgaSA9IDE7XG4gIHRoaXMuXyArPSBzdHJpbmdzWzBdO1xuICBmb3IgKGNvbnN0IGogPSBzdHJpbmdzLmxlbmd0aDsgaSA8IGo7ICsraSkge1xuICAgIHRoaXMuXyArPSBhcmd1bWVudHNbaV0gKyBzdHJpbmdzW2ldO1xuICB9XG59XG5cbmZ1bmN0aW9uIGFwcGVuZFJvdW5kKGRpZ2l0cykge1xuICBjb25zdCBkID0gTWF0aC5mbG9vcihkaWdpdHMpO1xuICBpZiAoIShkID49IDApKSB0aHJvdyBuZXcgUmFuZ2VFcnJvcihgaW52YWxpZCBkaWdpdHM6ICR7ZGlnaXRzfWApO1xuICBpZiAoZCA+IDE1KSByZXR1cm4gYXBwZW5kO1xuICBpZiAoZCAhPT0gY2FjaGVEaWdpdHMpIHtcbiAgICBjb25zdCBrID0gMTAgKiogZDtcbiAgICBjYWNoZURpZ2l0cyA9IGQ7XG4gICAgY2FjaGVBcHBlbmQgPSBmdW5jdGlvbiBhcHBlbmQoc3RyaW5ncykge1xuICAgICAgbGV0IGkgPSAxO1xuICAgICAgdGhpcy5fICs9IHN0cmluZ3NbMF07XG4gICAgICBmb3IgKGNvbnN0IGogPSBzdHJpbmdzLmxlbmd0aDsgaSA8IGo7ICsraSkge1xuICAgICAgICB0aGlzLl8gKz0gTWF0aC5yb3VuZChhcmd1bWVudHNbaV0gKiBrKSAvIGsgKyBzdHJpbmdzW2ldO1xuICAgICAgfVxuICAgIH07XG4gIH1cbiAgcmV0dXJuIGNhY2hlQXBwZW5kO1xufVxuIl0sIm5hbWVzIjpbImNhY2hlRGlnaXRzIiwiY2FjaGVBcHBlbmQiLCJjYWNoZVJhZGl1cyIsImNhY2hlQ2lyY2xlIiwiUGF0aFN0cmluZyIsImNvbnN0cnVjdG9yIiwiZGlnaXRzIiwiX2FwcGVuZCIsImFwcGVuZCIsImFwcGVuZFJvdW5kIiwiX3JhZGl1cyIsIl8iLCJwb2ludFJhZGl1cyIsInBvbHlnb25TdGFydCIsIl9saW5lIiwicG9seWdvbkVuZCIsIk5hTiIsImxpbmVTdGFydCIsIl9wb2ludCIsImxpbmVFbmQiLCJwb2ludCIsIngiLCJ5IiwiciIsInMiLCJyZXN1bHQiLCJsZW5ndGgiLCJzdHJpbmdzIiwiaSIsImoiLCJhcmd1bWVudHMiLCJkIiwiTWF0aCIsImZsb29yIiwiUmFuZ2VFcnJvciIsImsiLCJyb3VuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/path/string.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/pointEqual.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-geo/src/pointEqual.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(a[0] - b[0]) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(a[1] - b[1]) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcG9pbnRFcXVhbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUV2Qyw2QkFBZSxvQ0FBU0UsQ0FBQyxFQUFFQyxDQUFDO0lBQzFCLE9BQU9ILDZDQUFHQSxDQUFDRSxDQUFDLENBQUMsRUFBRSxHQUFHQyxDQUFDLENBQUMsRUFBRSxJQUFJRiw2Q0FBT0EsSUFBSUQsNkNBQUdBLENBQUNFLENBQUMsQ0FBQyxFQUFFLEdBQUdDLENBQUMsQ0FBQyxFQUFFLElBQUlGLDZDQUFPQTtBQUNqRSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3BvaW50RXF1YWwuanM/NWU3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2FicywgZXBzaWxvbn0gZnJvbSBcIi4vbWF0aC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihhLCBiKSB7XG4gIHJldHVybiBhYnMoYVswXSAtIGJbMF0pIDwgZXBzaWxvbiAmJiBhYnMoYVsxXSAtIGJbMV0pIDwgZXBzaWxvbjtcbn1cbiJdLCJuYW1lcyI6WyJhYnMiLCJlcHNpbG9uIiwiYSIsImIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/pointEqual.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/polygonContains.js":
/*!********************************************************!*\
  !*** ../../node_modules/d3-geo/src/polygonContains.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/../../node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\n\nfunction longitude(point) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(point[0]) <= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi ? point[0] : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(point[0]) * (((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(point[0]) + _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) % _math_js__WEBPACK_IMPORTED_MODULE_0__.tau - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi);\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon, point) {\n    var lambda = longitude(point), phi = point[1], sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi), normal = [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda),\n        -(0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda),\n        0\n    ], angle = 0, winding = 0;\n    var sum = new d3_array__WEBPACK_IMPORTED_MODULE_1__.Adder();\n    if (sinPhi === 1) phi = _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n    else if (sinPhi === -1) phi = -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n    for(var i = 0, n = polygon.length; i < n; ++i){\n        if (!(m = (ring = polygon[i]).length)) continue;\n        var ring, m, point0 = ring[m - 1], lambda0 = longitude(point0), phi0 = point0[1] / 2 + _math_js__WEBPACK_IMPORTED_MODULE_0__.quarterPi, sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi0), cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi0);\n        for(var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1){\n            var point1 = ring[j], lambda1 = longitude(point1), phi1 = point1[1] / 2 + _math_js__WEBPACK_IMPORTED_MODULE_0__.quarterPi, sinPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi1), cosPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi1), delta = lambda1 - lambda0, sign = delta >= 0 ? 1 : -1, absDelta = sign * delta, antimeridian = absDelta > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi, k = sinPhi0 * sinPhi1;\n            sum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(k * sign * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(absDelta), cosPhi0 * cosPhi1 + k * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(absDelta)));\n            angle += antimeridian ? delta + sign * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau : delta;\n            // Are the longitudes either side of the point’s meridian (lambda),\n            // and are the latitudes smaller than the parallel (phi)?\n            if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n                var arc = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianCross)((0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)(point0), (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)(point1));\n                (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianNormalizeInPlace)(arc);\n                var intersection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianCross)(normal, arc);\n                (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianNormalizeInPlace)(intersection);\n                var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(intersection[2]);\n                if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n                    winding += antimeridian ^ delta >= 0 ? 1 : -1;\n                }\n            }\n        }\n    }\n    // First, determine whether the South pole is inside or outside:\n    //\n    // It is inside if:\n    // * the polygon winds around it in a clockwise direction.\n    // * the polygon does not (cumulatively) wind around it, but has a negative\n    //   (counter-clockwise) area.\n    //\n    // Second, count the (signed) number of times a segment crosses a lambda\n    // from the point to the South pole.  If it is zero, then the point is the\n    // same side as the South pole.\n    return (angle < -_math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon || angle < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && sum < -_math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon2) ^ winding & 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/polygonContains.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/albers.js":
/*!**********************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/albers.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _conicEqualArea_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./conicEqualArea.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conicEqualArea.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().parallels([\n        29.5,\n        45.5\n    ]).scale(1070).translate([\n        480,\n        250\n    ]).rotate([\n        96,\n        0\n    ]).center([\n        -0.6,\n        38.7\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9hbGJlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUQ7QUFFakQsNkJBQWUsc0NBQVc7SUFDeEIsT0FBT0EsOERBQWNBLEdBQ2hCQyxTQUFTLENBQUM7UUFBQztRQUFNO0tBQUssRUFDdEJDLEtBQUssQ0FBQyxNQUNOQyxTQUFTLENBQUM7UUFBQztRQUFLO0tBQUksRUFDcEJDLE1BQU0sQ0FBQztRQUFDO1FBQUk7S0FBRSxFQUNkQyxNQUFNLENBQUM7UUFBQyxDQUFDO1FBQUs7S0FBSztBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vYWxiZXJzLmpzP2IwODIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvbmljRXF1YWxBcmVhIGZyb20gXCIuL2NvbmljRXF1YWxBcmVhLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gY29uaWNFcXVhbEFyZWEoKVxuICAgICAgLnBhcmFsbGVscyhbMjkuNSwgNDUuNV0pXG4gICAgICAuc2NhbGUoMTA3MClcbiAgICAgIC50cmFuc2xhdGUoWzQ4MCwgMjUwXSlcbiAgICAgIC5yb3RhdGUoWzk2LCAwXSlcbiAgICAgIC5jZW50ZXIoWy0wLjYsIDM4LjddKTtcbn1cbiJdLCJuYW1lcyI6WyJjb25pY0VxdWFsQXJlYSIsInBhcmFsbGVscyIsInNjYWxlIiwidHJhbnNsYXRlIiwicm90YXRlIiwiY2VudGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/albers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/albersUsa.js":
/*!*************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/albersUsa.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _albers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./albers.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/albers.js\");\n/* harmony import */ var _conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./conicEqualArea.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conicEqualArea.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/fit.js\");\n\n\n\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n    var n = streams.length;\n    return {\n        point: function(x, y) {\n            var i = -1;\n            while(++i < n)streams[i].point(x, y);\n        },\n        sphere: function() {\n            var i = -1;\n            while(++i < n)streams[i].sphere();\n        },\n        lineStart: function() {\n            var i = -1;\n            while(++i < n)streams[i].lineStart();\n        },\n        lineEnd: function() {\n            var i = -1;\n            while(++i < n)streams[i].lineEnd();\n        },\n        polygonStart: function() {\n            var i = -1;\n            while(++i < n)streams[i].polygonStart();\n        },\n        polygonEnd: function() {\n            var i = -1;\n            while(++i < n)streams[i].polygonEnd();\n        }\n    };\n}\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var cache, cacheStream, lower48 = (0,_albers_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), lower48Point, alaska = (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])().rotate([\n        154,\n        0\n    ]).center([\n        -2,\n        58.5\n    ]).parallels([\n        55,\n        65\n    ]), alaskaPoint, hawaii = (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])().rotate([\n        157,\n        0\n    ]).center([\n        -3,\n        19.9\n    ]).parallels([\n        8,\n        18\n    ]), hawaiiPoint, point, pointStream = {\n        point: function(x, y) {\n            point = [\n                x,\n                y\n            ];\n        }\n    };\n    function albersUsa(coordinates) {\n        var x = coordinates[0], y = coordinates[1];\n        return point = null, (lower48Point.point(x, y), point) || (alaskaPoint.point(x, y), point) || (hawaiiPoint.point(x, y), point);\n    }\n    albersUsa.invert = function(coordinates) {\n        var k = lower48.scale(), t = lower48.translate(), x = (coordinates[0] - t[0]) / k, y = (coordinates[1] - t[1]) / k;\n        return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii : lower48).invert(coordinates);\n    };\n    albersUsa.stream = function(stream) {\n        return cache && cacheStream === stream ? cache : cache = multiplex([\n            lower48.stream(cacheStream = stream),\n            alaska.stream(stream),\n            hawaii.stream(stream)\n        ]);\n    };\n    albersUsa.precision = function(_) {\n        if (!arguments.length) return lower48.precision();\n        lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n        return reset();\n    };\n    albersUsa.scale = function(_) {\n        if (!arguments.length) return lower48.scale();\n        lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n        return albersUsa.translate(lower48.translate());\n    };\n    albersUsa.translate = function(_) {\n        if (!arguments.length) return lower48.translate();\n        var k = lower48.scale(), x = +_[0], y = +_[1];\n        lower48Point = lower48.translate(_).clipExtent([\n            [\n                x - 0.455 * k,\n                y - 0.238 * k\n            ],\n            [\n                x + 0.455 * k,\n                y + 0.238 * k\n            ]\n        ]).stream(pointStream);\n        alaskaPoint = alaska.translate([\n            x - 0.307 * k,\n            y + 0.201 * k\n        ]).clipExtent([\n            [\n                x - 0.425 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.120 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ],\n            [\n                x - 0.214 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.234 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ]\n        ]).stream(pointStream);\n        hawaiiPoint = hawaii.translate([\n            x - 0.205 * k,\n            y + 0.212 * k\n        ]).clipExtent([\n            [\n                x - 0.214 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.166 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ],\n            [\n                x - 0.115 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.234 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ]\n        ]).stream(pointStream);\n        return reset();\n    };\n    albersUsa.fitExtent = function(extent, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitExtent)(albersUsa, extent, object);\n    };\n    albersUsa.fitSize = function(size, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitSize)(albersUsa, size, object);\n    };\n    albersUsa.fitWidth = function(width, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitWidth)(albersUsa, width, object);\n    };\n    albersUsa.fitHeight = function(height, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitHeight)(albersUsa, height, object);\n    };\n    function reset() {\n        cache = cacheStream = null;\n        return albersUsa;\n    }\n    return albersUsa.scale(1070);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9hbGJlcnNVc2EuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUM7QUFDRjtBQUNnQjtBQUNnQjtBQUVqRSwyRUFBMkU7QUFDM0UsK0RBQStEO0FBQy9ELFNBQVNPLFVBQVVDLE9BQU87SUFDeEIsSUFBSUMsSUFBSUQsUUFBUUUsTUFBTTtJQUN0QixPQUFPO1FBQ0xDLE9BQU8sU0FBU0MsQ0FBQyxFQUFFQyxDQUFDO1lBQUksSUFBSUMsSUFBSSxDQUFDO1lBQUcsTUFBTyxFQUFFQSxJQUFJTCxFQUFHRCxPQUFPLENBQUNNLEVBQUUsQ0FBQ0gsS0FBSyxDQUFDQyxHQUFHQztRQUFJO1FBQzVFRSxRQUFRO1lBQWEsSUFBSUQsSUFBSSxDQUFDO1lBQUcsTUFBTyxFQUFFQSxJQUFJTCxFQUFHRCxPQUFPLENBQUNNLEVBQUUsQ0FBQ0MsTUFBTTtRQUFJO1FBQ3RFQyxXQUFXO1lBQWEsSUFBSUYsSUFBSSxDQUFDO1lBQUcsTUFBTyxFQUFFQSxJQUFJTCxFQUFHRCxPQUFPLENBQUNNLEVBQUUsQ0FBQ0UsU0FBUztRQUFJO1FBQzVFQyxTQUFTO1lBQWEsSUFBSUgsSUFBSSxDQUFDO1lBQUcsTUFBTyxFQUFFQSxJQUFJTCxFQUFHRCxPQUFPLENBQUNNLEVBQUUsQ0FBQ0csT0FBTztRQUFJO1FBQ3hFQyxjQUFjO1lBQWEsSUFBSUosSUFBSSxDQUFDO1lBQUcsTUFBTyxFQUFFQSxJQUFJTCxFQUFHRCxPQUFPLENBQUNNLEVBQUUsQ0FBQ0ksWUFBWTtRQUFJO1FBQ2xGQyxZQUFZO1lBQWEsSUFBSUwsSUFBSSxDQUFDO1lBQUcsTUFBTyxFQUFFQSxJQUFJTCxFQUFHRCxPQUFPLENBQUNNLEVBQUUsQ0FBQ0ssVUFBVTtRQUFJO0lBQ2hGO0FBQ0Y7QUFFQSwwRUFBMEU7QUFDMUUsNkVBQTZFO0FBQzdFLDBFQUEwRTtBQUMxRSxzRUFBc0U7QUFDdEUsdUVBQXVFO0FBQ3ZFLDZCQUFlLHNDQUFXO0lBQ3hCLElBQUlDLE9BQ0FDLGFBQ0FDLFVBQVVyQixzREFBTUEsSUFBSXNCLGNBQ3BCQyxTQUFTdEIsOERBQWNBLEdBQUd1QixNQUFNLENBQUM7UUFBQztRQUFLO0tBQUUsRUFBRUMsTUFBTSxDQUFDO1FBQUMsQ0FBQztRQUFHO0tBQUssRUFBRUMsU0FBUyxDQUFDO1FBQUM7UUFBSTtLQUFHLEdBQUdDLGFBQ25GQyxTQUFTM0IsOERBQWNBLEdBQUd1QixNQUFNLENBQUM7UUFBQztRQUFLO0tBQUUsRUFBRUMsTUFBTSxDQUFDO1FBQUMsQ0FBQztRQUFHO0tBQUssRUFBRUMsU0FBUyxDQUFDO1FBQUM7UUFBRztLQUFHLEdBQUdHLGFBQ2xGbkIsT0FBT29CLGNBQWM7UUFBQ3BCLE9BQU8sU0FBU0MsQ0FBQyxFQUFFQyxDQUFDO1lBQUlGLFFBQVE7Z0JBQUNDO2dCQUFHQzthQUFFO1FBQUU7SUFBQztJQUVuRSxTQUFTbUIsVUFBVUMsV0FBVztRQUM1QixJQUFJckIsSUFBSXFCLFdBQVcsQ0FBQyxFQUFFLEVBQUVwQixJQUFJb0IsV0FBVyxDQUFDLEVBQUU7UUFDMUMsT0FBT3RCLFFBQVEsTUFDWCxDQUFDWSxhQUFhWixLQUFLLENBQUNDLEdBQUdDLElBQUlGLEtBQUksS0FDM0JpQixDQUFBQSxZQUFZakIsS0FBSyxDQUFDQyxHQUFHQyxJQUFJRixLQUFJLEtBQzdCbUIsQ0FBQUEsWUFBWW5CLEtBQUssQ0FBQ0MsR0FBR0MsSUFBSUYsS0FBSTtJQUN2QztJQUVBcUIsVUFBVUUsTUFBTSxHQUFHLFNBQVNELFdBQVc7UUFDckMsSUFBSUUsSUFBSWIsUUFBUWMsS0FBSyxJQUNqQkMsSUFBSWYsUUFBUWdCLFNBQVMsSUFDckIxQixJQUFJLENBQUNxQixXQUFXLENBQUMsRUFBRSxHQUFHSSxDQUFDLENBQUMsRUFBRSxJQUFJRixHQUM5QnRCLElBQUksQ0FBQ29CLFdBQVcsQ0FBQyxFQUFFLEdBQUdJLENBQUMsQ0FBQyxFQUFFLElBQUlGO1FBQ2xDLE9BQU8sQ0FBQ3RCLEtBQUssU0FBU0EsSUFBSSxTQUFTRCxLQUFLLENBQUMsU0FBU0EsSUFBSSxDQUFDLFFBQVFZLFNBQ3pEWCxLQUFLLFNBQVNBLElBQUksU0FBU0QsS0FBSyxDQUFDLFNBQVNBLElBQUksQ0FBQyxRQUFRaUIsU0FDdkRQLE9BQU0sRUFBR1ksTUFBTSxDQUFDRDtJQUN4QjtJQUVBRCxVQUFVTyxNQUFNLEdBQUcsU0FBU0EsTUFBTTtRQUNoQyxPQUFPbkIsU0FBU0MsZ0JBQWdCa0IsU0FBU25CLFFBQVFBLFFBQVFiLFVBQVU7WUFBQ2UsUUFBUWlCLE1BQU0sQ0FBQ2xCLGNBQWNrQjtZQUFTZixPQUFPZSxNQUFNLENBQUNBO1lBQVNWLE9BQU9VLE1BQU0sQ0FBQ0E7U0FBUTtJQUN6SjtJQUVBUCxVQUFVUSxTQUFTLEdBQUcsU0FBU0MsQ0FBQztRQUM5QixJQUFJLENBQUNDLFVBQVVoQyxNQUFNLEVBQUUsT0FBT1ksUUFBUWtCLFNBQVM7UUFDL0NsQixRQUFRa0IsU0FBUyxDQUFDQyxJQUFJakIsT0FBT2dCLFNBQVMsQ0FBQ0MsSUFBSVosT0FBT1csU0FBUyxDQUFDQztRQUM1RCxPQUFPRTtJQUNUO0lBRUFYLFVBQVVJLEtBQUssR0FBRyxTQUFTSyxDQUFDO1FBQzFCLElBQUksQ0FBQ0MsVUFBVWhDLE1BQU0sRUFBRSxPQUFPWSxRQUFRYyxLQUFLO1FBQzNDZCxRQUFRYyxLQUFLLENBQUNLLElBQUlqQixPQUFPWSxLQUFLLENBQUNLLElBQUksT0FBT1osT0FBT08sS0FBSyxDQUFDSztRQUN2RCxPQUFPVCxVQUFVTSxTQUFTLENBQUNoQixRQUFRZ0IsU0FBUztJQUM5QztJQUVBTixVQUFVTSxTQUFTLEdBQUcsU0FBU0csQ0FBQztRQUM5QixJQUFJLENBQUNDLFVBQVVoQyxNQUFNLEVBQUUsT0FBT1ksUUFBUWdCLFNBQVM7UUFDL0MsSUFBSUgsSUFBSWIsUUFBUWMsS0FBSyxJQUFJeEIsSUFBSSxDQUFDNkIsQ0FBQyxDQUFDLEVBQUUsRUFBRTVCLElBQUksQ0FBQzRCLENBQUMsQ0FBQyxFQUFFO1FBRTdDbEIsZUFBZUQsUUFDVmdCLFNBQVMsQ0FBQ0csR0FDVkcsVUFBVSxDQUFDO1lBQUM7Z0JBQUNoQyxJQUFJLFFBQVF1QjtnQkFBR3RCLElBQUksUUFBUXNCO2FBQUU7WUFBRTtnQkFBQ3ZCLElBQUksUUFBUXVCO2dCQUFHdEIsSUFBSSxRQUFRc0I7YUFBRTtTQUFDLEVBQzNFSSxNQUFNLENBQUNSO1FBRVpILGNBQWNKLE9BQ1RjLFNBQVMsQ0FBQztZQUFDMUIsSUFBSSxRQUFRdUI7WUFBR3RCLElBQUksUUFBUXNCO1NBQUUsRUFDeENTLFVBQVUsQ0FBQztZQUFDO2dCQUFDaEMsSUFBSSxRQUFRdUIsSUFBSW5DLDZDQUFPQTtnQkFBRWEsSUFBSSxRQUFRc0IsSUFBSW5DLDZDQUFPQTthQUFDO1lBQUU7Z0JBQUNZLElBQUksUUFBUXVCLElBQUluQyw2Q0FBT0E7Z0JBQUVhLElBQUksUUFBUXNCLElBQUluQyw2Q0FBT0E7YUFBQztTQUFDLEVBQ25IdUMsTUFBTSxDQUFDUjtRQUVaRCxjQUFjRCxPQUNUUyxTQUFTLENBQUM7WUFBQzFCLElBQUksUUFBUXVCO1lBQUd0QixJQUFJLFFBQVFzQjtTQUFFLEVBQ3hDUyxVQUFVLENBQUM7WUFBQztnQkFBQ2hDLElBQUksUUFBUXVCLElBQUluQyw2Q0FBT0E7Z0JBQUVhLElBQUksUUFBUXNCLElBQUluQyw2Q0FBT0E7YUFBQztZQUFFO2dCQUFDWSxJQUFJLFFBQVF1QixJQUFJbkMsNkNBQU9BO2dCQUFFYSxJQUFJLFFBQVFzQixJQUFJbkMsNkNBQU9BO2FBQUM7U0FBQyxFQUNuSHVDLE1BQU0sQ0FBQ1I7UUFFWixPQUFPWTtJQUNUO0lBRUFYLFVBQVU3QixTQUFTLEdBQUcsU0FBUzBDLE1BQU0sRUFBRUMsTUFBTTtRQUMzQyxPQUFPM0Msa0RBQVNBLENBQUM2QixXQUFXYSxRQUFRQztJQUN0QztJQUVBZCxVQUFVNUIsT0FBTyxHQUFHLFNBQVMyQyxJQUFJLEVBQUVELE1BQU07UUFDdkMsT0FBTzFDLGdEQUFPQSxDQUFDNEIsV0FBV2UsTUFBTUQ7SUFDbEM7SUFFQWQsVUFBVTNCLFFBQVEsR0FBRyxTQUFTMkMsS0FBSyxFQUFFRixNQUFNO1FBQ3pDLE9BQU96QyxpREFBUUEsQ0FBQzJCLFdBQVdnQixPQUFPRjtJQUNwQztJQUVBZCxVQUFVMUIsU0FBUyxHQUFHLFNBQVMyQyxNQUFNLEVBQUVILE1BQU07UUFDM0MsT0FBT3hDLGtEQUFTQSxDQUFDMEIsV0FBV2lCLFFBQVFIO0lBQ3RDO0lBRUEsU0FBU0g7UUFDUHZCLFFBQVFDLGNBQWM7UUFDdEIsT0FBT1c7SUFDVDtJQUVBLE9BQU9BLFVBQVVJLEtBQUssQ0FBQztBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vYWxiZXJzVXNhLmpzPzAzZDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtlcHNpbG9ufSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IGFsYmVycyBmcm9tIFwiLi9hbGJlcnMuanNcIjtcbmltcG9ydCBjb25pY0VxdWFsQXJlYSBmcm9tIFwiLi9jb25pY0VxdWFsQXJlYS5qc1wiO1xuaW1wb3J0IHtmaXRFeHRlbnQsIGZpdFNpemUsIGZpdFdpZHRoLCBmaXRIZWlnaHR9IGZyb20gXCIuL2ZpdC5qc1wiO1xuXG4vLyBUaGUgcHJvamVjdGlvbnMgbXVzdCBoYXZlIG11dHVhbGx5IGV4Y2x1c2l2ZSBjbGlwIHJlZ2lvbnMgb24gdGhlIHNwaGVyZSxcbi8vIGFzIHRoaXMgd2lsbCBhdm9pZCBlbWl0dGluZyBpbnRlcmxlYXZpbmcgbGluZXMgYW5kIHBvbHlnb25zLlxuZnVuY3Rpb24gbXVsdGlwbGV4KHN0cmVhbXMpIHtcbiAgdmFyIG4gPSBzdHJlYW1zLmxlbmd0aDtcbiAgcmV0dXJuIHtcbiAgICBwb2ludDogZnVuY3Rpb24oeCwgeSkgeyB2YXIgaSA9IC0xOyB3aGlsZSAoKytpIDwgbikgc3RyZWFtc1tpXS5wb2ludCh4LCB5KTsgfSxcbiAgICBzcGhlcmU6IGZ1bmN0aW9uKCkgeyB2YXIgaSA9IC0xOyB3aGlsZSAoKytpIDwgbikgc3RyZWFtc1tpXS5zcGhlcmUoKTsgfSxcbiAgICBsaW5lU3RhcnQ6IGZ1bmN0aW9uKCkgeyB2YXIgaSA9IC0xOyB3aGlsZSAoKytpIDwgbikgc3RyZWFtc1tpXS5saW5lU3RhcnQoKTsgfSxcbiAgICBsaW5lRW5kOiBmdW5jdGlvbigpIHsgdmFyIGkgPSAtMTsgd2hpbGUgKCsraSA8IG4pIHN0cmVhbXNbaV0ubGluZUVuZCgpOyB9LFxuICAgIHBvbHlnb25TdGFydDogZnVuY3Rpb24oKSB7IHZhciBpID0gLTE7IHdoaWxlICgrK2kgPCBuKSBzdHJlYW1zW2ldLnBvbHlnb25TdGFydCgpOyB9LFxuICAgIHBvbHlnb25FbmQ6IGZ1bmN0aW9uKCkgeyB2YXIgaSA9IC0xOyB3aGlsZSAoKytpIDwgbikgc3RyZWFtc1tpXS5wb2x5Z29uRW5kKCk7IH1cbiAgfTtcbn1cblxuLy8gQSBjb21wb3NpdGUgcHJvamVjdGlvbiBmb3IgdGhlIFVuaXRlZCBTdGF0ZXMsIGNvbmZpZ3VyZWQgYnkgZGVmYXVsdCBmb3Jcbi8vIDk2MMOXNTAwLiBUaGUgcHJvamVjdGlvbiBhbHNvIHdvcmtzIHF1aXRlIHdlbGwgYXQgOTYww5c2MDAgaWYgeW91IGNoYW5nZSB0aGVcbi8vIHNjYWxlIHRvIDEyODUgYW5kIGFkanVzdCB0aGUgdHJhbnNsYXRlIGFjY29yZGluZ2x5LiBUaGUgc2V0IG9mIHN0YW5kYXJkXG4vLyBwYXJhbGxlbHMgZm9yIGVhY2ggcmVnaW9uIGNvbWVzIGZyb20gVVNHUywgd2hpY2ggaXMgcHVibGlzaGVkIGhlcmU6XG4vLyBodHRwOi8vZWdzYy51c2dzLmdvdi9pc2IvcHVicy9NYXBQcm9qZWN0aW9ucy9wcm9qZWN0aW9ucy5odG1sI2FsYmVyc1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHZhciBjYWNoZSxcbiAgICAgIGNhY2hlU3RyZWFtLFxuICAgICAgbG93ZXI0OCA9IGFsYmVycygpLCBsb3dlcjQ4UG9pbnQsXG4gICAgICBhbGFza2EgPSBjb25pY0VxdWFsQXJlYSgpLnJvdGF0ZShbMTU0LCAwXSkuY2VudGVyKFstMiwgNTguNV0pLnBhcmFsbGVscyhbNTUsIDY1XSksIGFsYXNrYVBvaW50LCAvLyBFUFNHOjMzMzhcbiAgICAgIGhhd2FpaSA9IGNvbmljRXF1YWxBcmVhKCkucm90YXRlKFsxNTcsIDBdKS5jZW50ZXIoWy0zLCAxOS45XSkucGFyYWxsZWxzKFs4LCAxOF0pLCBoYXdhaWlQb2ludCwgLy8gRVNSSToxMDIwMDdcbiAgICAgIHBvaW50LCBwb2ludFN0cmVhbSA9IHtwb2ludDogZnVuY3Rpb24oeCwgeSkgeyBwb2ludCA9IFt4LCB5XTsgfX07XG5cbiAgZnVuY3Rpb24gYWxiZXJzVXNhKGNvb3JkaW5hdGVzKSB7XG4gICAgdmFyIHggPSBjb29yZGluYXRlc1swXSwgeSA9IGNvb3JkaW5hdGVzWzFdO1xuICAgIHJldHVybiBwb2ludCA9IG51bGwsXG4gICAgICAgIChsb3dlcjQ4UG9pbnQucG9pbnQoeCwgeSksIHBvaW50KVxuICAgICAgICB8fCAoYWxhc2thUG9pbnQucG9pbnQoeCwgeSksIHBvaW50KVxuICAgICAgICB8fCAoaGF3YWlpUG9pbnQucG9pbnQoeCwgeSksIHBvaW50KTtcbiAgfVxuXG4gIGFsYmVyc1VzYS5pbnZlcnQgPSBmdW5jdGlvbihjb29yZGluYXRlcykge1xuICAgIHZhciBrID0gbG93ZXI0OC5zY2FsZSgpLFxuICAgICAgICB0ID0gbG93ZXI0OC50cmFuc2xhdGUoKSxcbiAgICAgICAgeCA9IChjb29yZGluYXRlc1swXSAtIHRbMF0pIC8gayxcbiAgICAgICAgeSA9IChjb29yZGluYXRlc1sxXSAtIHRbMV0pIC8gaztcbiAgICByZXR1cm4gKHkgPj0gMC4xMjAgJiYgeSA8IDAuMjM0ICYmIHggPj0gLTAuNDI1ICYmIHggPCAtMC4yMTQgPyBhbGFza2FcbiAgICAgICAgOiB5ID49IDAuMTY2ICYmIHkgPCAwLjIzNCAmJiB4ID49IC0wLjIxNCAmJiB4IDwgLTAuMTE1ID8gaGF3YWlpXG4gICAgICAgIDogbG93ZXI0OCkuaW52ZXJ0KGNvb3JkaW5hdGVzKTtcbiAgfTtcblxuICBhbGJlcnNVc2Euc3RyZWFtID0gZnVuY3Rpb24oc3RyZWFtKSB7XG4gICAgcmV0dXJuIGNhY2hlICYmIGNhY2hlU3RyZWFtID09PSBzdHJlYW0gPyBjYWNoZSA6IGNhY2hlID0gbXVsdGlwbGV4KFtsb3dlcjQ4LnN0cmVhbShjYWNoZVN0cmVhbSA9IHN0cmVhbSksIGFsYXNrYS5zdHJlYW0oc3RyZWFtKSwgaGF3YWlpLnN0cmVhbShzdHJlYW0pXSk7XG4gIH07XG5cbiAgYWxiZXJzVXNhLnByZWNpc2lvbiA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBsb3dlcjQ4LnByZWNpc2lvbigpO1xuICAgIGxvd2VyNDgucHJlY2lzaW9uKF8pLCBhbGFza2EucHJlY2lzaW9uKF8pLCBoYXdhaWkucHJlY2lzaW9uKF8pO1xuICAgIHJldHVybiByZXNldCgpO1xuICB9O1xuXG4gIGFsYmVyc1VzYS5zY2FsZSA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBsb3dlcjQ4LnNjYWxlKCk7XG4gICAgbG93ZXI0OC5zY2FsZShfKSwgYWxhc2thLnNjYWxlKF8gKiAwLjM1KSwgaGF3YWlpLnNjYWxlKF8pO1xuICAgIHJldHVybiBhbGJlcnNVc2EudHJhbnNsYXRlKGxvd2VyNDgudHJhbnNsYXRlKCkpO1xuICB9O1xuXG4gIGFsYmVyc1VzYS50cmFuc2xhdGUgPSBmdW5jdGlvbihfKSB7XG4gICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKSByZXR1cm4gbG93ZXI0OC50cmFuc2xhdGUoKTtcbiAgICB2YXIgayA9IGxvd2VyNDguc2NhbGUoKSwgeCA9ICtfWzBdLCB5ID0gK19bMV07XG5cbiAgICBsb3dlcjQ4UG9pbnQgPSBsb3dlcjQ4XG4gICAgICAgIC50cmFuc2xhdGUoXylcbiAgICAgICAgLmNsaXBFeHRlbnQoW1t4IC0gMC40NTUgKiBrLCB5IC0gMC4yMzggKiBrXSwgW3ggKyAwLjQ1NSAqIGssIHkgKyAwLjIzOCAqIGtdXSlcbiAgICAgICAgLnN0cmVhbShwb2ludFN0cmVhbSk7XG5cbiAgICBhbGFza2FQb2ludCA9IGFsYXNrYVxuICAgICAgICAudHJhbnNsYXRlKFt4IC0gMC4zMDcgKiBrLCB5ICsgMC4yMDEgKiBrXSlcbiAgICAgICAgLmNsaXBFeHRlbnQoW1t4IC0gMC40MjUgKiBrICsgZXBzaWxvbiwgeSArIDAuMTIwICogayArIGVwc2lsb25dLCBbeCAtIDAuMjE0ICogayAtIGVwc2lsb24sIHkgKyAwLjIzNCAqIGsgLSBlcHNpbG9uXV0pXG4gICAgICAgIC5zdHJlYW0ocG9pbnRTdHJlYW0pO1xuXG4gICAgaGF3YWlpUG9pbnQgPSBoYXdhaWlcbiAgICAgICAgLnRyYW5zbGF0ZShbeCAtIDAuMjA1ICogaywgeSArIDAuMjEyICoga10pXG4gICAgICAgIC5jbGlwRXh0ZW50KFtbeCAtIDAuMjE0ICogayArIGVwc2lsb24sIHkgKyAwLjE2NiAqIGsgKyBlcHNpbG9uXSwgW3ggLSAwLjExNSAqIGsgLSBlcHNpbG9uLCB5ICsgMC4yMzQgKiBrIC0gZXBzaWxvbl1dKVxuICAgICAgICAuc3RyZWFtKHBvaW50U3RyZWFtKTtcblxuICAgIHJldHVybiByZXNldCgpO1xuICB9O1xuXG4gIGFsYmVyc1VzYS5maXRFeHRlbnQgPSBmdW5jdGlvbihleHRlbnQsIG9iamVjdCkge1xuICAgIHJldHVybiBmaXRFeHRlbnQoYWxiZXJzVXNhLCBleHRlbnQsIG9iamVjdCk7XG4gIH07XG5cbiAgYWxiZXJzVXNhLmZpdFNpemUgPSBmdW5jdGlvbihzaXplLCBvYmplY3QpIHtcbiAgICByZXR1cm4gZml0U2l6ZShhbGJlcnNVc2EsIHNpemUsIG9iamVjdCk7XG4gIH07XG5cbiAgYWxiZXJzVXNhLmZpdFdpZHRoID0gZnVuY3Rpb24od2lkdGgsIG9iamVjdCkge1xuICAgIHJldHVybiBmaXRXaWR0aChhbGJlcnNVc2EsIHdpZHRoLCBvYmplY3QpO1xuICB9O1xuXG4gIGFsYmVyc1VzYS5maXRIZWlnaHQgPSBmdW5jdGlvbihoZWlnaHQsIG9iamVjdCkge1xuICAgIHJldHVybiBmaXRIZWlnaHQoYWxiZXJzVXNhLCBoZWlnaHQsIG9iamVjdCk7XG4gIH07XG5cbiAgZnVuY3Rpb24gcmVzZXQoKSB7XG4gICAgY2FjaGUgPSBjYWNoZVN0cmVhbSA9IG51bGw7XG4gICAgcmV0dXJuIGFsYmVyc1VzYTtcbiAgfVxuXG4gIHJldHVybiBhbGJlcnNVc2Euc2NhbGUoMTA3MCk7XG59XG4iXSwibmFtZXMiOlsiZXBzaWxvbiIsImFsYmVycyIsImNvbmljRXF1YWxBcmVhIiwiZml0RXh0ZW50IiwiZml0U2l6ZSIsImZpdFdpZHRoIiwiZml0SGVpZ2h0IiwibXVsdGlwbGV4Iiwic3RyZWFtcyIsIm4iLCJsZW5ndGgiLCJwb2ludCIsIngiLCJ5IiwiaSIsInNwaGVyZSIsImxpbmVTdGFydCIsImxpbmVFbmQiLCJwb2x5Z29uU3RhcnQiLCJwb2x5Z29uRW5kIiwiY2FjaGUiLCJjYWNoZVN0cmVhbSIsImxvd2VyNDgiLCJsb3dlcjQ4UG9pbnQiLCJhbGFza2EiLCJyb3RhdGUiLCJjZW50ZXIiLCJwYXJhbGxlbHMiLCJhbGFza2FQb2ludCIsImhhd2FpaSIsImhhd2FpaVBvaW50IiwicG9pbnRTdHJlYW0iLCJhbGJlcnNVc2EiLCJjb29yZGluYXRlcyIsImludmVydCIsImsiLCJzY2FsZSIsInQiLCJ0cmFuc2xhdGUiLCJzdHJlYW0iLCJwcmVjaXNpb24iLCJfIiwiYXJndW1lbnRzIiwicmVzZXQiLCJjbGlwRXh0ZW50IiwiZXh0ZW50Iiwib2JqZWN0Iiwic2l6ZSIsIndpZHRoIiwiaGVpZ2h0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/albersUsa.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/azimuthal.js":
/*!*************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/azimuthal.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalInvert: () => (/* binding */ azimuthalInvert),\n/* harmony export */   azimuthalRaw: () => (/* binding */ azimuthalRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\nfunction azimuthalRaw(scale) {\n    return function(x, y) {\n        var cx = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x), cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = scale(cx * cy);\n        if (k === Infinity) return [\n            2,\n            0\n        ];\n        return [\n            k * cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x),\n            k * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)\n        ];\n    };\n}\nfunction azimuthalInvert(angle) {\n    return function(x, y) {\n        var z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + y * y), c = angle(z), sc = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(c), cc = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(c);\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x * sc, z * cc),\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(z && y * sc / z)\n        ];\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/azimuthal.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/azimuthalEqualArea.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/azimuthalEqualArea.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalEqualAreaRaw: () => (/* binding */ azimuthalEqualAreaRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\n\n\nvar azimuthalEqualAreaRaw = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalRaw)(function(cxcy) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(2 / (1 + cxcy));\n});\nazimuthalEqualAreaRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalInvert)(function(z) {\n    return 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(z / 2);\n});\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(azimuthalEqualAreaRaw).scale(124.75).clipAngle(180 - 1e-3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9hemltdXRoYWxFcXVhbEFyZWEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFDdUI7QUFDekI7QUFFN0IsSUFBSUssd0JBQXdCSCwyREFBWUEsQ0FBQyxTQUFTSSxJQUFJO0lBQzNELE9BQU9MLDhDQUFJQSxDQUFDLElBQUssS0FBSUssSUFBRztBQUMxQixHQUFHO0FBRUhELHNCQUFzQkUsTUFBTSxHQUFHSiw4REFBZUEsQ0FBQyxTQUFTSyxDQUFDO0lBQ3ZELE9BQU8sSUFBSVIsOENBQUlBLENBQUNRLElBQUk7QUFDdEI7QUFFQSw2QkFBZSxzQ0FBVztJQUN4QixPQUFPSixxREFBVUEsQ0FBQ0MsdUJBQ2JJLEtBQUssQ0FBQyxRQUNOQyxTQUFTLENBQUMsTUFBTTtBQUN2QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vYXppbXV0aGFsRXF1YWxBcmVhLmpzPzU1MDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthc2luLCBzcXJ0fSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHthemltdXRoYWxSYXcsIGF6aW11dGhhbEludmVydH0gZnJvbSBcIi4vYXppbXV0aGFsLmpzXCI7XG5pbXBvcnQgcHJvamVjdGlvbiBmcm9tIFwiLi9pbmRleC5qc1wiO1xuXG5leHBvcnQgdmFyIGF6aW11dGhhbEVxdWFsQXJlYVJhdyA9IGF6aW11dGhhbFJhdyhmdW5jdGlvbihjeGN5KSB7XG4gIHJldHVybiBzcXJ0KDIgLyAoMSArIGN4Y3kpKTtcbn0pO1xuXG5hemltdXRoYWxFcXVhbEFyZWFSYXcuaW52ZXJ0ID0gYXppbXV0aGFsSW52ZXJ0KGZ1bmN0aW9uKHopIHtcbiAgcmV0dXJuIDIgKiBhc2luKHogLyAyKTtcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHByb2plY3Rpb24oYXppbXV0aGFsRXF1YWxBcmVhUmF3KVxuICAgICAgLnNjYWxlKDEyNC43NSlcbiAgICAgIC5jbGlwQW5nbGUoMTgwIC0gMWUtMyk7XG59XG4iXSwibmFtZXMiOlsiYXNpbiIsInNxcnQiLCJhemltdXRoYWxSYXciLCJhemltdXRoYWxJbnZlcnQiLCJwcm9qZWN0aW9uIiwiYXppbXV0aGFsRXF1YWxBcmVhUmF3IiwiY3hjeSIsImludmVydCIsInoiLCJzY2FsZSIsImNsaXBBbmdsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/azimuthalEqualArea.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/azimuthalEquidistant.js":
/*!************************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/azimuthalEquidistant.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalEquidistantRaw: () => (/* binding */ azimuthalEquidistantRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\n\n\nvar azimuthalEquidistantRaw = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalRaw)(function(c) {\n    return (c = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.acos)(c)) && c / (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(c);\n});\nazimuthalEquidistantRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalInvert)(function(z) {\n    return z;\n});\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(azimuthalEquidistantRaw).scale(79.4188).clipAngle(180 - 1e-3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9hemltdXRoYWxFcXVpZGlzdGFudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFxQztBQUN3QjtBQUN6QjtBQUU3QixJQUFJSywwQkFBMEJILDJEQUFZQSxDQUFDLFNBQVNJLENBQUM7SUFDMUQsT0FBTyxDQUFDQSxJQUFJTiw4Q0FBSUEsQ0FBQ00sRUFBQyxLQUFNQSxJQUFJTCw2Q0FBR0EsQ0FBQ0s7QUFDbEMsR0FBRztBQUVIRCx3QkFBd0JFLE1BQU0sR0FBR0osOERBQWVBLENBQUMsU0FBU0ssQ0FBQztJQUN6RCxPQUFPQTtBQUNUO0FBRUEsNkJBQWUsc0NBQVc7SUFDeEIsT0FBT0oscURBQVVBLENBQUNDLHlCQUNiSSxLQUFLLENBQUMsU0FDTkMsU0FBUyxDQUFDLE1BQU07QUFDdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbEVxdWlkaXN0YW50LmpzPzZlYTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthY29zLCBzaW59IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5pbXBvcnQge2F6aW11dGhhbFJhdywgYXppbXV0aGFsSW52ZXJ0fSBmcm9tIFwiLi9hemltdXRoYWwuanNcIjtcbmltcG9ydCBwcm9qZWN0aW9uIGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCB2YXIgYXppbXV0aGFsRXF1aWRpc3RhbnRSYXcgPSBhemltdXRoYWxSYXcoZnVuY3Rpb24oYykge1xuICByZXR1cm4gKGMgPSBhY29zKGMpKSAmJiBjIC8gc2luKGMpO1xufSk7XG5cbmF6aW11dGhhbEVxdWlkaXN0YW50UmF3LmludmVydCA9IGF6aW11dGhhbEludmVydChmdW5jdGlvbih6KSB7XG4gIHJldHVybiB6O1xufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcHJvamVjdGlvbihhemltdXRoYWxFcXVpZGlzdGFudFJhdylcbiAgICAgIC5zY2FsZSg3OS40MTg4KVxuICAgICAgLmNsaXBBbmdsZSgxODAgLSAxZS0zKTtcbn1cbiJdLCJuYW1lcyI6WyJhY29zIiwic2luIiwiYXppbXV0aGFsUmF3IiwiYXppbXV0aGFsSW52ZXJ0IiwicHJvamVjdGlvbiIsImF6aW11dGhhbEVxdWlkaXN0YW50UmF3IiwiYyIsImludmVydCIsInoiLCJzY2FsZSIsImNsaXBBbmdsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/azimuthalEquidistant.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/conic.js":
/*!*********************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/conic.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicProjection: () => (/* binding */ conicProjection)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\n\nfunction conicProjection(projectAt) {\n    var phi0 = 0, phi1 = _math_js__WEBPACK_IMPORTED_MODULE_0__.pi / 3, m = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.projectionMutator)(projectAt), p = m(phi0, phi1);\n    p.parallels = function(_) {\n        return arguments.length ? m(phi0 = _[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, phi1 = _[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians) : [\n            phi0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees,\n            phi1 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees\n        ];\n    };\n    return p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9jb25pYy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Q7QUFDSDtBQUV0QyxTQUFTSSxnQkFBZ0JDLFNBQVM7SUFDdkMsSUFBSUMsT0FBTyxHQUNQQyxPQUFPTix3Q0FBRUEsR0FBRyxHQUNaTyxJQUFJTCw0REFBaUJBLENBQUNFLFlBQ3RCSSxJQUFJRCxFQUFFRixNQUFNQztJQUVoQkUsRUFBRUMsU0FBUyxHQUFHLFNBQVNDLENBQUM7UUFDdEIsT0FBT0MsVUFBVUMsTUFBTSxHQUFHTCxFQUFFRixPQUFPSyxDQUFDLENBQUMsRUFBRSxHQUFHVCw2Q0FBT0EsRUFBRUssT0FBT0ksQ0FBQyxDQUFDLEVBQUUsR0FBR1QsNkNBQU9BLElBQUk7WUFBQ0ksT0FBT04sNkNBQU9BO1lBQUVPLE9BQU9QLDZDQUFPQTtTQUFDO0lBQzlHO0lBRUEsT0FBT1M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vY29uaWMuanM/MDk4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2RlZ3JlZXMsIHBpLCByYWRpYW5zfSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHtwcm9qZWN0aW9uTXV0YXRvcn0gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGNvbmljUHJvamVjdGlvbihwcm9qZWN0QXQpIHtcbiAgdmFyIHBoaTAgPSAwLFxuICAgICAgcGhpMSA9IHBpIC8gMyxcbiAgICAgIG0gPSBwcm9qZWN0aW9uTXV0YXRvcihwcm9qZWN0QXQpLFxuICAgICAgcCA9IG0ocGhpMCwgcGhpMSk7XG5cbiAgcC5wYXJhbGxlbHMgPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyBtKHBoaTAgPSBfWzBdICogcmFkaWFucywgcGhpMSA9IF9bMV0gKiByYWRpYW5zKSA6IFtwaGkwICogZGVncmVlcywgcGhpMSAqIGRlZ3JlZXNdO1xuICB9O1xuXG4gIHJldHVybiBwO1xufVxuIl0sIm5hbWVzIjpbImRlZ3JlZXMiLCJwaSIsInJhZGlhbnMiLCJwcm9qZWN0aW9uTXV0YXRvciIsImNvbmljUHJvamVjdGlvbiIsInByb2plY3RBdCIsInBoaTAiLCJwaGkxIiwibSIsInAiLCJwYXJhbGxlbHMiLCJfIiwiYXJndW1lbnRzIiwibGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/conic.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/conicConformal.js":
/*!******************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/conicConformal.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicConformalRaw: () => (/* binding */ conicConformalRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _mercator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mercator.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/mercator.js\");\n\n\n\nfunction tany(y) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + y) / 2);\n}\nfunction conicConformalRaw(y0, y1) {\n    var cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0), n = y0 === y1 ? (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0) : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)(cy0 / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1)) / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)(tany(y1) / tany(y0)), f = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(tany(y0), n) / n;\n    if (!n) return _mercator_js__WEBPACK_IMPORTED_MODULE_1__.mercatorRaw;\n    function project(x, y) {\n        if (f > 0) {\n            if (y < -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) y = -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n        } else {\n            if (y > _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) y = _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n        }\n        var r = f / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(tany(y), n);\n        return [\n            r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(n * x),\n            f - r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(n * x)\n        ];\n    }\n    project.invert = function(x, y) {\n        var fy = f - y, r = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(n) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + fy * fy), l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(fy)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(fy);\n        if (fy * n < 0) l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(fy);\n        return [\n            l / n,\n            2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(f / r, 1 / n)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi\n        ];\n    };\n    return project;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicConformalRaw).scale(109.5).parallels([\n        30,\n        30\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/conicConformal.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/conicEqualArea.js":
/*!******************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/conicEqualArea.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicEqualAreaRaw: () => (/* binding */ conicEqualAreaRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _cylindricalEqualArea_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cylindricalEqualArea.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/cylindricalEqualArea.js\");\n\n\n\nfunction conicEqualAreaRaw(y0, y1) {\n    var sy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0), n = (sy0 + (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y1)) / 2;\n    // Are the parallels symmetrical around the Equator?\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(n) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) return (0,_cylindricalEqualArea_js__WEBPACK_IMPORTED_MODULE_1__.cylindricalEqualAreaRaw)(y0);\n    var c = 1 + sy0 * (2 * n - sy0), r0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(c) / n;\n    function project(x, y) {\n        var r = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(c - 2 * n * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)) / n;\n        return [\n            r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x *= n),\n            r0 - r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x)\n        ];\n    }\n    project.invert = function(x, y) {\n        var r0y = r0 - y, l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(r0y)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(r0y);\n        if (r0y * n < 0) l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(r0y);\n        return [\n            l / n,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((c - (x * x + r0y * r0y) * n * n) / (2 * n))\n        ];\n    };\n    return project;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicEqualAreaRaw).scale(155.424).center([\n        0,\n        33.6442\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/conicEqualArea.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/conicEquidistant.js":
/*!********************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/conicEquidistant.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicEquidistantRaw: () => (/* binding */ conicEquidistantRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _equirectangular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./equirectangular.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/equirectangular.js\");\n\n\n\nfunction conicEquidistantRaw(y0, y1) {\n    var cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0), n = y0 === y1 ? (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0) : (cy0 - (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1)) / (y1 - y0), g = cy0 / n + y0;\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(n) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) return _equirectangular_js__WEBPACK_IMPORTED_MODULE_1__.equirectangularRaw;\n    function project(x, y) {\n        var gy = g - y, nx = n * x;\n        return [\n            gy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(nx),\n            g - gy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(nx)\n        ];\n    }\n    project.invert = function(x, y) {\n        var gy = g - y, l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(gy)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(gy);\n        if (gy * n < 0) l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(gy);\n        return [\n            l / n,\n            g - (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(n) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + gy * gy)\n        ];\n    };\n    return project;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicEquidistantRaw).scale(131.154).center([\n        0,\n        13.9389\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9jb25pY0VxdWlkaXN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXlFO0FBQzlCO0FBQ2E7QUFFakQsU0FBU1Usb0JBQW9CQyxFQUFFLEVBQUVDLEVBQUU7SUFDeEMsSUFBSUMsTUFBTVgsNkNBQUdBLENBQUNTLEtBQ1ZHLElBQUlILE9BQU9DLEtBQUtOLDZDQUFHQSxDQUFDSyxNQUFNLENBQUNFLE1BQU1YLDZDQUFHQSxDQUFDVSxHQUFFLElBQU1BLENBQUFBLEtBQUtELEVBQUMsR0FDbkRJLElBQUlGLE1BQU1DLElBQUlIO0lBRWxCLElBQUlYLDZDQUFHQSxDQUFDYyxLQUFLWCw2Q0FBT0EsRUFBRSxPQUFPTSxtRUFBa0JBO0lBRS9DLFNBQVNPLFFBQVFDLENBQUMsRUFBRUMsQ0FBQztRQUNuQixJQUFJQyxLQUFLSixJQUFJRyxHQUFHRSxLQUFLTixJQUFJRztRQUN6QixPQUFPO1lBQUNFLEtBQUtiLDZDQUFHQSxDQUFDYztZQUFLTCxJQUFJSSxLQUFLakIsNkNBQUdBLENBQUNrQjtTQUFJO0lBQ3pDO0lBRUFKLFFBQVFLLE1BQU0sR0FBRyxTQUFTSixDQUFDLEVBQUVDLENBQUM7UUFDNUIsSUFBSUMsS0FBS0osSUFBSUcsR0FDVEksSUFBSXJCLCtDQUFLQSxDQUFDZ0IsR0FBR2pCLDZDQUFHQSxDQUFDbUIsT0FBT2QsOENBQUlBLENBQUNjO1FBQ2pDLElBQUlBLEtBQUtMLElBQUksR0FDWFEsS0FBS2xCLHdDQUFFQSxHQUFHQyw4Q0FBSUEsQ0FBQ1ksS0FBS1osOENBQUlBLENBQUNjO1FBQzNCLE9BQU87WUFBQ0csSUFBSVI7WUFBR0MsSUFBSVYsOENBQUlBLENBQUNTLEtBQUtQLDhDQUFJQSxDQUFDVSxJQUFJQSxJQUFJRSxLQUFLQTtTQUFJO0lBQ3JEO0lBRUEsT0FBT0g7QUFDVDtBQUVBLDZCQUFlLHNDQUFXO0lBQ3hCLE9BQU9SLDBEQUFlQSxDQUFDRSxxQkFDbEJhLEtBQUssQ0FBQyxTQUNOQyxNQUFNLENBQUM7UUFBQztRQUFHO0tBQVE7QUFDMUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2NvbmljRXF1aWRpc3RhbnQuanM/MGE0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2FicywgYXRhbjIsIGNvcywgZXBzaWxvbiwgcGksIHNpZ24sIHNpbiwgc3FydH0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7Y29uaWNQcm9qZWN0aW9ufSBmcm9tIFwiLi9jb25pYy5qc1wiO1xuaW1wb3J0IHtlcXVpcmVjdGFuZ3VsYXJSYXd9IGZyb20gXCIuL2VxdWlyZWN0YW5ndWxhci5qc1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gY29uaWNFcXVpZGlzdGFudFJhdyh5MCwgeTEpIHtcbiAgdmFyIGN5MCA9IGNvcyh5MCksXG4gICAgICBuID0geTAgPT09IHkxID8gc2luKHkwKSA6IChjeTAgLSBjb3MoeTEpKSAvICh5MSAtIHkwKSxcbiAgICAgIGcgPSBjeTAgLyBuICsgeTA7XG5cbiAgaWYgKGFicyhuKSA8IGVwc2lsb24pIHJldHVybiBlcXVpcmVjdGFuZ3VsYXJSYXc7XG5cbiAgZnVuY3Rpb24gcHJvamVjdCh4LCB5KSB7XG4gICAgdmFyIGd5ID0gZyAtIHksIG54ID0gbiAqIHg7XG4gICAgcmV0dXJuIFtneSAqIHNpbihueCksIGcgLSBneSAqIGNvcyhueCldO1xuICB9XG5cbiAgcHJvamVjdC5pbnZlcnQgPSBmdW5jdGlvbih4LCB5KSB7XG4gICAgdmFyIGd5ID0gZyAtIHksXG4gICAgICAgIGwgPSBhdGFuMih4LCBhYnMoZ3kpKSAqIHNpZ24oZ3kpO1xuICAgIGlmIChneSAqIG4gPCAwKVxuICAgICAgbCAtPSBwaSAqIHNpZ24oeCkgKiBzaWduKGd5KTtcbiAgICByZXR1cm4gW2wgLyBuLCBnIC0gc2lnbihuKSAqIHNxcnQoeCAqIHggKyBneSAqIGd5KV07XG4gIH07XG5cbiAgcmV0dXJuIHByb2plY3Q7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gY29uaWNQcm9qZWN0aW9uKGNvbmljRXF1aWRpc3RhbnRSYXcpXG4gICAgICAuc2NhbGUoMTMxLjE1NClcbiAgICAgIC5jZW50ZXIoWzAsIDEzLjkzODldKTtcbn1cbiJdLCJuYW1lcyI6WyJhYnMiLCJhdGFuMiIsImNvcyIsImVwc2lsb24iLCJwaSIsInNpZ24iLCJzaW4iLCJzcXJ0IiwiY29uaWNQcm9qZWN0aW9uIiwiZXF1aXJlY3Rhbmd1bGFyUmF3IiwiY29uaWNFcXVpZGlzdGFudFJhdyIsInkwIiwieTEiLCJjeTAiLCJuIiwiZyIsInByb2plY3QiLCJ4IiwieSIsImd5IiwibngiLCJpbnZlcnQiLCJsIiwic2NhbGUiLCJjZW50ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/conicEquidistant.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/cylindricalEqualArea.js":
/*!************************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/cylindricalEqualArea.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cylindricalEqualAreaRaw: () => (/* binding */ cylindricalEqualAreaRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\nfunction cylindricalEqualAreaRaw(phi0) {\n    var cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi0);\n    function forward(lambda, phi) {\n        return [\n            lambda * cosPhi0,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi) / cosPhi0\n        ];\n    }\n    forward.invert = function(x, y) {\n        return [\n            x / cosPhi0,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(y * cosPhi0)\n        ];\n    };\n    return forward;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9jeWxpbmRyaWNhbEVxdWFsQXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUVuQyxTQUFTRyx3QkFBd0JDLElBQUk7SUFDMUMsSUFBSUMsVUFBVUosNkNBQUdBLENBQUNHO0lBRWxCLFNBQVNFLFFBQVFDLE1BQU0sRUFBRUMsR0FBRztRQUMxQixPQUFPO1lBQUNELFNBQVNGO1lBQVNILDZDQUFHQSxDQUFDTSxPQUFPSDtTQUFRO0lBQy9DO0lBRUFDLFFBQVFHLE1BQU0sR0FBRyxTQUFTQyxDQUFDLEVBQUVDLENBQUM7UUFDNUIsT0FBTztZQUFDRCxJQUFJTDtZQUFTTCw4Q0FBSUEsQ0FBQ1csSUFBSU47U0FBUztJQUN6QztJQUVBLE9BQU9DO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2N5bGluZHJpY2FsRXF1YWxBcmVhLmpzPzk3OGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthc2luLCBjb3MsIHNpbn0gZnJvbSBcIi4uL21hdGguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGN5bGluZHJpY2FsRXF1YWxBcmVhUmF3KHBoaTApIHtcbiAgdmFyIGNvc1BoaTAgPSBjb3MocGhpMCk7XG5cbiAgZnVuY3Rpb24gZm9yd2FyZChsYW1iZGEsIHBoaSkge1xuICAgIHJldHVybiBbbGFtYmRhICogY29zUGhpMCwgc2luKHBoaSkgLyBjb3NQaGkwXTtcbiAgfVxuXG4gIGZvcndhcmQuaW52ZXJ0ID0gZnVuY3Rpb24oeCwgeSkge1xuICAgIHJldHVybiBbeCAvIGNvc1BoaTAsIGFzaW4oeSAqIGNvc1BoaTApXTtcbiAgfTtcblxuICByZXR1cm4gZm9yd2FyZDtcbn1cbiJdLCJuYW1lcyI6WyJhc2luIiwiY29zIiwic2luIiwiY3lsaW5kcmljYWxFcXVhbEFyZWFSYXciLCJwaGkwIiwiY29zUGhpMCIsImZvcndhcmQiLCJsYW1iZGEiLCJwaGkiLCJpbnZlcnQiLCJ4IiwieSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/cylindricalEqualArea.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/equalEarth.js":
/*!**************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/equalEarth.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equalEarthRaw: () => (/* binding */ equalEarthRaw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\nvar A1 = 1.340264, A2 = -0.081106, A3 = 0.000893, A4 = 0.003796, M = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(3) / 2, iterations = 12;\nfunction equalEarthRaw(lambda, phi) {\n    var l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(M * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n    return [\n        lambda * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n        l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n    ];\n}\nequalEarthRaw.invert = function(x, y) {\n    var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n    for(var i = 0, delta, fy, fpy; i < iterations; ++i){\n        fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n        fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n        l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n        if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon2) break;\n    }\n    return [\n        M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(l),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(l) / M)\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(equalEarthRaw).scale(177.158);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/equalEarth.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/equirectangular.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/equirectangular.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equirectangularRaw: () => (/* binding */ equirectangularRaw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\nfunction equirectangularRaw(lambda, phi) {\n    return [\n        lambda,\n        phi\n    ];\n}\nequirectangularRaw.invert = equirectangularRaw;\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(equirectangularRaw).scale(152.63);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9lcXVpcmVjdGFuZ3VsYXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9DO0FBRTdCLFNBQVNDLG1CQUFtQkMsTUFBTSxFQUFFQyxHQUFHO0lBQzVDLE9BQU87UUFBQ0Q7UUFBUUM7S0FBSTtBQUN0QjtBQUVBRixtQkFBbUJHLE1BQU0sR0FBR0g7QUFFNUIsNkJBQWUsc0NBQVc7SUFDeEIsT0FBT0QscURBQVVBLENBQUNDLG9CQUNiSSxLQUFLLENBQUM7QUFDYiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vZXF1aXJlY3Rhbmd1bGFyLmpzPzJkNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHByb2plY3Rpb24gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGVxdWlyZWN0YW5ndWxhclJhdyhsYW1iZGEsIHBoaSkge1xuICByZXR1cm4gW2xhbWJkYSwgcGhpXTtcbn1cblxuZXF1aXJlY3Rhbmd1bGFyUmF3LmludmVydCA9IGVxdWlyZWN0YW5ndWxhclJhdztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHJldHVybiBwcm9qZWN0aW9uKGVxdWlyZWN0YW5ndWxhclJhdylcbiAgICAgIC5zY2FsZSgxNTIuNjMpO1xufVxuIl0sIm5hbWVzIjpbInByb2plY3Rpb24iLCJlcXVpcmVjdGFuZ3VsYXJSYXciLCJsYW1iZGEiLCJwaGkiLCJpbnZlcnQiLCJzY2FsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/equirectangular.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/fit.js":
/*!*******************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/fit.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fitExtent: () => (/* binding */ fitExtent),\n/* harmony export */   fitHeight: () => (/* binding */ fitHeight),\n/* harmony export */   fitSize: () => (/* binding */ fitSize),\n/* harmony export */   fitWidth: () => (/* binding */ fitWidth)\n/* harmony export */ });\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stream.js */ \"(ssr)/../../node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _path_bounds_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../path/bounds.js */ \"(ssr)/../../node_modules/d3-geo/src/path/bounds.js\");\n\n\nfunction fit(projection, fitBounds, object) {\n    var clip = projection.clipExtent && projection.clipExtent();\n    projection.scale(150).translate([\n        0,\n        0\n    ]);\n    if (clip != null) projection.clipExtent(null);\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projection.stream(_path_bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n    fitBounds(_path_bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].result());\n    if (clip != null) projection.clipExtent(clip);\n    return projection;\n}\nfunction fitExtent(projection, extent, object) {\n    return fit(projection, function(b) {\n        var w = extent[1][0] - extent[0][0], h = extent[1][1] - extent[0][1], k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])), x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2, y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n        projection.scale(150 * k).translate([\n            x,\n            y\n        ]);\n    }, object);\n}\nfunction fitSize(projection, size, object) {\n    return fitExtent(projection, [\n        [\n            0,\n            0\n        ],\n        size\n    ], object);\n}\nfunction fitWidth(projection, width, object) {\n    return fit(projection, function(b) {\n        var w = +width, k = w / (b[1][0] - b[0][0]), x = (w - k * (b[1][0] + b[0][0])) / 2, y = -k * b[0][1];\n        projection.scale(150 * k).translate([\n            x,\n            y\n        ]);\n    }, object);\n}\nfunction fitHeight(projection, height, object) {\n    return fit(projection, function(b) {\n        var h = +height, k = h / (b[1][1] - b[0][1]), x = -k * b[0][0], y = (h - k * (b[1][1] + b[0][1])) / 2;\n        projection.scale(150 * k).translate([\n            x,\n            y\n        ]);\n    }, object);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/fit.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/gnomonic.js":
/*!************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/gnomonic.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   gnomonicRaw: () => (/* binding */ gnomonicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction gnomonicRaw(x, y) {\n    var cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x) * cy;\n    return [\n        cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x) / k,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y) / k\n    ];\n}\ngnomonicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(_math_js__WEBPACK_IMPORTED_MODULE_0__.atan);\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(gnomonicRaw).scale(144.049).clipAngle(60);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9nbm9tb25pYy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQztBQUNLO0FBQ1g7QUFFN0IsU0FBU0ssWUFBWUMsQ0FBQyxFQUFFQyxDQUFDO0lBQzlCLElBQUlDLEtBQUtQLDZDQUFHQSxDQUFDTSxJQUFJRSxJQUFJUiw2Q0FBR0EsQ0FBQ0ssS0FBS0U7SUFDOUIsT0FBTztRQUFDQSxLQUFLTiw2Q0FBR0EsQ0FBQ0ksS0FBS0c7UUFBR1AsNkNBQUdBLENBQUNLLEtBQUtFO0tBQUU7QUFDdEM7QUFFQUosWUFBWUssTUFBTSxHQUFHUCw4REFBZUEsQ0FBQ0gsMENBQUlBO0FBRXpDLDZCQUFlLHNDQUFXO0lBQ3hCLE9BQU9JLHFEQUFVQSxDQUFDQyxhQUNiTSxLQUFLLENBQUMsU0FDTkMsU0FBUyxDQUFDO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9nbm9tb25pYy5qcz80YTE3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YXRhbiwgY29zLCBzaW59IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5pbXBvcnQge2F6aW11dGhhbEludmVydH0gZnJvbSBcIi4vYXppbXV0aGFsLmpzXCI7XG5pbXBvcnQgcHJvamVjdGlvbiBmcm9tIFwiLi9pbmRleC5qc1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gZ25vbW9uaWNSYXcoeCwgeSkge1xuICB2YXIgY3kgPSBjb3MoeSksIGsgPSBjb3MoeCkgKiBjeTtcbiAgcmV0dXJuIFtjeSAqIHNpbih4KSAvIGssIHNpbih5KSAvIGtdO1xufVxuXG5nbm9tb25pY1Jhdy5pbnZlcnQgPSBhemltdXRoYWxJbnZlcnQoYXRhbik7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcHJvamVjdGlvbihnbm9tb25pY1JhdylcbiAgICAgIC5zY2FsZSgxNDQuMDQ5KVxuICAgICAgLmNsaXBBbmdsZSg2MCk7XG59XG4iXSwibmFtZXMiOlsiYXRhbiIsImNvcyIsInNpbiIsImF6aW11dGhhbEludmVydCIsInByb2plY3Rpb24iLCJnbm9tb25pY1JhdyIsIngiLCJ5IiwiY3kiLCJrIiwiaW52ZXJ0Iiwic2NhbGUiLCJjbGlwQW5nbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/gnomonic.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/identity.js":
/*!************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/identity.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../clip/rectangle.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/../../node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/../../node_modules/d3-geo/src/transform.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/fit.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var k = 1, tx = 0, ty = 0, sx = 1, sy = 1, alpha = 0, ca, sa, x0 = null, y0, x1, y1, kx = 1, ky = 1, transform = (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n        point: function(x, y) {\n            var p = projection([\n                x,\n                y\n            ]);\n            this.stream.point(p[0], p[1]);\n        }\n    }), postclip = _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], cache, cacheStream;\n    function reset() {\n        kx = k * sx;\n        ky = k * sy;\n        cache = cacheStream = null;\n        return projection;\n    }\n    function projection(p) {\n        var x = p[0] * kx, y = p[1] * ky;\n        if (alpha) {\n            var t = y * ca - x * sa;\n            x = x * ca + y * sa;\n            y = t;\n        }\n        return [\n            x + tx,\n            y + ty\n        ];\n    }\n    projection.invert = function(p) {\n        var x = p[0] - tx, y = p[1] - ty;\n        if (alpha) {\n            var t = y * ca + x * sa;\n            x = x * ca - y * sa;\n            y = t;\n        }\n        return [\n            x / kx,\n            y / ky\n        ];\n    };\n    projection.stream = function(stream) {\n        return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n    };\n    projection.postclip = function(_) {\n        return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n    };\n    projection.clipExtent = function(_) {\n        return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) : (0,_clip_rectangle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n    };\n    projection.scale = function(_) {\n        return arguments.length ? (k = +_, reset()) : k;\n    };\n    projection.translate = function(_) {\n        return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [\n            tx,\n            ty\n        ];\n    };\n    projection.angle = function(_) {\n        return arguments.length ? (alpha = _ % 360 * _math_js__WEBPACK_IMPORTED_MODULE_3__.radians, sa = (0,_math_js__WEBPACK_IMPORTED_MODULE_3__.sin)(alpha), ca = (0,_math_js__WEBPACK_IMPORTED_MODULE_3__.cos)(alpha), reset()) : alpha * _math_js__WEBPACK_IMPORTED_MODULE_3__.degrees;\n    };\n    projection.reflectX = function(_) {\n        return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n    };\n    projection.reflectY = function(_) {\n        return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n    };\n    projection.fitExtent = function(extent, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitExtent)(projection, extent, object);\n    };\n    projection.fitSize = function(size, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitSize)(projection, size, object);\n    };\n    projection.fitWidth = function(width, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitWidth)(projection, width, object);\n    };\n    projection.fitHeight = function(height, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitHeight)(projection, height, object);\n    };\n    return projection;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/identity.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/index.js":
/*!*********************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ projection),\n/* harmony export */   projectionMutator: () => (/* binding */ projectionMutator)\n/* harmony export */ });\n/* harmony import */ var _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../clip/antimeridian.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/antimeridian.js\");\n/* harmony import */ var _clip_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../clip/circle.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/circle.js\");\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../clip/rectangle.js */ \"(ssr)/../../node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _compose_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../compose.js */ \"(ssr)/../../node_modules/d3-geo/src/compose.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/../../node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../rotation.js */ \"(ssr)/../../node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/../../node_modules/d3-geo/src/transform.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/fit.js\");\n/* harmony import */ var _resample_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./resample.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/resample.js\");\n\n\n\n\n\n\n\n\n\n\nvar transformRadians = (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n    point: function(x, y) {\n        this.stream.point(x * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, y * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians);\n    }\n});\nfunction transformRotate(rotate) {\n    return (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n        point: function(x, y) {\n            var r = rotate(x, y);\n            return this.stream.point(r[0], r[1]);\n        }\n    });\n}\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n    function transform(x, y) {\n        x *= sx;\n        y *= sy;\n        return [\n            dx + k * x,\n            dy - k * y\n        ];\n    }\n    transform.invert = function(x, y) {\n        return [\n            (x - dx) / k * sx,\n            (dy - y) / k * sy\n        ];\n    };\n    return transform;\n}\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n    if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n    var cosAlpha = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(alpha), sinAlpha = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(alpha), a = cosAlpha * k, b = sinAlpha * k, ai = cosAlpha / k, bi = sinAlpha / k, ci = (sinAlpha * dy - cosAlpha * dx) / k, fi = (sinAlpha * dx + cosAlpha * dy) / k;\n    function transform(x, y) {\n        x *= sx;\n        y *= sy;\n        return [\n            a * x - b * y + dx,\n            dy - b * x - a * y\n        ];\n    }\n    transform.invert = function(x, y) {\n        return [\n            sx * (ai * x - bi * y + ci),\n            sy * (fi - bi * x - ai * y)\n        ];\n    };\n    return transform;\n}\nfunction projection(project) {\n    return projectionMutator(function() {\n        return project;\n    })();\n}\nfunction projectionMutator(projectAt) {\n    var project, k = 150, x = 480, y = 250, lambda = 0, phi = 0, deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, alpha = 0, sx = 1, sy = 1, theta = null, preclip = _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], x0 = null, y0, x1, y1, postclip = _identity_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], delta2 = 0.5, projectResample, projectTransform, projectRotateTransform, cache, cacheStream;\n    function projection(point) {\n        return projectRotateTransform(point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians);\n    }\n    function invert(point) {\n        point = projectRotateTransform.invert(point[0], point[1]);\n        return point && [\n            point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n        ];\n    }\n    projection.stream = function(stream) {\n        return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n    };\n    projection.preclip = function(_) {\n        return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n    };\n    projection.postclip = function(_) {\n        return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n    };\n    projection.clipAngle = function(_) {\n        return arguments.length ? (preclip = +_ ? (0,_clip_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(theta = _ * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians) : (theta = null, _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]), reset()) : theta * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees;\n    };\n    projection.clipExtent = function(_) {\n        return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, _identity_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]) : (0,_clip_rectangle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n    };\n    projection.scale = function(_) {\n        return arguments.length ? (k = +_, recenter()) : k;\n    };\n    projection.translate = function(_) {\n        return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [\n            x,\n            y\n        ];\n    };\n    projection.center = function(_) {\n        return arguments.length ? (lambda = _[0] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi = _[1] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, recenter()) : [\n            lambda * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            phi * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n        ];\n    };\n    projection.rotate = function(_) {\n        return arguments.length ? (deltaLambda = _[0] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, deltaPhi = _[1] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, deltaGamma = _.length > 2 ? _[2] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians : 0, recenter()) : [\n            deltaLambda * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            deltaPhi * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            deltaGamma * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n        ];\n    };\n    projection.angle = function(_) {\n        return arguments.length ? (alpha = _ % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, recenter()) : alpha * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees;\n    };\n    projection.reflectX = function(_) {\n        return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n    };\n    projection.reflectY = function(_) {\n        return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n    };\n    projection.precision = function(_) {\n        return arguments.length ? (projectResample = (0,_resample_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(projectTransform, delta2 = _ * _), reset()) : (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(delta2);\n    };\n    projection.fitExtent = function(extent, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitExtent)(projection, extent, object);\n    };\n    projection.fitSize = function(size, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitSize)(projection, size, object);\n    };\n    projection.fitWidth = function(width, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitWidth)(projection, width, object);\n    };\n    projection.fitHeight = function(height, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitHeight)(projection, height, object);\n    };\n    function recenter() {\n        var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)), transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n        rotate = (0,_rotation_js__WEBPACK_IMPORTED_MODULE_8__.rotateRadians)(deltaLambda, deltaPhi, deltaGamma);\n        projectTransform = (0,_compose_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(project, transform);\n        projectRotateTransform = (0,_compose_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(rotate, projectTransform);\n        projectResample = (0,_resample_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(projectTransform, delta2);\n        return reset();\n    }\n    function reset() {\n        cache = cacheStream = null;\n        return projection;\n    }\n    return function() {\n        project = projectAt.apply(this, arguments);\n        projection.invert = project.invert && invert;\n        return recenter();\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/mercator.js":
/*!************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/mercator.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mercatorProjection: () => (/* binding */ mercatorProjection),\n/* harmony export */   mercatorRaw: () => (/* binding */ mercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../rotation.js */ \"(ssr)/../../node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction mercatorRaw(lambda, phi) {\n    return [\n        lambda,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + phi) / 2))\n    ];\n}\nmercatorRaw.invert = function(x, y) {\n    return [\n        x,\n        2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.exp)(y)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return mercatorProjection(mercatorRaw).scale(961 / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau);\n}\nfunction mercatorProjection(project) {\n    var m = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(project), center = m.center, scale = m.scale, translate = m.translate, clipExtent = m.clipExtent, x0 = null, y0, x1, y1; // clip extent\n    m.scale = function(_) {\n        return arguments.length ? (scale(_), reclip()) : scale();\n    };\n    m.translate = function(_) {\n        return arguments.length ? (translate(_), reclip()) : translate();\n    };\n    m.center = function(_) {\n        return arguments.length ? (center(_), reclip()) : center();\n    };\n    m.clipExtent = function(_) {\n        return arguments.length ? (_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reclip()) : x0 == null ? null : [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n    };\n    function reclip() {\n        var k = _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * scale(), t = m((0,_rotation_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(m.rotate()).invert([\n            0,\n            0\n        ]));\n        return clipExtent(x0 == null ? [\n            [\n                t[0] - k,\n                t[1] - k\n            ],\n            [\n                t[0] + k,\n                t[1] + k\n            ]\n        ] : project === mercatorRaw ? [\n            [\n                Math.max(t[0] - k, x0),\n                y0\n            ],\n            [\n                Math.min(t[0] + k, x1),\n                y1\n            ]\n        ] : [\n            [\n                x0,\n                Math.max(t[1] - k, y0)\n            ],\n            [\n                x1,\n                Math.min(t[1] + k, y1)\n            ]\n        ]);\n    }\n    return reclip();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/mercator.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/naturalEarth1.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/naturalEarth1.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   naturalEarth1Raw: () => (/* binding */ naturalEarth1Raw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\nfunction naturalEarth1Raw(lambda, phi) {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    return [\n        lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n        phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n    ];\n}\nnaturalEarth1Raw.invert = function(x, y) {\n    var phi = y, i = 25, delta;\n    do {\n        var phi2 = phi * phi, phi4 = phi2 * phi2;\n        phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) / (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n    }while ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta) > _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && --i > 0);\n    return [\n        x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n        phi\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(naturalEarth1Raw).scale(175.295);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/naturalEarth1.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/orthographic.js":
/*!****************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/orthographic.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   orthographicRaw: () => (/* binding */ orthographicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction orthographicRaw(x, y) {\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)\n    ];\n}\northographicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(_math_js__WEBPACK_IMPORTED_MODULE_0__.asin);\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(orthographicRaw).scale(249.5).clipAngle(90 + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9vcnRob2dyYXBoaWMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUQ7QUFDSjtBQUNYO0FBRTdCLFNBQVNNLGdCQUFnQkMsQ0FBQyxFQUFFQyxDQUFDO0lBQ2xDLE9BQU87UUFBQ1AsNkNBQUdBLENBQUNPLEtBQUtMLDZDQUFHQSxDQUFDSTtRQUFJSiw2Q0FBR0EsQ0FBQ0s7S0FBRztBQUNsQztBQUVBRixnQkFBZ0JHLE1BQU0sR0FBR0wsOERBQWVBLENBQUNKLDBDQUFJQTtBQUU3Qyw2QkFBZSxzQ0FBVztJQUN4QixPQUFPSyxxREFBVUEsQ0FBQ0MsaUJBQ2JJLEtBQUssQ0FBQyxPQUNOQyxTQUFTLENBQUMsS0FBS1QsNkNBQU9BO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9vcnRob2dyYXBoaWMuanM/NGEyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2FzaW4sIGNvcywgZXBzaWxvbiwgc2lufSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHthemltdXRoYWxJbnZlcnR9IGZyb20gXCIuL2F6aW11dGhhbC5qc1wiO1xuaW1wb3J0IHByb2plY3Rpb24gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIG9ydGhvZ3JhcGhpY1Jhdyh4LCB5KSB7XG4gIHJldHVybiBbY29zKHkpICogc2luKHgpLCBzaW4oeSldO1xufVxuXG5vcnRob2dyYXBoaWNSYXcuaW52ZXJ0ID0gYXppbXV0aGFsSW52ZXJ0KGFzaW4pO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHByb2plY3Rpb24ob3J0aG9ncmFwaGljUmF3KVxuICAgICAgLnNjYWxlKDI0OS41KVxuICAgICAgLmNsaXBBbmdsZSg5MCArIGVwc2lsb24pO1xufVxuIl0sIm5hbWVzIjpbImFzaW4iLCJjb3MiLCJlcHNpbG9uIiwic2luIiwiYXppbXV0aGFsSW52ZXJ0IiwicHJvamVjdGlvbiIsIm9ydGhvZ3JhcGhpY1JhdyIsIngiLCJ5IiwiaW52ZXJ0Iiwic2NhbGUiLCJjbGlwQW5nbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/orthographic.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/resample.js":
/*!************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/resample.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cartesian.js */ \"(ssr)/../../node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/../../node_modules/d3-geo/src/transform.js\");\n\n\n\nvar maxDepth = 16, cosMinDistance = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(30 * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians); // cos(minimum angular distance)\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(project, delta2) {\n    return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\nfunction resampleNone(project) {\n    return (0,_transform_js__WEBPACK_IMPORTED_MODULE_1__.transformer)({\n        point: function(x, y) {\n            x = project(x, y);\n            this.stream.point(x[0], x[1]);\n        }\n    });\n}\nfunction resample(project, delta2) {\n    function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n        var dx = x1 - x0, dy = y1 - y0, d2 = dx * dx + dy * dy;\n        if (d2 > 4 * delta2 && depth--) {\n            var a = a0 + a1, b = b0 + b1, c = c0 + c1, m = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(a * a + b * b + c * c), phi2 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(c /= m), lambda2 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(c) - 1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon || (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda0 - lambda1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? (lambda0 + lambda1) / 2 : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(b, a), p = project(lambda2, phi2), x2 = p[0], y2 = p[1], dx2 = x2 - x0, dy2 = y2 - y0, dz = dy * dx2 - dx * dy2;\n            if (dz * dz / d2 > delta2 // perpendicular projected distance\n             || (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n             || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) {\n                resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n                stream.point(x2, y2);\n                resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n            }\n        }\n    }\n    return function(stream) {\n        var lambda00, x00, y00, a00, b00, c00, lambda0, x0, y0, a0, b0, c0; // previous point\n        var resampleStream = {\n            point: point,\n            lineStart: lineStart,\n            lineEnd: lineEnd,\n            polygonStart: function() {\n                stream.polygonStart();\n                resampleStream.lineStart = ringStart;\n            },\n            polygonEnd: function() {\n                stream.polygonEnd();\n                resampleStream.lineStart = lineStart;\n            }\n        };\n        function point(x, y) {\n            x = project(x, y);\n            stream.point(x[0], x[1]);\n        }\n        function lineStart() {\n            x0 = NaN;\n            resampleStream.point = linePoint;\n            stream.lineStart();\n        }\n        function linePoint(lambda, phi) {\n            var c = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)([\n                lambda,\n                phi\n            ]), p = project(lambda, phi);\n            resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n            stream.point(x0, y0);\n        }\n        function lineEnd() {\n            resampleStream.point = point;\n            stream.lineEnd();\n        }\n        function ringStart() {\n            lineStart();\n            resampleStream.point = ringPoint;\n            resampleStream.lineEnd = ringEnd;\n        }\n        function ringPoint(lambda, phi) {\n            linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n            resampleStream.point = linePoint;\n        }\n        function ringEnd() {\n            resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n            resampleStream.lineEnd = lineEnd;\n            lineEnd();\n        }\n        return resampleStream;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9yZXNhbXBsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBDO0FBQytCO0FBQzdCO0FBRTVDLElBQUlTLFdBQVcsSUFDWEMsaUJBQWlCTiw2Q0FBR0EsQ0FBQyxLQUFLRSw2Q0FBT0EsR0FBRyxnQ0FBZ0M7QUFFeEUsNkJBQWUsb0NBQVNLLE9BQU8sRUFBRUMsTUFBTTtJQUNyQyxPQUFPLENBQUNBLFNBQVNDLFNBQVNGLFNBQVNDLFVBQVVFLGFBQWFIO0FBQzVEO0FBRUEsU0FBU0csYUFBYUgsT0FBTztJQUMzQixPQUFPSCwwREFBV0EsQ0FBQztRQUNqQk8sT0FBTyxTQUFTQyxDQUFDLEVBQUVDLENBQUM7WUFDbEJELElBQUlMLFFBQVFLLEdBQUdDO1lBQ2YsSUFBSSxDQUFDQyxNQUFNLENBQUNILEtBQUssQ0FBQ0MsQ0FBQyxDQUFDLEVBQUUsRUFBRUEsQ0FBQyxDQUFDLEVBQUU7UUFDOUI7SUFDRjtBQUNGO0FBRUEsU0FBU0gsU0FBU0YsT0FBTyxFQUFFQyxNQUFNO0lBRS9CLFNBQVNPLGVBQWVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxPQUFPLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxPQUFPLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEtBQUssRUFBRWQsTUFBTTtRQUM3RixJQUFJZSxLQUFLUCxLQUFLTixJQUNWYyxLQUFLUCxLQUFLTixJQUNWYyxLQUFLRixLQUFLQSxLQUFLQyxLQUFLQTtRQUN4QixJQUFJQyxLQUFLLElBQUl2QixVQUFVb0IsU0FBUztZQUM5QixJQUFJSSxJQUFJYixLQUFLTSxJQUNUUSxJQUFJYixLQUFLTSxJQUNUUSxJQUFJYixLQUFLTSxJQUNUUSxJQUFJaEMsOENBQUlBLENBQUM2QixJQUFJQSxJQUFJQyxJQUFJQSxJQUFJQyxJQUFJQSxJQUM3QkUsT0FBT3RDLDhDQUFJQSxDQUFDb0MsS0FBS0MsSUFDakJFLFVBQVV4Qyw2Q0FBR0EsQ0FBQ0EsNkNBQUdBLENBQUNxQyxLQUFLLEtBQUtqQyw2Q0FBT0EsSUFBSUosNkNBQUdBLENBQUNxQixVQUFVTSxXQUFXdkIsNkNBQU9BLEdBQUcsQ0FBQ2lCLFVBQVVNLE9BQU0sSUFBSyxJQUFJekIsK0NBQUtBLENBQUNrQyxHQUFHRCxJQUM3R00sSUFBSS9CLFFBQVE4QixTQUFTRCxPQUNyQkcsS0FBS0QsQ0FBQyxDQUFDLEVBQUUsRUFDVEUsS0FBS0YsQ0FBQyxDQUFDLEVBQUUsRUFDVEcsTUFBTUYsS0FBS3ZCLElBQ1gwQixNQUFNRixLQUFLdkIsSUFDWDBCLEtBQUtiLEtBQUtXLE1BQU1aLEtBQUthO1lBQ3pCLElBQUlDLEtBQUtBLEtBQUtaLEtBQUt2QixPQUFPLG1DQUFtQztnQkFDdERYLDZDQUFHQSxDQUFDLENBQUNnQyxLQUFLWSxNQUFNWCxLQUFLWSxHQUFFLElBQUtYLEtBQUssT0FBTyxJQUFJLDJCQUEyQjtnQkFDdkVaLEtBQUtNLEtBQUtMLEtBQUtNLEtBQUtMLEtBQUtNLEtBQUtyQixnQkFBZ0I7Z0JBQ25EUyxlQUFlQyxJQUFJQyxJQUFJQyxTQUFTQyxJQUFJQyxJQUFJQyxJQUFJa0IsSUFBSUMsSUFBSUgsU0FBU0wsS0FBS0csR0FBR0YsS0FBS0UsR0FBR0QsR0FBR04sT0FBT2Q7Z0JBQ3ZGQSxPQUFPSCxLQUFLLENBQUM0QixJQUFJQztnQkFDakJ6QixlQUFld0IsSUFBSUMsSUFBSUgsU0FBU0wsR0FBR0MsR0FBR0MsR0FBR1osSUFBSUMsSUFBSUMsU0FBU0MsSUFBSUMsSUFBSUMsSUFBSUMsT0FBT2Q7WUFDL0U7UUFDRjtJQUNGO0lBQ0EsT0FBTyxTQUFTQSxNQUFNO1FBQ3BCLElBQUk4QixVQUFVQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUFLQyxLQUM5Qi9CLFNBQVNGLElBQUlDLElBQUlFLElBQUlDLElBQUlDLElBQUksaUJBQWlCO1FBRWxELElBQUk2QixpQkFBaUI7WUFDbkJ2QyxPQUFPQTtZQUNQd0MsV0FBV0E7WUFDWEMsU0FBU0E7WUFDVEMsY0FBYztnQkFBYXZDLE9BQU91QyxZQUFZO2dCQUFJSCxlQUFlQyxTQUFTLEdBQUdHO1lBQVc7WUFDeEZDLFlBQVk7Z0JBQWF6QyxPQUFPeUMsVUFBVTtnQkFBSUwsZUFBZUMsU0FBUyxHQUFHQTtZQUFXO1FBQ3RGO1FBRUEsU0FBU3hDLE1BQU1DLENBQUMsRUFBRUMsQ0FBQztZQUNqQkQsSUFBSUwsUUFBUUssR0FBR0M7WUFDZkMsT0FBT0gsS0FBSyxDQUFDQyxDQUFDLENBQUMsRUFBRSxFQUFFQSxDQUFDLENBQUMsRUFBRTtRQUN6QjtRQUVBLFNBQVN1QztZQUNQbkMsS0FBS3dDO1lBQ0xOLGVBQWV2QyxLQUFLLEdBQUc4QztZQUN2QjNDLE9BQU9xQyxTQUFTO1FBQ2xCO1FBRUEsU0FBU00sVUFBVUMsTUFBTSxFQUFFQyxHQUFHO1lBQzVCLElBQUl6QixJQUFJdEMsd0RBQVNBLENBQUM7Z0JBQUM4RDtnQkFBUUM7YUFBSSxHQUFHckIsSUFBSS9CLFFBQVFtRCxRQUFRQztZQUN0RDVDLGVBQWVDLElBQUlDLElBQUlDLFNBQVNDLElBQUlDLElBQUlDLElBQUlMLEtBQUtzQixDQUFDLENBQUMsRUFBRSxFQUFFckIsS0FBS3FCLENBQUMsQ0FBQyxFQUFFLEVBQUVwQixVQUFVd0MsUUFBUXZDLEtBQUtlLENBQUMsQ0FBQyxFQUFFLEVBQUVkLEtBQUtjLENBQUMsQ0FBQyxFQUFFLEVBQUViLEtBQUthLENBQUMsQ0FBQyxFQUFFLEVBQUU3QixVQUFVUztZQUMvSEEsT0FBT0gsS0FBSyxDQUFDSyxJQUFJQztRQUNuQjtRQUVBLFNBQVNtQztZQUNQRixlQUFldkMsS0FBSyxHQUFHQTtZQUN2QkcsT0FBT3NDLE9BQU87UUFDaEI7UUFFQSxTQUFTRTtZQUNQSDtZQUNBRCxlQUFldkMsS0FBSyxHQUFHaUQ7WUFDdkJWLGVBQWVFLE9BQU8sR0FBR1M7UUFDM0I7UUFFQSxTQUFTRCxVQUFVRixNQUFNLEVBQUVDLEdBQUc7WUFDNUJGLFVBQVViLFdBQVdjLFFBQVFDLE1BQU1kLE1BQU03QixJQUFJOEIsTUFBTTdCLElBQUk4QixNQUFNNUIsSUFBSTZCLE1BQU01QixJQUFJNkIsTUFBTTVCO1lBQ2pGNkIsZUFBZXZDLEtBQUssR0FBRzhDO1FBQ3pCO1FBRUEsU0FBU0k7WUFDUDlDLGVBQWVDLElBQUlDLElBQUlDLFNBQVNDLElBQUlDLElBQUlDLElBQUl3QixLQUFLQyxLQUFLRixVQUFVRyxLQUFLQyxLQUFLQyxLQUFLNUMsVUFBVVM7WUFDekZvQyxlQUFlRSxPQUFPLEdBQUdBO1lBQ3pCQTtRQUNGO1FBRUEsT0FBT0Y7SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9yZXNhbXBsZS5qcz80NmZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y2FydGVzaWFufSBmcm9tIFwiLi4vY2FydGVzaWFuLmpzXCI7XG5pbXBvcnQge2FicywgYXNpbiwgYXRhbjIsIGNvcywgZXBzaWxvbiwgcmFkaWFucywgc3FydH0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7dHJhbnNmb3JtZXJ9IGZyb20gXCIuLi90cmFuc2Zvcm0uanNcIjtcblxudmFyIG1heERlcHRoID0gMTYsIC8vIG1heGltdW0gZGVwdGggb2Ygc3ViZGl2aXNpb25cbiAgICBjb3NNaW5EaXN0YW5jZSA9IGNvcygzMCAqIHJhZGlhbnMpOyAvLyBjb3MobWluaW11bSBhbmd1bGFyIGRpc3RhbmNlKVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwcm9qZWN0LCBkZWx0YTIpIHtcbiAgcmV0dXJuICtkZWx0YTIgPyByZXNhbXBsZShwcm9qZWN0LCBkZWx0YTIpIDogcmVzYW1wbGVOb25lKHByb2plY3QpO1xufVxuXG5mdW5jdGlvbiByZXNhbXBsZU5vbmUocHJvamVjdCkge1xuICByZXR1cm4gdHJhbnNmb3JtZXIoe1xuICAgIHBvaW50OiBmdW5jdGlvbih4LCB5KSB7XG4gICAgICB4ID0gcHJvamVjdCh4LCB5KTtcbiAgICAgIHRoaXMuc3RyZWFtLnBvaW50KHhbMF0sIHhbMV0pO1xuICAgIH1cbiAgfSk7XG59XG5cbmZ1bmN0aW9uIHJlc2FtcGxlKHByb2plY3QsIGRlbHRhMikge1xuXG4gIGZ1bmN0aW9uIHJlc2FtcGxlTGluZVRvKHgwLCB5MCwgbGFtYmRhMCwgYTAsIGIwLCBjMCwgeDEsIHkxLCBsYW1iZGExLCBhMSwgYjEsIGMxLCBkZXB0aCwgc3RyZWFtKSB7XG4gICAgdmFyIGR4ID0geDEgLSB4MCxcbiAgICAgICAgZHkgPSB5MSAtIHkwLFxuICAgICAgICBkMiA9IGR4ICogZHggKyBkeSAqIGR5O1xuICAgIGlmIChkMiA+IDQgKiBkZWx0YTIgJiYgZGVwdGgtLSkge1xuICAgICAgdmFyIGEgPSBhMCArIGExLFxuICAgICAgICAgIGIgPSBiMCArIGIxLFxuICAgICAgICAgIGMgPSBjMCArIGMxLFxuICAgICAgICAgIG0gPSBzcXJ0KGEgKiBhICsgYiAqIGIgKyBjICogYyksXG4gICAgICAgICAgcGhpMiA9IGFzaW4oYyAvPSBtKSxcbiAgICAgICAgICBsYW1iZGEyID0gYWJzKGFicyhjKSAtIDEpIDwgZXBzaWxvbiB8fCBhYnMobGFtYmRhMCAtIGxhbWJkYTEpIDwgZXBzaWxvbiA/IChsYW1iZGEwICsgbGFtYmRhMSkgLyAyIDogYXRhbjIoYiwgYSksXG4gICAgICAgICAgcCA9IHByb2plY3QobGFtYmRhMiwgcGhpMiksXG4gICAgICAgICAgeDIgPSBwWzBdLFxuICAgICAgICAgIHkyID0gcFsxXSxcbiAgICAgICAgICBkeDIgPSB4MiAtIHgwLFxuICAgICAgICAgIGR5MiA9IHkyIC0geTAsXG4gICAgICAgICAgZHogPSBkeSAqIGR4MiAtIGR4ICogZHkyO1xuICAgICAgaWYgKGR6ICogZHogLyBkMiA+IGRlbHRhMiAvLyBwZXJwZW5kaWN1bGFyIHByb2plY3RlZCBkaXN0YW5jZVxuICAgICAgICAgIHx8IGFicygoZHggKiBkeDIgKyBkeSAqIGR5MikgLyBkMiAtIDAuNSkgPiAwLjMgLy8gbWlkcG9pbnQgY2xvc2UgdG8gYW4gZW5kXG4gICAgICAgICAgfHwgYTAgKiBhMSArIGIwICogYjEgKyBjMCAqIGMxIDwgY29zTWluRGlzdGFuY2UpIHsgLy8gYW5ndWxhciBkaXN0YW5jZVxuICAgICAgICByZXNhbXBsZUxpbmVUbyh4MCwgeTAsIGxhbWJkYTAsIGEwLCBiMCwgYzAsIHgyLCB5MiwgbGFtYmRhMiwgYSAvPSBtLCBiIC89IG0sIGMsIGRlcHRoLCBzdHJlYW0pO1xuICAgICAgICBzdHJlYW0ucG9pbnQoeDIsIHkyKTtcbiAgICAgICAgcmVzYW1wbGVMaW5lVG8oeDIsIHkyLCBsYW1iZGEyLCBhLCBiLCBjLCB4MSwgeTEsIGxhbWJkYTEsIGExLCBiMSwgYzEsIGRlcHRoLCBzdHJlYW0pO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gZnVuY3Rpb24oc3RyZWFtKSB7XG4gICAgdmFyIGxhbWJkYTAwLCB4MDAsIHkwMCwgYTAwLCBiMDAsIGMwMCwgLy8gZmlyc3QgcG9pbnRcbiAgICAgICAgbGFtYmRhMCwgeDAsIHkwLCBhMCwgYjAsIGMwOyAvLyBwcmV2aW91cyBwb2ludFxuXG4gICAgdmFyIHJlc2FtcGxlU3RyZWFtID0ge1xuICAgICAgcG9pbnQ6IHBvaW50LFxuICAgICAgbGluZVN0YXJ0OiBsaW5lU3RhcnQsXG4gICAgICBsaW5lRW5kOiBsaW5lRW5kLFxuICAgICAgcG9seWdvblN0YXJ0OiBmdW5jdGlvbigpIHsgc3RyZWFtLnBvbHlnb25TdGFydCgpOyByZXNhbXBsZVN0cmVhbS5saW5lU3RhcnQgPSByaW5nU3RhcnQ7IH0sXG4gICAgICBwb2x5Z29uRW5kOiBmdW5jdGlvbigpIHsgc3RyZWFtLnBvbHlnb25FbmQoKTsgcmVzYW1wbGVTdHJlYW0ubGluZVN0YXJ0ID0gbGluZVN0YXJ0OyB9XG4gICAgfTtcblxuICAgIGZ1bmN0aW9uIHBvaW50KHgsIHkpIHtcbiAgICAgIHggPSBwcm9qZWN0KHgsIHkpO1xuICAgICAgc3RyZWFtLnBvaW50KHhbMF0sIHhbMV0pO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGxpbmVTdGFydCgpIHtcbiAgICAgIHgwID0gTmFOO1xuICAgICAgcmVzYW1wbGVTdHJlYW0ucG9pbnQgPSBsaW5lUG9pbnQ7XG4gICAgICBzdHJlYW0ubGluZVN0YXJ0KCk7XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gbGluZVBvaW50KGxhbWJkYSwgcGhpKSB7XG4gICAgICB2YXIgYyA9IGNhcnRlc2lhbihbbGFtYmRhLCBwaGldKSwgcCA9IHByb2plY3QobGFtYmRhLCBwaGkpO1xuICAgICAgcmVzYW1wbGVMaW5lVG8oeDAsIHkwLCBsYW1iZGEwLCBhMCwgYjAsIGMwLCB4MCA9IHBbMF0sIHkwID0gcFsxXSwgbGFtYmRhMCA9IGxhbWJkYSwgYTAgPSBjWzBdLCBiMCA9IGNbMV0sIGMwID0gY1syXSwgbWF4RGVwdGgsIHN0cmVhbSk7XG4gICAgICBzdHJlYW0ucG9pbnQoeDAsIHkwKTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiBsaW5lRW5kKCkge1xuICAgICAgcmVzYW1wbGVTdHJlYW0ucG9pbnQgPSBwb2ludDtcbiAgICAgIHN0cmVhbS5saW5lRW5kKCk7XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gcmluZ1N0YXJ0KCkge1xuICAgICAgbGluZVN0YXJ0KCk7XG4gICAgICByZXNhbXBsZVN0cmVhbS5wb2ludCA9IHJpbmdQb2ludDtcbiAgICAgIHJlc2FtcGxlU3RyZWFtLmxpbmVFbmQgPSByaW5nRW5kO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIHJpbmdQb2ludChsYW1iZGEsIHBoaSkge1xuICAgICAgbGluZVBvaW50KGxhbWJkYTAwID0gbGFtYmRhLCBwaGkpLCB4MDAgPSB4MCwgeTAwID0geTAsIGEwMCA9IGEwLCBiMDAgPSBiMCwgYzAwID0gYzA7XG4gICAgICByZXNhbXBsZVN0cmVhbS5wb2ludCA9IGxpbmVQb2ludDtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiByaW5nRW5kKCkge1xuICAgICAgcmVzYW1wbGVMaW5lVG8oeDAsIHkwLCBsYW1iZGEwLCBhMCwgYjAsIGMwLCB4MDAsIHkwMCwgbGFtYmRhMDAsIGEwMCwgYjAwLCBjMDAsIG1heERlcHRoLCBzdHJlYW0pO1xuICAgICAgcmVzYW1wbGVTdHJlYW0ubGluZUVuZCA9IGxpbmVFbmQ7XG4gICAgICBsaW5lRW5kKCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc2FtcGxlU3RyZWFtO1xuICB9O1xufVxuIl0sIm5hbWVzIjpbImNhcnRlc2lhbiIsImFicyIsImFzaW4iLCJhdGFuMiIsImNvcyIsImVwc2lsb24iLCJyYWRpYW5zIiwic3FydCIsInRyYW5zZm9ybWVyIiwibWF4RGVwdGgiLCJjb3NNaW5EaXN0YW5jZSIsInByb2plY3QiLCJkZWx0YTIiLCJyZXNhbXBsZSIsInJlc2FtcGxlTm9uZSIsInBvaW50IiwieCIsInkiLCJzdHJlYW0iLCJyZXNhbXBsZUxpbmVUbyIsIngwIiwieTAiLCJsYW1iZGEwIiwiYTAiLCJiMCIsImMwIiwieDEiLCJ5MSIsImxhbWJkYTEiLCJhMSIsImIxIiwiYzEiLCJkZXB0aCIsImR4IiwiZHkiLCJkMiIsImEiLCJiIiwiYyIsIm0iLCJwaGkyIiwibGFtYmRhMiIsInAiLCJ4MiIsInkyIiwiZHgyIiwiZHkyIiwiZHoiLCJsYW1iZGEwMCIsIngwMCIsInkwMCIsImEwMCIsImIwMCIsImMwMCIsInJlc2FtcGxlU3RyZWFtIiwibGluZVN0YXJ0IiwibGluZUVuZCIsInBvbHlnb25TdGFydCIsInJpbmdTdGFydCIsInBvbHlnb25FbmQiLCJOYU4iLCJsaW5lUG9pbnQiLCJsYW1iZGEiLCJwaGkiLCJyaW5nUG9pbnQiLCJyaW5nRW5kIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/resample.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/stereographic.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/stereographic.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   stereographicRaw: () => (/* binding */ stereographicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction stereographicRaw(x, y) {\n    var cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = 1 + (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x) * cy;\n    return [\n        cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x) / k,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y) / k\n    ];\n}\nstereographicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(function(z) {\n    return 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)(z);\n});\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(stereographicRaw).scale(250).clipAngle(142);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9zdGVyZW9ncmFwaGljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBDO0FBQ0s7QUFDWDtBQUU3QixTQUFTSyxpQkFBaUJDLENBQUMsRUFBRUMsQ0FBQztJQUNuQyxJQUFJQyxLQUFLUCw2Q0FBR0EsQ0FBQ00sSUFBSUUsSUFBSSxJQUFJUiw2Q0FBR0EsQ0FBQ0ssS0FBS0U7SUFDbEMsT0FBTztRQUFDQSxLQUFLTiw2Q0FBR0EsQ0FBQ0ksS0FBS0c7UUFBR1AsNkNBQUdBLENBQUNLLEtBQUtFO0tBQUU7QUFDdEM7QUFFQUosaUJBQWlCSyxNQUFNLEdBQUdQLDhEQUFlQSxDQUFDLFNBQVNRLENBQUM7SUFDbEQsT0FBTyxJQUFJWCw4Q0FBSUEsQ0FBQ1c7QUFDbEI7QUFFQSw2QkFBZSxzQ0FBVztJQUN4QixPQUFPUCxxREFBVUEsQ0FBQ0Msa0JBQ2JPLEtBQUssQ0FBQyxLQUNOQyxTQUFTLENBQUM7QUFDakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL3N0ZXJlb2dyYXBoaWMuanM/YTAwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2F0YW4sIGNvcywgc2lufSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHthemltdXRoYWxJbnZlcnR9IGZyb20gXCIuL2F6aW11dGhhbC5qc1wiO1xuaW1wb3J0IHByb2plY3Rpb24gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIHN0ZXJlb2dyYXBoaWNSYXcoeCwgeSkge1xuICB2YXIgY3kgPSBjb3MoeSksIGsgPSAxICsgY29zKHgpICogY3k7XG4gIHJldHVybiBbY3kgKiBzaW4oeCkgLyBrLCBzaW4oeSkgLyBrXTtcbn1cblxuc3RlcmVvZ3JhcGhpY1Jhdy5pbnZlcnQgPSBhemltdXRoYWxJbnZlcnQoZnVuY3Rpb24oeikge1xuICByZXR1cm4gMiAqIGF0YW4oeik7XG59KTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHJldHVybiBwcm9qZWN0aW9uKHN0ZXJlb2dyYXBoaWNSYXcpXG4gICAgICAuc2NhbGUoMjUwKVxuICAgICAgLmNsaXBBbmdsZSgxNDIpO1xufVxuIl0sIm5hbWVzIjpbImF0YW4iLCJjb3MiLCJzaW4iLCJhemltdXRoYWxJbnZlcnQiLCJwcm9qZWN0aW9uIiwic3RlcmVvZ3JhcGhpY1JhdyIsIngiLCJ5IiwiY3kiLCJrIiwiaW52ZXJ0IiwieiIsInNjYWxlIiwiY2xpcEFuZ2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/stereographic.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/projection/transverseMercator.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/d3-geo/src/projection/transverseMercator.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   transverseMercatorRaw: () => (/* binding */ transverseMercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _mercator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mercator.js */ \"(ssr)/../../node_modules/d3-geo/src/projection/mercator.js\");\n\n\nfunction transverseMercatorRaw(lambda, phi) {\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + phi) / 2)),\n        -lambda\n    ];\n}\ntransverseMercatorRaw.invert = function(x, y) {\n    return [\n        -y,\n        2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.exp)(x)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var m = (0,_mercator_js__WEBPACK_IMPORTED_MODULE_1__.mercatorProjection)(transverseMercatorRaw), center = m.center, rotate = m.rotate;\n    m.center = function(_) {\n        return arguments.length ? center([\n            -_[1],\n            _[0]\n        ]) : (_ = center(), [\n            _[1],\n            -_[0]\n        ]);\n    };\n    m.rotate = function(_) {\n        return arguments.length ? rotate([\n            _[0],\n            _[1],\n            _.length > 2 ? _[2] + 90 : 90\n        ]) : (_ = rotate(), [\n            _[0],\n            _[1],\n            _[2] - 90\n        ]);\n    };\n    return rotate([\n        0,\n        0,\n        90\n    ]).scale(159.155);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/projection/transverseMercator.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/rotation.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-geo/src/rotation.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   rotateRadians: () => (/* binding */ rotateRadians)\n/* harmony export */ });\n/* harmony import */ var _compose_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compose.js */ \"(ssr)/../../node_modules/d3-geo/src/compose.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/../../node_modules/d3-geo/src/math.js\");\n\n\nfunction rotationIdentity(lambda, phi) {\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda) > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) lambda -= Math.round(lambda / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n    return [\n        lambda,\n        phi\n    ];\n}\nrotationIdentity.invert = rotationIdentity;\nfunction rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n    return (deltaLambda %= _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) ? deltaPhi || deltaGamma ? (0,_compose_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma)) : rotationLambda(deltaLambda) : deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma) : rotationIdentity;\n}\nfunction forwardRotationLambda(deltaLambda) {\n    return function(lambda, phi) {\n        lambda += deltaLambda;\n        if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda) > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) lambda -= Math.round(lambda / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n        return [\n            lambda,\n            phi\n        ];\n    };\n}\nfunction rotationLambda(deltaLambda) {\n    var rotation = forwardRotationLambda(deltaLambda);\n    rotation.invert = forwardRotationLambda(-deltaLambda);\n    return rotation;\n}\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n    var cosDeltaPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(deltaPhi), sinDeltaPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(deltaPhi), cosDeltaGamma = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(deltaGamma), sinDeltaGamma = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(deltaGamma);\n    function rotation(lambda, phi) {\n        var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi), x = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * cosPhi, y = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda) * cosPhi, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi), k = z * cosDeltaPhi + x * sinDeltaPhi;\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(k * cosDeltaGamma + y * sinDeltaGamma)\n        ];\n    }\n    rotation.invert = function(lambda, phi) {\n        var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi), x = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * cosPhi, y = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda) * cosPhi, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi), k = z * cosDeltaGamma - y * sinDeltaGamma;\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(k * cosDeltaPhi - x * sinDeltaPhi)\n        ];\n    };\n    return rotation;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(rotate) {\n    rotate = rotateRadians(rotate[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, rotate[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, rotate.length > 2 ? rotate[2] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians : 0);\n    function forward(coordinates) {\n        coordinates = rotate(coordinates[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, coordinates[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians);\n        return coordinates[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates;\n    }\n    forward.invert = function(coordinates) {\n        coordinates = rotate.invert(coordinates[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, coordinates[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians);\n        return coordinates[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates;\n    };\n    return forward;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/rotation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/stream.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-geo/src/stream.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction streamGeometry(geometry, stream) {\n    if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n        streamGeometryType[geometry.type](geometry, stream);\n    }\n}\nvar streamObjectType = {\n    Feature: function(object, stream) {\n        streamGeometry(object.geometry, stream);\n    },\n    FeatureCollection: function(object, stream) {\n        var features = object.features, i = -1, n = features.length;\n        while(++i < n)streamGeometry(features[i].geometry, stream);\n    }\n};\nvar streamGeometryType = {\n    Sphere: function(object, stream) {\n        stream.sphere();\n    },\n    Point: function(object, stream) {\n        object = object.coordinates;\n        stream.point(object[0], object[1], object[2]);\n    },\n    MultiPoint: function(object, stream) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)object = coordinates[i], stream.point(object[0], object[1], object[2]);\n    },\n    LineString: function(object, stream) {\n        streamLine(object.coordinates, stream, 0);\n    },\n    MultiLineString: function(object, stream) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)streamLine(coordinates[i], stream, 0);\n    },\n    Polygon: function(object, stream) {\n        streamPolygon(object.coordinates, stream);\n    },\n    MultiPolygon: function(object, stream) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)streamPolygon(coordinates[i], stream);\n    },\n    GeometryCollection: function(object, stream) {\n        var geometries = object.geometries, i = -1, n = geometries.length;\n        while(++i < n)streamGeometry(geometries[i], stream);\n    }\n};\nfunction streamLine(coordinates, stream, closed) {\n    var i = -1, n = coordinates.length - closed, coordinate;\n    stream.lineStart();\n    while(++i < n)coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n    stream.lineEnd();\n}\nfunction streamPolygon(coordinates, stream) {\n    var i = -1, n = coordinates.length;\n    stream.polygonStart();\n    while(++i < n)streamLine(coordinates[i], stream, 1);\n    stream.polygonEnd();\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object, stream) {\n    if (object && streamObjectType.hasOwnProperty(object.type)) {\n        streamObjectType[object.type](object, stream);\n    } else {\n        streamGeometry(object, stream);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvc3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxTQUFTQSxlQUFlQyxRQUFRLEVBQUVDLE1BQU07SUFDdEMsSUFBSUQsWUFBWUUsbUJBQW1CQyxjQUFjLENBQUNILFNBQVNJLElBQUksR0FBRztRQUNoRUYsa0JBQWtCLENBQUNGLFNBQVNJLElBQUksQ0FBQyxDQUFDSixVQUFVQztJQUM5QztBQUNGO0FBRUEsSUFBSUksbUJBQW1CO0lBQ3JCQyxTQUFTLFNBQVNDLE1BQU0sRUFBRU4sTUFBTTtRQUM5QkYsZUFBZVEsT0FBT1AsUUFBUSxFQUFFQztJQUNsQztJQUNBTyxtQkFBbUIsU0FBU0QsTUFBTSxFQUFFTixNQUFNO1FBQ3hDLElBQUlRLFdBQVdGLE9BQU9FLFFBQVEsRUFBRUMsSUFBSSxDQUFDLEdBQUdDLElBQUlGLFNBQVNHLE1BQU07UUFDM0QsTUFBTyxFQUFFRixJQUFJQyxFQUFHWixlQUFlVSxRQUFRLENBQUNDLEVBQUUsQ0FBQ1YsUUFBUSxFQUFFQztJQUN2RDtBQUNGO0FBRUEsSUFBSUMscUJBQXFCO0lBQ3ZCVyxRQUFRLFNBQVNOLE1BQU0sRUFBRU4sTUFBTTtRQUM3QkEsT0FBT2EsTUFBTTtJQUNmO0lBQ0FDLE9BQU8sU0FBU1IsTUFBTSxFQUFFTixNQUFNO1FBQzVCTSxTQUFTQSxPQUFPUyxXQUFXO1FBQzNCZixPQUFPZ0IsS0FBSyxDQUFDVixNQUFNLENBQUMsRUFBRSxFQUFFQSxNQUFNLENBQUMsRUFBRSxFQUFFQSxNQUFNLENBQUMsRUFBRTtJQUM5QztJQUNBVyxZQUFZLFNBQVNYLE1BQU0sRUFBRU4sTUFBTTtRQUNqQyxJQUFJZSxjQUFjVCxPQUFPUyxXQUFXLEVBQUVOLElBQUksQ0FBQyxHQUFHQyxJQUFJSyxZQUFZSixNQUFNO1FBQ3BFLE1BQU8sRUFBRUYsSUFBSUMsRUFBR0osU0FBU1MsV0FBVyxDQUFDTixFQUFFLEVBQUVULE9BQU9nQixLQUFLLENBQUNWLE1BQU0sQ0FBQyxFQUFFLEVBQUVBLE1BQU0sQ0FBQyxFQUFFLEVBQUVBLE1BQU0sQ0FBQyxFQUFFO0lBQ3ZGO0lBQ0FZLFlBQVksU0FBU1osTUFBTSxFQUFFTixNQUFNO1FBQ2pDbUIsV0FBV2IsT0FBT1MsV0FBVyxFQUFFZixRQUFRO0lBQ3pDO0lBQ0FvQixpQkFBaUIsU0FBU2QsTUFBTSxFQUFFTixNQUFNO1FBQ3RDLElBQUllLGNBQWNULE9BQU9TLFdBQVcsRUFBRU4sSUFBSSxDQUFDLEdBQUdDLElBQUlLLFlBQVlKLE1BQU07UUFDcEUsTUFBTyxFQUFFRixJQUFJQyxFQUFHUyxXQUFXSixXQUFXLENBQUNOLEVBQUUsRUFBRVQsUUFBUTtJQUNyRDtJQUNBcUIsU0FBUyxTQUFTZixNQUFNLEVBQUVOLE1BQU07UUFDOUJzQixjQUFjaEIsT0FBT1MsV0FBVyxFQUFFZjtJQUNwQztJQUNBdUIsY0FBYyxTQUFTakIsTUFBTSxFQUFFTixNQUFNO1FBQ25DLElBQUllLGNBQWNULE9BQU9TLFdBQVcsRUFBRU4sSUFBSSxDQUFDLEdBQUdDLElBQUlLLFlBQVlKLE1BQU07UUFDcEUsTUFBTyxFQUFFRixJQUFJQyxFQUFHWSxjQUFjUCxXQUFXLENBQUNOLEVBQUUsRUFBRVQ7SUFDaEQ7SUFDQXdCLG9CQUFvQixTQUFTbEIsTUFBTSxFQUFFTixNQUFNO1FBQ3pDLElBQUl5QixhQUFhbkIsT0FBT21CLFVBQVUsRUFBRWhCLElBQUksQ0FBQyxHQUFHQyxJQUFJZSxXQUFXZCxNQUFNO1FBQ2pFLE1BQU8sRUFBRUYsSUFBSUMsRUFBR1osZUFBZTJCLFVBQVUsQ0FBQ2hCLEVBQUUsRUFBRVQ7SUFDaEQ7QUFDRjtBQUVBLFNBQVNtQixXQUFXSixXQUFXLEVBQUVmLE1BQU0sRUFBRTBCLE1BQU07SUFDN0MsSUFBSWpCLElBQUksQ0FBQyxHQUFHQyxJQUFJSyxZQUFZSixNQUFNLEdBQUdlLFFBQVFDO0lBQzdDM0IsT0FBTzRCLFNBQVM7SUFDaEIsTUFBTyxFQUFFbkIsSUFBSUMsRUFBR2lCLGFBQWFaLFdBQVcsQ0FBQ04sRUFBRSxFQUFFVCxPQUFPZ0IsS0FBSyxDQUFDVyxVQUFVLENBQUMsRUFBRSxFQUFFQSxVQUFVLENBQUMsRUFBRSxFQUFFQSxVQUFVLENBQUMsRUFBRTtJQUNyRzNCLE9BQU82QixPQUFPO0FBQ2hCO0FBRUEsU0FBU1AsY0FBY1AsV0FBVyxFQUFFZixNQUFNO0lBQ3hDLElBQUlTLElBQUksQ0FBQyxHQUFHQyxJQUFJSyxZQUFZSixNQUFNO0lBQ2xDWCxPQUFPOEIsWUFBWTtJQUNuQixNQUFPLEVBQUVyQixJQUFJQyxFQUFHUyxXQUFXSixXQUFXLENBQUNOLEVBQUUsRUFBRVQsUUFBUTtJQUNuREEsT0FBTytCLFVBQVU7QUFDbkI7QUFFQSw2QkFBZSxvQ0FBU3pCLE1BQU0sRUFBRU4sTUFBTTtJQUNwQyxJQUFJTSxVQUFVRixpQkFBaUJGLGNBQWMsQ0FBQ0ksT0FBT0gsSUFBSSxHQUFHO1FBQzFEQyxnQkFBZ0IsQ0FBQ0UsT0FBT0gsSUFBSSxDQUFDLENBQUNHLFFBQVFOO0lBQ3hDLE9BQU87UUFDTEYsZUFBZVEsUUFBUU47SUFDekI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3N0cmVhbS5qcz80Mjg0Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHN0cmVhbUdlb21ldHJ5KGdlb21ldHJ5LCBzdHJlYW0pIHtcbiAgaWYgKGdlb21ldHJ5ICYmIHN0cmVhbUdlb21ldHJ5VHlwZS5oYXNPd25Qcm9wZXJ0eShnZW9tZXRyeS50eXBlKSkge1xuICAgIHN0cmVhbUdlb21ldHJ5VHlwZVtnZW9tZXRyeS50eXBlXShnZW9tZXRyeSwgc3RyZWFtKTtcbiAgfVxufVxuXG52YXIgc3RyZWFtT2JqZWN0VHlwZSA9IHtcbiAgRmVhdHVyZTogZnVuY3Rpb24ob2JqZWN0LCBzdHJlYW0pIHtcbiAgICBzdHJlYW1HZW9tZXRyeShvYmplY3QuZ2VvbWV0cnksIHN0cmVhbSk7XG4gIH0sXG4gIEZlYXR1cmVDb2xsZWN0aW9uOiBmdW5jdGlvbihvYmplY3QsIHN0cmVhbSkge1xuICAgIHZhciBmZWF0dXJlcyA9IG9iamVjdC5mZWF0dXJlcywgaSA9IC0xLCBuID0gZmVhdHVyZXMubGVuZ3RoO1xuICAgIHdoaWxlICgrK2kgPCBuKSBzdHJlYW1HZW9tZXRyeShmZWF0dXJlc1tpXS5nZW9tZXRyeSwgc3RyZWFtKTtcbiAgfVxufTtcblxudmFyIHN0cmVhbUdlb21ldHJ5VHlwZSA9IHtcbiAgU3BoZXJlOiBmdW5jdGlvbihvYmplY3QsIHN0cmVhbSkge1xuICAgIHN0cmVhbS5zcGhlcmUoKTtcbiAgfSxcbiAgUG9pbnQ6IGZ1bmN0aW9uKG9iamVjdCwgc3RyZWFtKSB7XG4gICAgb2JqZWN0ID0gb2JqZWN0LmNvb3JkaW5hdGVzO1xuICAgIHN0cmVhbS5wb2ludChvYmplY3RbMF0sIG9iamVjdFsxXSwgb2JqZWN0WzJdKTtcbiAgfSxcbiAgTXVsdGlQb2ludDogZnVuY3Rpb24ob2JqZWN0LCBzdHJlYW0pIHtcbiAgICB2YXIgY29vcmRpbmF0ZXMgPSBvYmplY3QuY29vcmRpbmF0ZXMsIGkgPSAtMSwgbiA9IGNvb3JkaW5hdGVzLmxlbmd0aDtcbiAgICB3aGlsZSAoKytpIDwgbikgb2JqZWN0ID0gY29vcmRpbmF0ZXNbaV0sIHN0cmVhbS5wb2ludChvYmplY3RbMF0sIG9iamVjdFsxXSwgb2JqZWN0WzJdKTtcbiAgfSxcbiAgTGluZVN0cmluZzogZnVuY3Rpb24ob2JqZWN0LCBzdHJlYW0pIHtcbiAgICBzdHJlYW1MaW5lKG9iamVjdC5jb29yZGluYXRlcywgc3RyZWFtLCAwKTtcbiAgfSxcbiAgTXVsdGlMaW5lU3RyaW5nOiBmdW5jdGlvbihvYmplY3QsIHN0cmVhbSkge1xuICAgIHZhciBjb29yZGluYXRlcyA9IG9iamVjdC5jb29yZGluYXRlcywgaSA9IC0xLCBuID0gY29vcmRpbmF0ZXMubGVuZ3RoO1xuICAgIHdoaWxlICgrK2kgPCBuKSBzdHJlYW1MaW5lKGNvb3JkaW5hdGVzW2ldLCBzdHJlYW0sIDApO1xuICB9LFxuICBQb2x5Z29uOiBmdW5jdGlvbihvYmplY3QsIHN0cmVhbSkge1xuICAgIHN0cmVhbVBvbHlnb24ob2JqZWN0LmNvb3JkaW5hdGVzLCBzdHJlYW0pO1xuICB9LFxuICBNdWx0aVBvbHlnb246IGZ1bmN0aW9uKG9iamVjdCwgc3RyZWFtKSB7XG4gICAgdmFyIGNvb3JkaW5hdGVzID0gb2JqZWN0LmNvb3JkaW5hdGVzLCBpID0gLTEsIG4gPSBjb29yZGluYXRlcy5sZW5ndGg7XG4gICAgd2hpbGUgKCsraSA8IG4pIHN0cmVhbVBvbHlnb24oY29vcmRpbmF0ZXNbaV0sIHN0cmVhbSk7XG4gIH0sXG4gIEdlb21ldHJ5Q29sbGVjdGlvbjogZnVuY3Rpb24ob2JqZWN0LCBzdHJlYW0pIHtcbiAgICB2YXIgZ2VvbWV0cmllcyA9IG9iamVjdC5nZW9tZXRyaWVzLCBpID0gLTEsIG4gPSBnZW9tZXRyaWVzLmxlbmd0aDtcbiAgICB3aGlsZSAoKytpIDwgbikgc3RyZWFtR2VvbWV0cnkoZ2VvbWV0cmllc1tpXSwgc3RyZWFtKTtcbiAgfVxufTtcblxuZnVuY3Rpb24gc3RyZWFtTGluZShjb29yZGluYXRlcywgc3RyZWFtLCBjbG9zZWQpIHtcbiAgdmFyIGkgPSAtMSwgbiA9IGNvb3JkaW5hdGVzLmxlbmd0aCAtIGNsb3NlZCwgY29vcmRpbmF0ZTtcbiAgc3RyZWFtLmxpbmVTdGFydCgpO1xuICB3aGlsZSAoKytpIDwgbikgY29vcmRpbmF0ZSA9IGNvb3JkaW5hdGVzW2ldLCBzdHJlYW0ucG9pbnQoY29vcmRpbmF0ZVswXSwgY29vcmRpbmF0ZVsxXSwgY29vcmRpbmF0ZVsyXSk7XG4gIHN0cmVhbS5saW5lRW5kKCk7XG59XG5cbmZ1bmN0aW9uIHN0cmVhbVBvbHlnb24oY29vcmRpbmF0ZXMsIHN0cmVhbSkge1xuICB2YXIgaSA9IC0xLCBuID0gY29vcmRpbmF0ZXMubGVuZ3RoO1xuICBzdHJlYW0ucG9seWdvblN0YXJ0KCk7XG4gIHdoaWxlICgrK2kgPCBuKSBzdHJlYW1MaW5lKGNvb3JkaW5hdGVzW2ldLCBzdHJlYW0sIDEpO1xuICBzdHJlYW0ucG9seWdvbkVuZCgpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihvYmplY3QsIHN0cmVhbSkge1xuICBpZiAob2JqZWN0ICYmIHN0cmVhbU9iamVjdFR5cGUuaGFzT3duUHJvcGVydHkob2JqZWN0LnR5cGUpKSB7XG4gICAgc3RyZWFtT2JqZWN0VHlwZVtvYmplY3QudHlwZV0ob2JqZWN0LCBzdHJlYW0pO1xuICB9IGVsc2Uge1xuICAgIHN0cmVhbUdlb21ldHJ5KG9iamVjdCwgc3RyZWFtKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbInN0cmVhbUdlb21ldHJ5IiwiZ2VvbWV0cnkiLCJzdHJlYW0iLCJzdHJlYW1HZW9tZXRyeVR5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsInR5cGUiLCJzdHJlYW1PYmplY3RUeXBlIiwiRmVhdHVyZSIsIm9iamVjdCIsIkZlYXR1cmVDb2xsZWN0aW9uIiwiZmVhdHVyZXMiLCJpIiwibiIsImxlbmd0aCIsIlNwaGVyZSIsInNwaGVyZSIsIlBvaW50IiwiY29vcmRpbmF0ZXMiLCJwb2ludCIsIk11bHRpUG9pbnQiLCJMaW5lU3RyaW5nIiwic3RyZWFtTGluZSIsIk11bHRpTGluZVN0cmluZyIsIlBvbHlnb24iLCJzdHJlYW1Qb2x5Z29uIiwiTXVsdGlQb2x5Z29uIiwiR2VvbWV0cnlDb2xsZWN0aW9uIiwiZ2VvbWV0cmllcyIsImNsb3NlZCIsImNvb3JkaW5hdGUiLCJsaW5lU3RhcnQiLCJsaW5lRW5kIiwicG9seWdvblN0YXJ0IiwicG9seWdvbkVuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/stream.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-geo/src/transform.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-geo/src/transform.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   transformer: () => (/* binding */ transformer)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(methods) {\n    return {\n        stream: transformer(methods)\n    };\n}\nfunction transformer(methods) {\n    return function(stream) {\n        var s = new TransformStream;\n        for(var key in methods)s[key] = methods[key];\n        s.stream = stream;\n        return s;\n    };\n}\nfunction TransformStream() {}\nTransformStream.prototype = {\n    constructor: TransformStream,\n    point: function(x, y) {\n        this.stream.point(x, y);\n    },\n    sphere: function() {\n        this.stream.sphere();\n    },\n    lineStart: function() {\n        this.stream.lineStart();\n    },\n    lineEnd: function() {\n        this.stream.lineEnd();\n    },\n    polygonStart: function() {\n        this.stream.polygonStart();\n    },\n    polygonEnd: function() {\n        this.stream.polygonEnd();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-geo/src/transform.js\n");

/***/ })

};
;