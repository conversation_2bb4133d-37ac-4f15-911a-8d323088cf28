"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-array";
exports.ids = ["vendor-chunks/d3-array"];
exports.modules = {

/***/ "(ssr)/../../node_modules/d3-array/src/array.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/array.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\nvar slice = array.slice;\nvar map = array.map;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9hcnJheS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLElBQUlBLFFBQVFDLE1BQU1DLFNBQVM7QUFFcEIsSUFBSUMsUUFBUUgsTUFBTUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlDLE1BQU1KLE1BQU1JLEdBQUcsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvYXJyYXkuanM/NDE3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgYXJyYXkgPSBBcnJheS5wcm90b3R5cGU7XG5cbmV4cG9ydCB2YXIgc2xpY2UgPSBhcnJheS5zbGljZTtcbmV4cG9ydCB2YXIgbWFwID0gYXJyYXkubWFwO1xuIl0sIm5hbWVzIjpbImFycmF5IiwiQXJyYXkiLCJwcm90b3R5cGUiLCJzbGljZSIsIm1hcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/array.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/ascending.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-array/src/ascending.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ascending)\n/* harmony export */ });\nfunction ascending(a, b) {\n    return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9hc2NlbmRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLFVBQVVDLENBQUMsRUFBRUMsQ0FBQztJQUNwQyxPQUFPRCxLQUFLLFFBQVFDLEtBQUssT0FBT0MsTUFBTUYsSUFBSUMsSUFBSSxDQUFDLElBQUlELElBQUlDLElBQUksSUFBSUQsS0FBS0MsSUFBSSxJQUFJQztBQUM5RSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvYXNjZW5kaW5nLmpzPzdkYjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gYXNjZW5kaW5nKGEsIGIpIHtcbiAgcmV0dXJuIGEgPT0gbnVsbCB8fCBiID09IG51bGwgPyBOYU4gOiBhIDwgYiA/IC0xIDogYSA+IGIgPyAxIDogYSA+PSBiID8gMCA6IE5hTjtcbn1cbiJdLCJuYW1lcyI6WyJhc2NlbmRpbmciLCJhIiwiYiIsIk5hTiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/bin.js":
/*!**********************************************!*\
  !*** ../../node_modules/d3-array/src/bin.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bin)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./array.js */ \"(ssr)/../../node_modules/d3-array/src/array.js\");\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/../../node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/../../node_modules/d3-array/src/constant.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/../../node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/../../node_modules/d3-array/src/identity.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/../../node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/../../node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/../../node_modules/d3-array/src/threshold/sturges.js\");\n\n\n\n\n\n\n\n\nfunction bin() {\n    var value = _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], domain = _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], threshold = _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n    function histogram(data) {\n        if (!Array.isArray(data)) data = Array.from(data);\n        var i, n = data.length, x, step, values = new Array(n);\n        for(i = 0; i < n; ++i){\n            values[i] = value(data[i], i, data);\n        }\n        var xz = domain(values), x0 = xz[0], x1 = xz[1], tz = threshold(values, x0, x1);\n        // Convert number of thresholds into uniform thresholds, and nice the\n        // default domain accordingly.\n        if (!Array.isArray(tz)) {\n            const max = x1, tn = +tz;\n            if (domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) [x0, x1] = (0,_nice_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(x0, x1, tn);\n            tz = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(x0, x1, tn);\n            // If the domain is aligned with the first tick (which it will by\n            // default), then we can use quantization rather than bisection to bin\n            // values, which is substantially faster.\n            if (tz[0] <= x0) step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n            // If the last threshold is coincident with the domain’s upper bound, the\n            // last bin will be zero-width. If the default domain is used, and this\n            // last threshold is coincident with the maximum input value, we can\n            // extend the niced upper bound by one tick to ensure uniform bin widths;\n            // otherwise, we simply remove the last threshold. Note that we don’t\n            // coerce values or the domain to numbers, and thus must be careful to\n            // compare order (>=) rather than strict equality (===)!\n            if (tz[tz.length - 1] >= x1) {\n                if (max >= x1 && domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n                    const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n                    if (isFinite(step)) {\n                        if (step > 0) {\n                            x1 = (Math.floor(x1 / step) + 1) * step;\n                        } else if (step < 0) {\n                            x1 = (Math.ceil(x1 * -step) + 1) / -step;\n                        }\n                    }\n                } else {\n                    tz.pop();\n                }\n            }\n        }\n        // Remove any thresholds outside the domain.\n        // Be careful not to mutate an array owned by the user!\n        var m = tz.length, a = 0, b = m;\n        while(tz[a] <= x0)++a;\n        while(tz[b - 1] > x1)--b;\n        if (a || b < m) tz = tz.slice(a, b), m = b - a;\n        var bins = new Array(m + 1), bin;\n        // Initialize bins.\n        for(i = 0; i <= m; ++i){\n            bin = bins[i] = [];\n            bin.x0 = i > 0 ? tz[i - 1] : x0;\n            bin.x1 = i < m ? tz[i] : x1;\n        }\n        // Assign data to bins by value, ignoring any outside the domain.\n        if (isFinite(step)) {\n            if (step > 0) {\n                for(i = 0; i < n; ++i){\n                    if ((x = values[i]) != null && x0 <= x && x <= x1) {\n                        bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n                    }\n                }\n            } else if (step < 0) {\n                for(i = 0; i < n; ++i){\n                    if ((x = values[i]) != null && x0 <= x && x <= x1) {\n                        const j = Math.floor((x0 - x) * step);\n                        bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n                    }\n                }\n            }\n        } else {\n            for(i = 0; i < n; ++i){\n                if ((x = values[i]) != null && x0 <= x && x <= x1) {\n                    bins[(0,_bisect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(tz, x, 0, m)].push(data[i]);\n                }\n            }\n        }\n        return bins;\n    }\n    histogram.value = function(_) {\n        return arguments.length ? (value = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_), histogram) : value;\n    };\n    histogram.domain = function(_) {\n        return arguments.length ? (domain = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])([\n            _[0],\n            _[1]\n        ]), histogram) : domain;\n    };\n    histogram.thresholds = function(_) {\n        return arguments.length ? (threshold = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(Array.isArray(_) ? _array_js__WEBPACK_IMPORTED_MODULE_7__.slice.call(_) : _), histogram) : threshold;\n    };\n    return histogram;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/bin.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/bisect.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/bisect.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bisectCenter: () => (/* binding */ bisectCenter),\n/* harmony export */   bisectLeft: () => (/* binding */ bisectLeft),\n/* harmony export */   bisectRight: () => (/* binding */ bisectRight),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/../../node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./number.js */ \"(ssr)/../../node_modules/d3-array/src/number.js\");\n\n\n\nconst ascendingBisect = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nconst bisectRight = ascendingBisect.right;\nconst bisectLeft = ascendingBisect.left;\nconst bisectCenter = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_number_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).center;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bisectRight);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9iaXNlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1QztBQUNGO0FBQ0o7QUFFakMsTUFBTUcsa0JBQWtCRix3REFBUUEsQ0FBQ0QscURBQVNBO0FBQ25DLE1BQU1JLGNBQWNELGdCQUFnQkUsS0FBSyxDQUFDO0FBQzFDLE1BQU1DLGFBQWFILGdCQUFnQkksSUFBSSxDQUFDO0FBQ3hDLE1BQU1DLGVBQWVQLHdEQUFRQSxDQUFDQyxrREFBTUEsRUFBRU8sTUFBTSxDQUFDO0FBQ3BELGlFQUFlTCxXQUFXQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9iaXNlY3QuanM/YWU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IGJpc2VjdG9yIGZyb20gXCIuL2Jpc2VjdG9yLmpzXCI7XG5pbXBvcnQgbnVtYmVyIGZyb20gXCIuL251bWJlci5qc1wiO1xuXG5jb25zdCBhc2NlbmRpbmdCaXNlY3QgPSBiaXNlY3Rvcihhc2NlbmRpbmcpO1xuZXhwb3J0IGNvbnN0IGJpc2VjdFJpZ2h0ID0gYXNjZW5kaW5nQmlzZWN0LnJpZ2h0O1xuZXhwb3J0IGNvbnN0IGJpc2VjdExlZnQgPSBhc2NlbmRpbmdCaXNlY3QubGVmdDtcbmV4cG9ydCBjb25zdCBiaXNlY3RDZW50ZXIgPSBiaXNlY3RvcihudW1iZXIpLmNlbnRlcjtcbmV4cG9ydCBkZWZhdWx0IGJpc2VjdFJpZ2h0O1xuIl0sIm5hbWVzIjpbImFzY2VuZGluZyIsImJpc2VjdG9yIiwibnVtYmVyIiwiYXNjZW5kaW5nQmlzZWN0IiwiYmlzZWN0UmlnaHQiLCJyaWdodCIsImJpc2VjdExlZnQiLCJsZWZ0IiwiYmlzZWN0Q2VudGVyIiwiY2VudGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/bisect.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/bisector.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/bisector.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bisector)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/../../node_modules/d3-array/src/descending.js\");\n\n\nfunction bisector(f) {\n    let compare1, compare2, delta;\n    // If an accessor is specified, promote it to a comparator. In this case we\n    // can test whether the search value is (self-) comparable. We can’t do this\n    // for a comparator (except for specific, known comparators) because we can’t\n    // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n    // used to test whether a single value is comparable.\n    if (f.length !== 2) {\n        compare1 = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n        compare2 = (d, x)=>(0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(f(d), x);\n        delta = (d, x)=>f(d) - x;\n    } else {\n        compare1 = f === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || f === _descending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"] ? f : zero;\n        compare2 = f;\n        delta = f;\n    }\n    function left(a, x, lo = 0, hi = a.length) {\n        if (lo < hi) {\n            if (compare1(x, x) !== 0) return hi;\n            do {\n                const mid = lo + hi >>> 1;\n                if (compare2(a[mid], x) < 0) lo = mid + 1;\n                else hi = mid;\n            }while (lo < hi);\n        }\n        return lo;\n    }\n    function right(a, x, lo = 0, hi = a.length) {\n        if (lo < hi) {\n            if (compare1(x, x) !== 0) return hi;\n            do {\n                const mid = lo + hi >>> 1;\n                if (compare2(a[mid], x) <= 0) lo = mid + 1;\n                else hi = mid;\n            }while (lo < hi);\n        }\n        return lo;\n    }\n    function center(a, x, lo = 0, hi = a.length) {\n        const i = left(a, x, lo, hi - 1);\n        return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n    }\n    return {\n        left,\n        center,\n        right\n    };\n}\nfunction zero() {\n    return 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/bisector.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/blur.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/blur.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blur: () => (/* binding */ blur),\n/* harmony export */   blur2: () => (/* binding */ blur2),\n/* harmony export */   blurImage: () => (/* binding */ blurImage)\n/* harmony export */ });\nfunction blur(values, r) {\n    if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n    let length = values.length;\n    if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n    if (!length || !r) return values;\n    const blur = blurf(r);\n    const temp = values.slice();\n    blur(values, temp, 0, length, 1);\n    blur(temp, values, 0, length, 1);\n    blur(values, temp, 0, length, 1);\n    return values;\n}\nconst blur2 = Blur2(blurf);\nconst blurImage = Blur2(blurfImage);\nfunction Blur2(blur) {\n    return function(data, rx, ry = rx) {\n        if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n        if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n        let { data: values, width, height } = data;\n        if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n        if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n        if (!width || !height || !rx && !ry) return data;\n        const blurx = rx && blur(rx);\n        const blury = ry && blur(ry);\n        const temp = values.slice();\n        if (blurx && blury) {\n            blurh(blurx, temp, values, width, height);\n            blurh(blurx, values, temp, width, height);\n            blurh(blurx, temp, values, width, height);\n            blurv(blury, values, temp, width, height);\n            blurv(blury, temp, values, width, height);\n            blurv(blury, values, temp, width, height);\n        } else if (blurx) {\n            blurh(blurx, values, temp, width, height);\n            blurh(blurx, temp, values, width, height);\n            blurh(blurx, values, temp, width, height);\n        } else if (blury) {\n            blurv(blury, values, temp, width, height);\n            blurv(blury, temp, values, width, height);\n            blurv(blury, values, temp, width, height);\n        }\n        return data;\n    };\n}\nfunction blurh(blur, T, S, w, h) {\n    for(let y = 0, n = w * h; y < n;){\n        blur(T, S, y, y += w, 1);\n    }\n}\nfunction blurv(blur, T, S, w, h) {\n    for(let x = 0, n = w * h; x < w; ++x){\n        blur(T, S, x, x + n, w);\n    }\n}\nfunction blurfImage(radius) {\n    const blur = blurf(radius);\n    return (T, S, start, stop, step)=>{\n        start <<= 2, stop <<= 2, step <<= 2;\n        blur(T, S, start + 0, stop + 0, step);\n        blur(T, S, start + 1, stop + 1, step);\n        blur(T, S, start + 2, stop + 2, step);\n        blur(T, S, start + 3, stop + 3, step);\n    };\n}\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n    const radius0 = Math.floor(radius);\n    if (radius0 === radius) return bluri(radius);\n    const t = radius - radius0;\n    const w = 2 * radius + 1;\n    return (T, S, start, stop, step)=>{\n        if (!((stop -= step) >= start)) return; // inclusive stop\n        let sum = radius0 * S[start];\n        const s0 = step * radius0;\n        const s1 = s0 + step;\n        for(let i = start, j = start + s0; i < j; i += step){\n            sum += S[Math.min(stop, i)];\n        }\n        for(let i = start, j = stop; i <= j; i += step){\n            sum += S[Math.min(stop, i + s0)];\n            T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n            sum -= S[Math.max(start, i - s0)];\n        }\n    };\n}\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n    const w = 2 * radius + 1;\n    return (T, S, start, stop, step)=>{\n        if (!((stop -= step) >= start)) return; // inclusive stop\n        let sum = radius * S[start];\n        const s = step * radius;\n        for(let i = start, j = start + s; i < j; i += step){\n            sum += S[Math.min(stop, i)];\n        }\n        for(let i = start, j = stop; i <= j; i += step){\n            sum += S[Math.min(stop, i + s)];\n            T[i] = sum / w;\n            sum -= S[Math.max(start, i - s)];\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/blur.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/constant.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/constant.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ constant)\n/* harmony export */ });\nfunction constant(x) {\n    return ()=>x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9jb25zdGFudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsU0FBU0MsQ0FBQztJQUNoQyxPQUFPLElBQU1BO0FBQ2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvbnN0YW50LmpzPzNhNzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY29uc3RhbnQoeCkge1xuICByZXR1cm4gKCkgPT4geDtcbn1cbiJdLCJuYW1lcyI6WyJjb25zdGFudCIsIngiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/constant.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/count.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/count.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ count)\n/* harmony export */ });\nfunction count(values, valueof) {\n    let count = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                ++count;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                ++count;\n            }\n        }\n    }\n    return count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9jb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsTUFBTUMsTUFBTSxFQUFFQyxPQUFPO0lBQzNDLElBQUlGLFFBQVE7SUFDWixJQUFJRSxZQUFZQyxXQUFXO1FBQ3pCLEtBQUssSUFBSUMsU0FBU0gsT0FBUTtZQUN4QixJQUFJRyxTQUFTLFFBQVEsQ0FBQ0EsUUFBUSxDQUFDQSxLQUFJLEtBQU1BLE9BQU87Z0JBQzlDLEVBQUVKO1lBQ0o7UUFDRjtJQUNGLE9BQU87UUFDTCxJQUFJSyxRQUFRLENBQUM7UUFDYixLQUFLLElBQUlELFNBQVNILE9BQVE7WUFDeEIsSUFBSSxDQUFDRyxRQUFRRixRQUFRRSxPQUFPLEVBQUVDLE9BQU9KLE9BQU0sS0FBTSxRQUFRLENBQUNHLFFBQVEsQ0FBQ0EsS0FBSSxLQUFNQSxPQUFPO2dCQUNsRixFQUFFSjtZQUNKO1FBQ0Y7SUFDRjtJQUNBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvdW50LmpzPzQ5NDAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY291bnQodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBjb3VudCA9IDA7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgKytjb3VudDtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQ7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBjb3VudDtcbn1cbiJdLCJuYW1lcyI6WyJjb3VudCIsInZhbHVlcyIsInZhbHVlb2YiLCJ1bmRlZmluZWQiLCJ2YWx1ZSIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/count.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/cross.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/cross.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cross)\n/* harmony export */ });\nfunction length(array) {\n    return array.length | 0;\n}\nfunction empty(length) {\n    return !(length > 0);\n}\nfunction arrayify(values) {\n    return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\nfunction reducer(reduce) {\n    return (values)=>reduce(...values);\n}\nfunction cross(...values) {\n    const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n    values = values.map(arrayify);\n    const lengths = values.map(length);\n    const j = values.length - 1;\n    const index = new Array(j + 1).fill(0);\n    const product = [];\n    if (j < 0 || lengths.some(empty)) return product;\n    while(true){\n        product.push(index.map((j, i)=>values[i][j]));\n        let i = j;\n        while(++index[i] === lengths[i]){\n            if (i === 0) return reduce ? product.map(reduce) : product;\n            index[i--] = 0;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/cross.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/cumsum.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/cumsum.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cumsum)\n/* harmony export */ });\nfunction cumsum(values, valueof) {\n    var sum = 0, index = 0;\n    return Float64Array.from(values, valueof === undefined ? (v)=>sum += +v || 0 : (v)=>sum += +valueof(v, index++, values) || 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9jdW1zdW0uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLE9BQU9DLE1BQU0sRUFBRUMsT0FBTztJQUM1QyxJQUFJQyxNQUFNLEdBQUdDLFFBQVE7SUFDckIsT0FBT0MsYUFBYUMsSUFBSSxDQUFDTCxRQUFRQyxZQUFZSyxZQUN6Q0MsQ0FBQUEsSUFBTUwsT0FBTyxDQUFDSyxLQUFLLElBQ25CQSxDQUFBQSxJQUFNTCxPQUFPLENBQUNELFFBQVFNLEdBQUdKLFNBQVNILFdBQVc7QUFDbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2N1bXN1bS5qcz8zZWI3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGN1bXN1bSh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgdmFyIHN1bSA9IDAsIGluZGV4ID0gMDtcbiAgcmV0dXJuIEZsb2F0NjRBcnJheS5mcm9tKHZhbHVlcywgdmFsdWVvZiA9PT0gdW5kZWZpbmVkXG4gICAgPyB2ID0+IChzdW0gKz0gK3YgfHwgMClcbiAgICA6IHYgPT4gKHN1bSArPSArdmFsdWVvZih2LCBpbmRleCsrLCB2YWx1ZXMpIHx8IDApKTtcbn0iXSwibmFtZXMiOlsiY3Vtc3VtIiwidmFsdWVzIiwidmFsdWVvZiIsInN1bSIsImluZGV4IiwiRmxvYXQ2NEFycmF5IiwiZnJvbSIsInVuZGVmaW5lZCIsInYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/cumsum.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/descending.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-array/src/descending.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ descending)\n/* harmony export */ });\nfunction descending(a, b) {\n    return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kZXNjZW5kaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxXQUFXQyxDQUFDLEVBQUVDLENBQUM7SUFDckMsT0FBT0QsS0FBSyxRQUFRQyxLQUFLLE9BQU9DLE1BQzVCRCxJQUFJRCxJQUFJLENBQUMsSUFDVEMsSUFBSUQsSUFBSSxJQUNSQyxLQUFLRCxJQUFJLElBQ1RFO0FBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rlc2NlbmRpbmcuanM/ZTE2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBkZXNjZW5kaW5nKGEsIGIpIHtcbiAgcmV0dXJuIGEgPT0gbnVsbCB8fCBiID09IG51bGwgPyBOYU5cbiAgICA6IGIgPCBhID8gLTFcbiAgICA6IGIgPiBhID8gMVxuICAgIDogYiA+PSBhID8gMFxuICAgIDogTmFOO1xufVxuIl0sIm5hbWVzIjpbImRlc2NlbmRpbmciLCJhIiwiYiIsIk5hTiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/descending.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/deviation.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-array/src/deviation.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ deviation)\n/* harmony export */ });\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/../../node_modules/d3-array/src/variance.js\");\n\nfunction deviation(values, valueof) {\n    const v = (0,_variance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, valueof);\n    return v ? Math.sqrt(v) : v;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kZXZpYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFdEIsU0FBU0MsVUFBVUMsTUFBTSxFQUFFQyxPQUFPO0lBQy9DLE1BQU1DLElBQUlKLHdEQUFRQSxDQUFDRSxRQUFRQztJQUMzQixPQUFPQyxJQUFJQyxLQUFLQyxJQUFJLENBQUNGLEtBQUtBO0FBQzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kZXZpYXRpb24uanM/OTIxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdmFyaWFuY2UgZnJvbSBcIi4vdmFyaWFuY2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGV2aWF0aW9uKHZhbHVlcywgdmFsdWVvZikge1xuICBjb25zdCB2ID0gdmFyaWFuY2UodmFsdWVzLCB2YWx1ZW9mKTtcbiAgcmV0dXJuIHYgPyBNYXRoLnNxcnQodikgOiB2O1xufVxuIl0sIm5hbWVzIjpbInZhcmlhbmNlIiwiZGV2aWF0aW9uIiwidmFsdWVzIiwidmFsdWVvZiIsInYiLCJNYXRoIiwic3FydCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/deviation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/difference.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-array/src/difference.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ difference)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/../../node_modules/internmap/src/index.js\");\n\nfunction difference(values, ...others) {\n    values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n    for (const other of others){\n        for (const value of other){\n            values.delete(value);\n        }\n    }\n    return values;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kaWZmZXJlbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBRXJCLFNBQVNDLFdBQVdDLE1BQU0sRUFBRSxHQUFHQyxNQUFNO0lBQ2xERCxTQUFTLElBQUlGLGdEQUFTQSxDQUFDRTtJQUN2QixLQUFLLE1BQU1FLFNBQVNELE9BQVE7UUFDMUIsS0FBSyxNQUFNRSxTQUFTRCxNQUFPO1lBQ3pCRixPQUFPSSxNQUFNLENBQUNEO1FBQ2hCO0lBQ0Y7SUFDQSxPQUFPSDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kaWZmZXJlbmNlLmpzPzFjMGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtJbnRlcm5TZXR9IGZyb20gXCJpbnRlcm5tYXBcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGlmZmVyZW5jZSh2YWx1ZXMsIC4uLm90aGVycykge1xuICB2YWx1ZXMgPSBuZXcgSW50ZXJuU2V0KHZhbHVlcyk7XG4gIGZvciAoY29uc3Qgb3RoZXIgb2Ygb3RoZXJzKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiBvdGhlcikge1xuICAgICAgdmFsdWVzLmRlbGV0ZSh2YWx1ZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiB2YWx1ZXM7XG59XG4iXSwibmFtZXMiOlsiSW50ZXJuU2V0IiwiZGlmZmVyZW5jZSIsInZhbHVlcyIsIm90aGVycyIsIm90aGVyIiwidmFsdWUiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/difference.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/disjoint.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/disjoint.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ disjoint)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/../../node_modules/internmap/src/index.js\");\n\nfunction disjoint(values, other) {\n    const iterator = other[Symbol.iterator](), set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n    for (const v of values){\n        if (set.has(v)) return false;\n        let value, done;\n        while({ value, done } = iterator.next()){\n            if (done) break;\n            if (Object.is(v, value)) return false;\n            set.add(value);\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kaXNqb2ludC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQztBQUVyQixTQUFTQyxTQUFTQyxNQUFNLEVBQUVDLEtBQUs7SUFDNUMsTUFBTUMsV0FBV0QsS0FBSyxDQUFDRSxPQUFPRCxRQUFRLENBQUMsSUFBSUUsTUFBTSxJQUFJTixnREFBU0E7SUFDOUQsS0FBSyxNQUFNTyxLQUFLTCxPQUFRO1FBQ3RCLElBQUlJLElBQUlFLEdBQUcsQ0FBQ0QsSUFBSSxPQUFPO1FBQ3ZCLElBQUlFLE9BQU9DO1FBQ1gsTUFBUSxFQUFDRCxLQUFLLEVBQUVDLElBQUksRUFBQyxHQUFHTixTQUFTTyxJQUFJLEdBQUs7WUFDeEMsSUFBSUQsTUFBTTtZQUNWLElBQUlFLE9BQU9DLEVBQUUsQ0FBQ04sR0FBR0UsUUFBUSxPQUFPO1lBQ2hDSCxJQUFJUSxHQUFHLENBQUNMO1FBQ1Y7SUFDRjtJQUNBLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvZGlzam9pbnQuanM/MWYyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0ludGVyblNldH0gZnJvbSBcImludGVybm1hcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBkaXNqb2ludCh2YWx1ZXMsIG90aGVyKSB7XG4gIGNvbnN0IGl0ZXJhdG9yID0gb3RoZXJbU3ltYm9sLml0ZXJhdG9yXSgpLCBzZXQgPSBuZXcgSW50ZXJuU2V0KCk7XG4gIGZvciAoY29uc3QgdiBvZiB2YWx1ZXMpIHtcbiAgICBpZiAoc2V0Lmhhcyh2KSkgcmV0dXJuIGZhbHNlO1xuICAgIGxldCB2YWx1ZSwgZG9uZTtcbiAgICB3aGlsZSAoKHt2YWx1ZSwgZG9uZX0gPSBpdGVyYXRvci5uZXh0KCkpKSB7XG4gICAgICBpZiAoZG9uZSkgYnJlYWs7XG4gICAgICBpZiAoT2JqZWN0LmlzKHYsIHZhbHVlKSkgcmV0dXJuIGZhbHNlO1xuICAgICAgc2V0LmFkZCh2YWx1ZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiB0cnVlO1xufVxuIl0sIm5hbWVzIjpbIkludGVyblNldCIsImRpc2pvaW50IiwidmFsdWVzIiwib3RoZXIiLCJpdGVyYXRvciIsIlN5bWJvbCIsInNldCIsInYiLCJoYXMiLCJ2YWx1ZSIsImRvbmUiLCJuZXh0IiwiT2JqZWN0IiwiaXMiLCJhZGQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/disjoint.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/every.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/every.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ every)\n/* harmony export */ });\nfunction every(values, test) {\n    if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n    let index = -1;\n    for (const value of values){\n        if (!test(value, ++index, values)) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ldmVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsTUFBTUMsTUFBTSxFQUFFQyxJQUFJO0lBQ3hDLElBQUksT0FBT0EsU0FBUyxZQUFZLE1BQU0sSUFBSUMsVUFBVTtJQUNwRCxJQUFJQyxRQUFRLENBQUM7SUFDYixLQUFLLE1BQU1DLFNBQVNKLE9BQVE7UUFDMUIsSUFBSSxDQUFDQyxLQUFLRyxPQUFPLEVBQUVELE9BQU9ILFNBQVM7WUFDakMsT0FBTztRQUNUO0lBQ0Y7SUFDQSxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V2ZXJ5LmpzP2U5MmMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZXZlcnkodmFsdWVzLCB0ZXN0KSB7XG4gIGlmICh0eXBlb2YgdGVzdCAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwidGVzdCBpcyBub3QgYSBmdW5jdGlvblwiKTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgaWYgKCF0ZXN0KHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG4gIHJldHVybiB0cnVlO1xufVxuIl0sIm5hbWVzIjpbImV2ZXJ5IiwidmFsdWVzIiwidGVzdCIsIlR5cGVFcnJvciIsImluZGV4IiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/every.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/extent.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/extent.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ extent)\n/* harmony export */ });\nfunction extent(values, valueof) {\n    let min;\n    let max;\n    if (valueof === undefined) {\n        for (const value of values){\n            if (value != null) {\n                if (min === undefined) {\n                    if (value >= value) min = max = value;\n                } else {\n                    if (min > value) min = value;\n                    if (max < value) max = value;\n                }\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null) {\n                if (min === undefined) {\n                    if (value >= value) min = max = value;\n                } else {\n                    if (min > value) min = value;\n                    if (max < value) max = value;\n                }\n            }\n        }\n    }\n    return [\n        min,\n        max\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9leHRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLE9BQU9DLE1BQU0sRUFBRUMsT0FBTztJQUM1QyxJQUFJQztJQUNKLElBQUlDO0lBQ0osSUFBSUYsWUFBWUcsV0FBVztRQUN6QixLQUFLLE1BQU1DLFNBQVNMLE9BQVE7WUFDMUIsSUFBSUssU0FBUyxNQUFNO2dCQUNqQixJQUFJSCxRQUFRRSxXQUFXO29CQUNyQixJQUFJQyxTQUFTQSxPQUFPSCxNQUFNQyxNQUFNRTtnQkFDbEMsT0FBTztvQkFDTCxJQUFJSCxNQUFNRyxPQUFPSCxNQUFNRztvQkFDdkIsSUFBSUYsTUFBTUUsT0FBT0YsTUFBTUU7Z0JBQ3pCO1lBQ0Y7UUFDRjtJQUNGLE9BQU87UUFDTCxJQUFJQyxRQUFRLENBQUM7UUFDYixLQUFLLElBQUlELFNBQVNMLE9BQVE7WUFDeEIsSUFBSSxDQUFDSyxRQUFRSixRQUFRSSxPQUFPLEVBQUVDLE9BQU9OLE9BQU0sS0FBTSxNQUFNO2dCQUNyRCxJQUFJRSxRQUFRRSxXQUFXO29CQUNyQixJQUFJQyxTQUFTQSxPQUFPSCxNQUFNQyxNQUFNRTtnQkFDbEMsT0FBTztvQkFDTCxJQUFJSCxNQUFNRyxPQUFPSCxNQUFNRztvQkFDdkIsSUFBSUYsTUFBTUUsT0FBT0YsTUFBTUU7Z0JBQ3pCO1lBQ0Y7UUFDRjtJQUNGO0lBQ0EsT0FBTztRQUFDSDtRQUFLQztLQUFJO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9leHRlbnQuanM/OGRiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBleHRlbnQodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBtaW47XG4gIGxldCBtYXg7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGwpIHtcbiAgICAgICAgaWYgKG1pbiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgaWYgKHZhbHVlID49IHZhbHVlKSBtaW4gPSBtYXggPSB2YWx1ZTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpZiAobWluID4gdmFsdWUpIG1pbiA9IHZhbHVlO1xuICAgICAgICAgIGlmIChtYXggPCB2YWx1ZSkgbWF4ID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbCkge1xuICAgICAgICBpZiAobWluID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICBpZiAodmFsdWUgPj0gdmFsdWUpIG1pbiA9IG1heCA9IHZhbHVlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGlmIChtaW4gPiB2YWx1ZSkgbWluID0gdmFsdWU7XG4gICAgICAgICAgaWYgKG1heCA8IHZhbHVlKSBtYXggPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gW21pbiwgbWF4XTtcbn1cbiJdLCJuYW1lcyI6WyJleHRlbnQiLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwibWluIiwibWF4IiwidW5kZWZpbmVkIiwidmFsdWUiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/extent.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/filter.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/filter.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ filter)\n/* harmony export */ });\nfunction filter(values, test) {\n    if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n    const array = [];\n    let index = -1;\n    for (const value of values){\n        if (test(value, ++index, values)) {\n            array.push(value);\n        }\n    }\n    return array;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9maWx0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLE9BQU9DLE1BQU0sRUFBRUMsSUFBSTtJQUN6QyxJQUFJLE9BQU9BLFNBQVMsWUFBWSxNQUFNLElBQUlDLFVBQVU7SUFDcEQsTUFBTUMsUUFBUSxFQUFFO0lBQ2hCLElBQUlDLFFBQVEsQ0FBQztJQUNiLEtBQUssTUFBTUMsU0FBU0wsT0FBUTtRQUMxQixJQUFJQyxLQUFLSSxPQUFPLEVBQUVELE9BQU9KLFNBQVM7WUFDaENHLE1BQU1HLElBQUksQ0FBQ0Q7UUFDYjtJQUNGO0lBQ0EsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvZmlsdGVyLmpzPzc4NTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZmlsdGVyKHZhbHVlcywgdGVzdCkge1xuICBpZiAodHlwZW9mIHRlc3QgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInRlc3QgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIGNvbnN0IGFycmF5ID0gW107XG4gIGxldCBpbmRleCA9IC0xO1xuICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGlmICh0ZXN0KHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSB7XG4gICAgICBhcnJheS5wdXNoKHZhbHVlKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGFycmF5O1xufVxuIl0sIm5hbWVzIjpbImZpbHRlciIsInZhbHVlcyIsInRlc3QiLCJUeXBlRXJyb3IiLCJhcnJheSIsImluZGV4IiwidmFsdWUiLCJwdXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/filter.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/fsum.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/fsum.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* binding */ Adder),\n/* harmony export */   fcumsum: () => (/* binding */ fcumsum),\n/* harmony export */   fsum: () => (/* binding */ fsum)\n/* harmony export */ });\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n    constructor(){\n        this._partials = new Float64Array(32);\n        this._n = 0;\n    }\n    add(x) {\n        const p = this._partials;\n        let i = 0;\n        for(let j = 0; j < this._n && j < 32; j++){\n            const y = p[j], hi = x + y, lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n            if (lo) p[i++] = lo;\n            x = hi;\n        }\n        p[i] = x;\n        this._n = i + 1;\n        return this;\n    }\n    valueOf() {\n        const p = this._partials;\n        let n = this._n, x, y, lo, hi = 0;\n        if (n > 0) {\n            hi = p[--n];\n            while(n > 0){\n                x = hi;\n                y = p[--n];\n                hi = x + y;\n                lo = y - (hi - x);\n                if (lo) break;\n            }\n            if (n > 0 && (lo < 0 && p[n - 1] < 0 || lo > 0 && p[n - 1] > 0)) {\n                y = lo * 2;\n                x = hi + y;\n                if (y == x - hi) hi = x;\n            }\n        }\n        return hi;\n    }\n}\nfunction fsum(values, valueof) {\n    const adder = new Adder();\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value = +value) {\n                adder.add(value);\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if (value = +valueof(value, ++index, values)) {\n                adder.add(value);\n            }\n        }\n    }\n    return +adder;\n}\nfunction fcumsum(values, valueof) {\n    const adder = new Adder();\n    let index = -1;\n    return Float64Array.from(values, valueof === undefined ? (v)=>adder.add(+v || 0) : (v)=>adder.add(+valueof(v, ++index, values) || 0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/fsum.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/greatest.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/greatest.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatest)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n\nfunction greatest(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    let max;\n    let defined = false;\n    if (compare.length === 1) {\n        let maxValue;\n        for (const element of values){\n            const value = compare(element);\n            if (defined ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, maxValue) > 0 : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n                max = element;\n                maxValue = value;\n                defined = true;\n            }\n        }\n    } else {\n        for (const value of values){\n            if (defined ? compare(value, max) > 0 : compare(value, value) === 0) {\n                max = value;\n                defined = true;\n            }\n        }\n    }\n    return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ncmVhdGVzdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUV4QixTQUFTQyxTQUFTQyxNQUFNLEVBQUVDLFVBQVVILHFEQUFTO0lBQzFELElBQUlJO0lBQ0osSUFBSUMsVUFBVTtJQUNkLElBQUlGLFFBQVFHLE1BQU0sS0FBSyxHQUFHO1FBQ3hCLElBQUlDO1FBQ0osS0FBSyxNQUFNQyxXQUFXTixPQUFRO1lBQzVCLE1BQU1PLFFBQVFOLFFBQVFLO1lBQ3RCLElBQUlILFVBQ0VMLHlEQUFTQSxDQUFDUyxPQUFPRixZQUFZLElBQzdCUCx5REFBU0EsQ0FBQ1MsT0FBT0EsV0FBVyxHQUFHO2dCQUNuQ0wsTUFBTUk7Z0JBQ05ELFdBQVdFO2dCQUNYSixVQUFVO1lBQ1o7UUFDRjtJQUNGLE9BQU87UUFDTCxLQUFLLE1BQU1JLFNBQVNQLE9BQVE7WUFDMUIsSUFBSUcsVUFDRUYsUUFBUU0sT0FBT0wsT0FBTyxJQUN0QkQsUUFBUU0sT0FBT0EsV0FBVyxHQUFHO2dCQUNqQ0wsTUFBTUs7Z0JBQ05KLFVBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ncmVhdGVzdC5qcz8yYjgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhc2NlbmRpbmcgZnJvbSBcIi4vYXNjZW5kaW5nLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdyZWF0ZXN0KHZhbHVlcywgY29tcGFyZSA9IGFzY2VuZGluZykge1xuICBsZXQgbWF4O1xuICBsZXQgZGVmaW5lZCA9IGZhbHNlO1xuICBpZiAoY29tcGFyZS5sZW5ndGggPT09IDEpIHtcbiAgICBsZXQgbWF4VmFsdWU7XG4gICAgZm9yIChjb25zdCBlbGVtZW50IG9mIHZhbHVlcykge1xuICAgICAgY29uc3QgdmFsdWUgPSBjb21wYXJlKGVsZW1lbnQpO1xuICAgICAgaWYgKGRlZmluZWRcbiAgICAgICAgICA/IGFzY2VuZGluZyh2YWx1ZSwgbWF4VmFsdWUpID4gMFxuICAgICAgICAgIDogYXNjZW5kaW5nKHZhbHVlLCB2YWx1ZSkgPT09IDApIHtcbiAgICAgICAgbWF4ID0gZWxlbWVudDtcbiAgICAgICAgbWF4VmFsdWUgPSB2YWx1ZTtcbiAgICAgICAgZGVmaW5lZCA9IHRydWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoZGVmaW5lZFxuICAgICAgICAgID8gY29tcGFyZSh2YWx1ZSwgbWF4KSA+IDBcbiAgICAgICAgICA6IGNvbXBhcmUodmFsdWUsIHZhbHVlKSA9PT0gMCkge1xuICAgICAgICBtYXggPSB2YWx1ZTtcbiAgICAgICAgZGVmaW5lZCA9IHRydWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBtYXg7XG59XG4iXSwibmFtZXMiOlsiYXNjZW5kaW5nIiwiZ3JlYXRlc3QiLCJ2YWx1ZXMiLCJjb21wYXJlIiwibWF4IiwiZGVmaW5lZCIsImxlbmd0aCIsIm1heFZhbHVlIiwiZWxlbWVudCIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/greatest.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/greatestIndex.js":
/*!********************************************************!*\
  !*** ../../node_modules/d3-array/src/greatestIndex.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatestIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/../../node_modules/d3-array/src/maxIndex.js\");\n\n\nfunction greatestIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (compare.length === 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n    let maxValue;\n    let max = -1;\n    let index = -1;\n    for (const value of values){\n        ++index;\n        if (max < 0 ? compare(value, value) === 0 : compare(value, maxValue) > 0) {\n            maxValue = value;\n            max = index;\n        }\n    }\n    return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ncmVhdGVzdEluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1QztBQUNGO0FBRXRCLFNBQVNFLGNBQWNDLE1BQU0sRUFBRUMsVUFBVUoscURBQVM7SUFDL0QsSUFBSUksUUFBUUMsTUFBTSxLQUFLLEdBQUcsT0FBT0osd0RBQVFBLENBQUNFLFFBQVFDO0lBQ2xELElBQUlFO0lBQ0osSUFBSUMsTUFBTSxDQUFDO0lBQ1gsSUFBSUMsUUFBUSxDQUFDO0lBQ2IsS0FBSyxNQUFNQyxTQUFTTixPQUFRO1FBQzFCLEVBQUVLO1FBQ0YsSUFBSUQsTUFBTSxJQUNKSCxRQUFRSyxPQUFPQSxXQUFXLElBQzFCTCxRQUFRSyxPQUFPSCxZQUFZLEdBQUc7WUFDbENBLFdBQVdHO1lBQ1hGLE1BQU1DO1FBQ1I7SUFDRjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0SW5kZXguanM/YTBmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IG1heEluZGV4IGZyb20gXCIuL21heEluZGV4LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdyZWF0ZXN0SW5kZXgodmFsdWVzLCBjb21wYXJlID0gYXNjZW5kaW5nKSB7XG4gIGlmIChjb21wYXJlLmxlbmd0aCA9PT0gMSkgcmV0dXJuIG1heEluZGV4KHZhbHVlcywgY29tcGFyZSk7XG4gIGxldCBtYXhWYWx1ZTtcbiAgbGV0IG1heCA9IC0xO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICArK2luZGV4O1xuICAgIGlmIChtYXggPCAwXG4gICAgICAgID8gY29tcGFyZSh2YWx1ZSwgdmFsdWUpID09PSAwXG4gICAgICAgIDogY29tcGFyZSh2YWx1ZSwgbWF4VmFsdWUpID4gMCkge1xuICAgICAgbWF4VmFsdWUgPSB2YWx1ZTtcbiAgICAgIG1heCA9IGluZGV4O1xuICAgIH1cbiAgfVxuICByZXR1cm4gbWF4O1xufVxuIl0sIm5hbWVzIjpbImFzY2VuZGluZyIsIm1heEluZGV4IiwiZ3JlYXRlc3RJbmRleCIsInZhbHVlcyIsImNvbXBhcmUiLCJsZW5ndGgiLCJtYXhWYWx1ZSIsIm1heCIsImluZGV4IiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/greatestIndex.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/group.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/group.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ group),\n/* harmony export */   flatGroup: () => (/* binding */ flatGroup),\n/* harmony export */   flatRollup: () => (/* binding */ flatRollup),\n/* harmony export */   groups: () => (/* binding */ groups),\n/* harmony export */   index: () => (/* binding */ index),\n/* harmony export */   indexes: () => (/* binding */ indexes),\n/* harmony export */   rollup: () => (/* binding */ rollup),\n/* harmony export */   rollups: () => (/* binding */ rollups)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! internmap */ \"(ssr)/../../node_modules/internmap/src/index.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/../../node_modules/d3-array/src/identity.js\");\n\n\nfunction group(values, ...keys) {\n    return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\nfunction groups(values, ...keys) {\n    return nest(values, Array.from, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\nfunction flatten(groups, keys) {\n    for(let i = 1, n = keys.length; i < n; ++i){\n        groups = groups.flatMap((g)=>g.pop().map(([key, value])=>[\n                    ...g,\n                    key,\n                    value\n                ]));\n    }\n    return groups;\n}\nfunction flatGroup(values, ...keys) {\n    return flatten(groups(values, ...keys), keys);\n}\nfunction flatRollup(values, reduce, ...keys) {\n    return flatten(rollups(values, reduce, ...keys), keys);\n}\nfunction rollup(values, reduce, ...keys) {\n    return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], reduce, keys);\n}\nfunction rollups(values, reduce, ...keys) {\n    return nest(values, Array.from, reduce, keys);\n}\nfunction index(values, ...keys) {\n    return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], unique, keys);\n}\nfunction indexes(values, ...keys) {\n    return nest(values, Array.from, unique, keys);\n}\nfunction unique(values) {\n    if (values.length !== 1) throw new Error(\"duplicate key\");\n    return values[0];\n}\nfunction nest(values, map, reduce, keys) {\n    return function regroup(values, i) {\n        if (i >= keys.length) return reduce(values);\n        const groups = new internmap__WEBPACK_IMPORTED_MODULE_1__.InternMap();\n        const keyof = keys[i++];\n        let index = -1;\n        for (const value of values){\n            const key = keyof(value, ++index, values);\n            const group = groups.get(key);\n            if (group) group.push(value);\n            else groups.set(key, [\n                value\n            ]);\n        }\n        for (const [key, values] of groups){\n            groups.set(key, regroup(values, i));\n        }\n        return map(groups);\n    }(values, 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/group.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/groupSort.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-array/src/groupSort.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ groupSort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./group.js */ \"(ssr)/../../node_modules/d3-array/src/group.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/../../node_modules/d3-array/src/sort.js\");\n\n\n\nfunction groupSort(values, reduce, key) {\n    return (reduce.length !== 2 ? (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__.rollup)(values, reduce, key), ([ak, av], [bk, bv])=>(0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk)) : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, key), ([ak, av], [bk, bv])=>reduce(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk))).map(([key])=>key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9ncm91cFNvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1QztBQUNFO0FBQ1o7QUFFZCxTQUFTSSxVQUFVQyxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsR0FBRztJQUNuRCxPQUFPLENBQUNELE9BQU9FLE1BQU0sS0FBSyxJQUN0Qkwsb0RBQUlBLENBQUNELGlEQUFNQSxDQUFDRyxRQUFRQyxRQUFRQyxNQUFPLENBQUMsQ0FBQ0UsSUFBSUMsR0FBRyxFQUFFLENBQUNDLElBQUlDLEdBQUcsR0FBS1oseURBQVNBLENBQUNVLElBQUlFLE9BQU9aLHlEQUFTQSxDQUFDUyxJQUFJRSxPQUM5RlIsb0RBQUlBLENBQUNGLHFEQUFLQSxDQUFDSSxRQUFRRSxNQUFPLENBQUMsQ0FBQ0UsSUFBSUMsR0FBRyxFQUFFLENBQUNDLElBQUlDLEdBQUcsR0FBS04sT0FBT0ksSUFBSUUsT0FBT1oseURBQVNBLENBQUNTLElBQUlFLElBQUksRUFDdkZFLEdBQUcsQ0FBQyxDQUFDLENBQUNOLElBQUksR0FBS0E7QUFDcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyb3VwU29ydC5qcz9mMTg3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhc2NlbmRpbmcgZnJvbSBcIi4vYXNjZW5kaW5nLmpzXCI7XG5pbXBvcnQgZ3JvdXAsIHtyb2xsdXB9IGZyb20gXCIuL2dyb3VwLmpzXCI7XG5pbXBvcnQgc29ydCBmcm9tIFwiLi9zb3J0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdyb3VwU29ydCh2YWx1ZXMsIHJlZHVjZSwga2V5KSB7XG4gIHJldHVybiAocmVkdWNlLmxlbmd0aCAhPT0gMlxuICAgID8gc29ydChyb2xsdXAodmFsdWVzLCByZWR1Y2UsIGtleSksICgoW2FrLCBhdl0sIFtiaywgYnZdKSA9PiBhc2NlbmRpbmcoYXYsIGJ2KSB8fCBhc2NlbmRpbmcoYWssIGJrKSkpXG4gICAgOiBzb3J0KGdyb3VwKHZhbHVlcywga2V5KSwgKChbYWssIGF2XSwgW2JrLCBidl0pID0+IHJlZHVjZShhdiwgYnYpIHx8IGFzY2VuZGluZyhhaywgYmspKSkpXG4gICAgLm1hcCgoW2tleV0pID0+IGtleSk7XG59XG4iXSwibmFtZXMiOlsiYXNjZW5kaW5nIiwiZ3JvdXAiLCJyb2xsdXAiLCJzb3J0IiwiZ3JvdXBTb3J0IiwidmFsdWVzIiwicmVkdWNlIiwia2V5IiwibGVuZ3RoIiwiYWsiLCJhdiIsImJrIiwiYnYiLCJtYXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/groupSort.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/identity.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/identity.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ identity)\n/* harmony export */ });\nfunction identity(x) {\n    return x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9pZGVudGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsU0FBU0MsQ0FBQztJQUNoQyxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9pZGVudGl0eS5qcz8xNWE3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlkZW50aXR5KHgpIHtcbiAgcmV0dXJuIHg7XG59XG4iXSwibmFtZXMiOlsiaWRlbnRpdHkiLCJ4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/identity.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/index.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.Adder),\n/* harmony export */   InternMap: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternMap),\n/* harmony export */   InternSet: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternSet),\n/* harmony export */   ascending: () => (/* reexport safe */ _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   bin: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   bisect: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   bisectCenter: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectCenter),\n/* harmony export */   bisectLeft: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectLeft),\n/* harmony export */   bisectRight: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectRight),\n/* harmony export */   bisector: () => (/* reexport safe */ _bisector_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   blur: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur),\n/* harmony export */   blur2: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur2),\n/* harmony export */   blurImage: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blurImage),\n/* harmony export */   count: () => (/* reexport safe */ _count_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   cross: () => (/* reexport safe */ _cross_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   cumsum: () => (/* reexport safe */ _cumsum_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   descending: () => (/* reexport safe */ _descending_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   deviation: () => (/* reexport safe */ _deviation_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   difference: () => (/* reexport safe */ _difference_js__WEBPACK_IMPORTED_MODULE_50__[\"default\"]),\n/* harmony export */   disjoint: () => (/* reexport safe */ _disjoint_js__WEBPACK_IMPORTED_MODULE_51__[\"default\"]),\n/* harmony export */   every: () => (/* reexport safe */ _every_js__WEBPACK_IMPORTED_MODULE_43__[\"default\"]),\n/* harmony export */   extent: () => (/* reexport safe */ _extent_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   fcumsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fcumsum),\n/* harmony export */   filter: () => (/* reexport safe */ _filter_js__WEBPACK_IMPORTED_MODULE_45__[\"default\"]),\n/* harmony export */   flatGroup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatGroup),\n/* harmony export */   flatRollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatRollup),\n/* harmony export */   fsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fsum),\n/* harmony export */   greatest: () => (/* reexport safe */ _greatest_js__WEBPACK_IMPORTED_MODULE_34__[\"default\"]),\n/* harmony export */   greatestIndex: () => (/* reexport safe */ _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__[\"default\"]),\n/* harmony export */   group: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   groupSort: () => (/* reexport safe */ _groupSort_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   groups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.groups),\n/* harmony export */   histogram: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   index: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.index),\n/* harmony export */   indexes: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.indexes),\n/* harmony export */   intersection: () => (/* reexport safe */ _intersection_js__WEBPACK_IMPORTED_MODULE_52__[\"default\"]),\n/* harmony export */   least: () => (/* reexport safe */ _least_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"]),\n/* harmony export */   leastIndex: () => (/* reexport safe */ _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__[\"default\"]),\n/* harmony export */   map: () => (/* reexport safe */ _map_js__WEBPACK_IMPORTED_MODULE_46__[\"default\"]),\n/* harmony export */   max: () => (/* reexport safe */ _max_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   maxIndex: () => (/* reexport safe */ _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   mean: () => (/* reexport safe */ _mean_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   median: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   medianIndex: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__.medianIndex),\n/* harmony export */   merge: () => (/* reexport safe */ _merge_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   min: () => (/* reexport safe */ _min_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   minIndex: () => (/* reexport safe */ _minIndex_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   mode: () => (/* reexport safe */ _mode_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   nice: () => (/* reexport safe */ _nice_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   pairs: () => (/* reexport safe */ _pairs_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   permute: () => (/* reexport safe */ _permute_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   quantile: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   quantileIndex: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileIndex),\n/* harmony export */   quantileSorted: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileSorted),\n/* harmony export */   quickselect: () => (/* reexport safe */ _quickselect_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   range: () => (/* reexport safe */ _range_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   rank: () => (/* reexport safe */ _rank_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   reduce: () => (/* reexport safe */ _reduce_js__WEBPACK_IMPORTED_MODULE_47__[\"default\"]),\n/* harmony export */   reverse: () => (/* reexport safe */ _reverse_js__WEBPACK_IMPORTED_MODULE_48__[\"default\"]),\n/* harmony export */   rollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollup),\n/* harmony export */   rollups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollups),\n/* harmony export */   scan: () => (/* reexport safe */ _scan_js__WEBPACK_IMPORTED_MODULE_36__[\"default\"]),\n/* harmony export */   shuffle: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__[\"default\"]),\n/* harmony export */   shuffler: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__.shuffler),\n/* harmony export */   some: () => (/* reexport safe */ _some_js__WEBPACK_IMPORTED_MODULE_44__[\"default\"]),\n/* harmony export */   sort: () => (/* reexport safe */ _sort_js__WEBPACK_IMPORTED_MODULE_49__[\"default\"]),\n/* harmony export */   subset: () => (/* reexport safe */ _subset_js__WEBPACK_IMPORTED_MODULE_53__[\"default\"]),\n/* harmony export */   sum: () => (/* reexport safe */ _sum_js__WEBPACK_IMPORTED_MODULE_38__[\"default\"]),\n/* harmony export */   superset: () => (/* reexport safe */ _superset_js__WEBPACK_IMPORTED_MODULE_54__[\"default\"]),\n/* harmony export */   thresholdFreedmanDiaconis: () => (/* reexport safe */ _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   thresholdScott: () => (/* reexport safe */ _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   thresholdSturges: () => (/* reexport safe */ _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   tickIncrement: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickIncrement),\n/* harmony export */   tickStep: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickStep),\n/* harmony export */   ticks: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__[\"default\"]),\n/* harmony export */   transpose: () => (/* reexport safe */ _transpose_js__WEBPACK_IMPORTED_MODULE_40__[\"default\"]),\n/* harmony export */   union: () => (/* reexport safe */ _union_js__WEBPACK_IMPORTED_MODULE_55__[\"default\"]),\n/* harmony export */   variance: () => (/* reexport safe */ _variance_js__WEBPACK_IMPORTED_MODULE_41__[\"default\"]),\n/* harmony export */   zip: () => (/* reexport safe */ _zip_js__WEBPACK_IMPORTED_MODULE_42__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/../../node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/../../node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _blur_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blur.js */ \"(ssr)/../../node_modules/d3-array/src/blur.js\");\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./count.js */ \"(ssr)/../../node_modules/d3-array/src/count.js\");\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/../../node_modules/d3-array/src/cross.js\");\n/* harmony import */ var _cumsum_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./cumsum.js */ \"(ssr)/../../node_modules/d3-array/src/cumsum.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/../../node_modules/d3-array/src/descending.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./deviation.js */ \"(ssr)/../../node_modules/d3-array/src/deviation.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/../../node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _fsum_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./fsum.js */ \"(ssr)/../../node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./group.js */ \"(ssr)/../../node_modules/d3-array/src/group.js\");\n/* harmony import */ var _groupSort_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./groupSort.js */ \"(ssr)/../../node_modules/d3-array/src/groupSort.js\");\n/* harmony import */ var _bin_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./bin.js */ \"(ssr)/../../node_modules/d3-array/src/bin.js\");\n/* harmony import */ var _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./threshold/freedmanDiaconis.js */ \"(ssr)/../../node_modules/d3-array/src/threshold/freedmanDiaconis.js\");\n/* harmony import */ var _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./threshold/scott.js */ \"(ssr)/../../node_modules/d3-array/src/threshold/scott.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/../../node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./max.js */ \"(ssr)/../../node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/../../node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _mean_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./mean.js */ \"(ssr)/../../node_modules/d3-array/src/mean.js\");\n/* harmony import */ var _median_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./median.js */ \"(ssr)/../../node_modules/d3-array/src/median.js\");\n/* harmony import */ var _merge_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./merge.js */ \"(ssr)/../../node_modules/d3-array/src/merge.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./min.js */ \"(ssr)/../../node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/../../node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _mode_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./mode.js */ \"(ssr)/../../node_modules/d3-array/src/mode.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/../../node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _pairs_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./pairs.js */ \"(ssr)/../../node_modules/d3-array/src/pairs.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/../../node_modules/d3-array/src/permute.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/../../node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/../../node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _range_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./range.js */ \"(ssr)/../../node_modules/d3-array/src/range.js\");\n/* harmony import */ var _rank_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./rank.js */ \"(ssr)/../../node_modules/d3-array/src/rank.js\");\n/* harmony import */ var _least_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./least.js */ \"(ssr)/../../node_modules/d3-array/src/least.js\");\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/../../node_modules/d3-array/src/leastIndex.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/../../node_modules/d3-array/src/greatest.js\");\n/* harmony import */ var _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./greatestIndex.js */ \"(ssr)/../../node_modules/d3-array/src/greatestIndex.js\");\n/* harmony import */ var _scan_js__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./scan.js */ \"(ssr)/../../node_modules/d3-array/src/scan.js\");\n/* harmony import */ var _shuffle_js__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./shuffle.js */ \"(ssr)/../../node_modules/d3-array/src/shuffle.js\");\n/* harmony import */ var _sum_js__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ./sum.js */ \"(ssr)/../../node_modules/d3-array/src/sum.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/../../node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/../../node_modules/d3-array/src/transpose.js\");\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/../../node_modules/d3-array/src/variance.js\");\n/* harmony import */ var _zip_js__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ./zip.js */ \"(ssr)/../../node_modules/d3-array/src/zip.js\");\n/* harmony import */ var _every_js__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! ./every.js */ \"(ssr)/../../node_modules/d3-array/src/every.js\");\n/* harmony import */ var _some_js__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! ./some.js */ \"(ssr)/../../node_modules/d3-array/src/some.js\");\n/* harmony import */ var _filter_js__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! ./filter.js */ \"(ssr)/../../node_modules/d3-array/src/filter.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! ./map.js */ \"(ssr)/../../node_modules/d3-array/src/map.js\");\n/* harmony import */ var _reduce_js__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! ./reduce.js */ \"(ssr)/../../node_modules/d3-array/src/reduce.js\");\n/* harmony import */ var _reverse_js__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! ./reverse.js */ \"(ssr)/../../node_modules/d3-array/src/reverse.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/../../node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _difference_js__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! ./difference.js */ \"(ssr)/../../node_modules/d3-array/src/difference.js\");\n/* harmony import */ var _disjoint_js__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! ./disjoint.js */ \"(ssr)/../../node_modules/d3-array/src/disjoint.js\");\n/* harmony import */ var _intersection_js__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! ./intersection.js */ \"(ssr)/../../node_modules/d3-array/src/intersection.js\");\n/* harmony import */ var _subset_js__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! ./subset.js */ \"(ssr)/../../node_modules/d3-array/src/subset.js\");\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/../../node_modules/d3-array/src/superset.js\");\n/* harmony import */ var _union_js__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! ./union.js */ \"(ssr)/../../node_modules/d3-array/src/union.js\");\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! internmap */ \"(ssr)/../../node_modules/internmap/src/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use bin.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use leastIndex.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/intersection.js":
/*!*******************************************************!*\
  !*** ../../node_modules/d3-array/src/intersection.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ intersection)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/../../node_modules/internmap/src/index.js\");\n\nfunction intersection(values, ...others) {\n    values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n    others = others.map(set);\n    out: for (const value of values){\n        for (const other of others){\n            if (!other.has(value)) {\n                values.delete(value);\n                continue out;\n            }\n        }\n    }\n    return values;\n}\nfunction set(values) {\n    return values instanceof internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet ? values : new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9pbnRlcnNlY3Rpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFFckIsU0FBU0MsYUFBYUMsTUFBTSxFQUFFLEdBQUdDLE1BQU07SUFDcERELFNBQVMsSUFBSUYsZ0RBQVNBLENBQUNFO0lBQ3ZCQyxTQUFTQSxPQUFPQyxHQUFHLENBQUNDO0lBQ3BCQyxLQUFLLEtBQUssTUFBTUMsU0FBU0wsT0FBUTtRQUMvQixLQUFLLE1BQU1NLFNBQVNMLE9BQVE7WUFDMUIsSUFBSSxDQUFDSyxNQUFNQyxHQUFHLENBQUNGLFFBQVE7Z0JBQ3JCTCxPQUFPUSxNQUFNLENBQUNIO2dCQUNkLFNBQVNEO1lBQ1g7UUFDRjtJQUNGO0lBQ0EsT0FBT0o7QUFDVDtBQUVBLFNBQVNHLElBQUlILE1BQU07SUFDakIsT0FBT0Esa0JBQWtCRixnREFBU0EsR0FBR0UsU0FBUyxJQUFJRixnREFBU0EsQ0FBQ0U7QUFDOUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ludGVyc2VjdGlvbi5qcz8zMmVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7SW50ZXJuU2V0fSBmcm9tIFwiaW50ZXJubWFwXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGludGVyc2VjdGlvbih2YWx1ZXMsIC4uLm90aGVycykge1xuICB2YWx1ZXMgPSBuZXcgSW50ZXJuU2V0KHZhbHVlcyk7XG4gIG90aGVycyA9IG90aGVycy5tYXAoc2V0KTtcbiAgb3V0OiBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGZvciAoY29uc3Qgb3RoZXIgb2Ygb3RoZXJzKSB7XG4gICAgICBpZiAoIW90aGVyLmhhcyh2YWx1ZSkpIHtcbiAgICAgICAgdmFsdWVzLmRlbGV0ZSh2YWx1ZSk7XG4gICAgICAgIGNvbnRpbnVlIG91dDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlcztcbn1cblxuZnVuY3Rpb24gc2V0KHZhbHVlcykge1xuICByZXR1cm4gdmFsdWVzIGluc3RhbmNlb2YgSW50ZXJuU2V0ID8gdmFsdWVzIDogbmV3IEludGVyblNldCh2YWx1ZXMpO1xufVxuIl0sIm5hbWVzIjpbIkludGVyblNldCIsImludGVyc2VjdGlvbiIsInZhbHVlcyIsIm90aGVycyIsIm1hcCIsInNldCIsIm91dCIsInZhbHVlIiwib3RoZXIiLCJoYXMiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/intersection.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/least.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/least.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ least)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n\nfunction least(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    let min;\n    let defined = false;\n    if (compare.length === 1) {\n        let minValue;\n        for (const element of values){\n            const value = compare(element);\n            if (defined ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, minValue) < 0 : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n                min = element;\n                minValue = value;\n                defined = true;\n            }\n        }\n    } else {\n        for (const value of values){\n            if (defined ? compare(value, min) < 0 : compare(value, value) === 0) {\n                min = value;\n                defined = true;\n            }\n        }\n    }\n    return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9sZWFzdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUV4QixTQUFTQyxNQUFNQyxNQUFNLEVBQUVDLFVBQVVILHFEQUFTO0lBQ3ZELElBQUlJO0lBQ0osSUFBSUMsVUFBVTtJQUNkLElBQUlGLFFBQVFHLE1BQU0sS0FBSyxHQUFHO1FBQ3hCLElBQUlDO1FBQ0osS0FBSyxNQUFNQyxXQUFXTixPQUFRO1lBQzVCLE1BQU1PLFFBQVFOLFFBQVFLO1lBQ3RCLElBQUlILFVBQ0VMLHlEQUFTQSxDQUFDUyxPQUFPRixZQUFZLElBQzdCUCx5REFBU0EsQ0FBQ1MsT0FBT0EsV0FBVyxHQUFHO2dCQUNuQ0wsTUFBTUk7Z0JBQ05ELFdBQVdFO2dCQUNYSixVQUFVO1lBQ1o7UUFDRjtJQUNGLE9BQU87UUFDTCxLQUFLLE1BQU1JLFNBQVNQLE9BQVE7WUFDMUIsSUFBSUcsVUFDRUYsUUFBUU0sT0FBT0wsT0FBTyxJQUN0QkQsUUFBUU0sT0FBT0EsV0FBVyxHQUFHO2dCQUNqQ0wsTUFBTUs7Z0JBQ05KLFVBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9sZWFzdC5qcz82NjUzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhc2NlbmRpbmcgZnJvbSBcIi4vYXNjZW5kaW5nLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGxlYXN0KHZhbHVlcywgY29tcGFyZSA9IGFzY2VuZGluZykge1xuICBsZXQgbWluO1xuICBsZXQgZGVmaW5lZCA9IGZhbHNlO1xuICBpZiAoY29tcGFyZS5sZW5ndGggPT09IDEpIHtcbiAgICBsZXQgbWluVmFsdWU7XG4gICAgZm9yIChjb25zdCBlbGVtZW50IG9mIHZhbHVlcykge1xuICAgICAgY29uc3QgdmFsdWUgPSBjb21wYXJlKGVsZW1lbnQpO1xuICAgICAgaWYgKGRlZmluZWRcbiAgICAgICAgICA/IGFzY2VuZGluZyh2YWx1ZSwgbWluVmFsdWUpIDwgMFxuICAgICAgICAgIDogYXNjZW5kaW5nKHZhbHVlLCB2YWx1ZSkgPT09IDApIHtcbiAgICAgICAgbWluID0gZWxlbWVudDtcbiAgICAgICAgbWluVmFsdWUgPSB2YWx1ZTtcbiAgICAgICAgZGVmaW5lZCA9IHRydWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoZGVmaW5lZFxuICAgICAgICAgID8gY29tcGFyZSh2YWx1ZSwgbWluKSA8IDBcbiAgICAgICAgICA6IGNvbXBhcmUodmFsdWUsIHZhbHVlKSA9PT0gMCkge1xuICAgICAgICBtaW4gPSB2YWx1ZTtcbiAgICAgICAgZGVmaW5lZCA9IHRydWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBtaW47XG59XG4iXSwibmFtZXMiOlsiYXNjZW5kaW5nIiwibGVhc3QiLCJ2YWx1ZXMiLCJjb21wYXJlIiwibWluIiwiZGVmaW5lZCIsImxlbmd0aCIsIm1pblZhbHVlIiwiZWxlbWVudCIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/least.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/leastIndex.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-array/src/leastIndex.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leastIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/../../node_modules/d3-array/src/minIndex.js\");\n\n\nfunction leastIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (compare.length === 1) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n    let minValue;\n    let min = -1;\n    let index = -1;\n    for (const value of values){\n        ++index;\n        if (min < 0 ? compare(value, value) === 0 : compare(value, minValue) < 0) {\n            minValue = value;\n            min = index;\n        }\n    }\n    return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9sZWFzdEluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1QztBQUNGO0FBRXRCLFNBQVNFLFdBQVdDLE1BQU0sRUFBRUMsVUFBVUoscURBQVM7SUFDNUQsSUFBSUksUUFBUUMsTUFBTSxLQUFLLEdBQUcsT0FBT0osd0RBQVFBLENBQUNFLFFBQVFDO0lBQ2xELElBQUlFO0lBQ0osSUFBSUMsTUFBTSxDQUFDO0lBQ1gsSUFBSUMsUUFBUSxDQUFDO0lBQ2IsS0FBSyxNQUFNQyxTQUFTTixPQUFRO1FBQzFCLEVBQUVLO1FBQ0YsSUFBSUQsTUFBTSxJQUNKSCxRQUFRSyxPQUFPQSxXQUFXLElBQzFCTCxRQUFRSyxPQUFPSCxZQUFZLEdBQUc7WUFDbENBLFdBQVdHO1lBQ1hGLE1BQU1DO1FBQ1I7SUFDRjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0SW5kZXguanM/NzgzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IG1pbkluZGV4IGZyb20gXCIuL21pbkluZGV4LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGxlYXN0SW5kZXgodmFsdWVzLCBjb21wYXJlID0gYXNjZW5kaW5nKSB7XG4gIGlmIChjb21wYXJlLmxlbmd0aCA9PT0gMSkgcmV0dXJuIG1pbkluZGV4KHZhbHVlcywgY29tcGFyZSk7XG4gIGxldCBtaW5WYWx1ZTtcbiAgbGV0IG1pbiA9IC0xO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICArK2luZGV4O1xuICAgIGlmIChtaW4gPCAwXG4gICAgICAgID8gY29tcGFyZSh2YWx1ZSwgdmFsdWUpID09PSAwXG4gICAgICAgIDogY29tcGFyZSh2YWx1ZSwgbWluVmFsdWUpIDwgMCkge1xuICAgICAgbWluVmFsdWUgPSB2YWx1ZTtcbiAgICAgIG1pbiA9IGluZGV4O1xuICAgIH1cbiAgfVxuICByZXR1cm4gbWluO1xufVxuIl0sIm5hbWVzIjpbImFzY2VuZGluZyIsIm1pbkluZGV4IiwibGVhc3RJbmRleCIsInZhbHVlcyIsImNvbXBhcmUiLCJsZW5ndGgiLCJtaW5WYWx1ZSIsIm1pbiIsImluZGV4IiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/leastIndex.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/map.js":
/*!**********************************************!*\
  !*** ../../node_modules/d3-array/src/map.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ map)\n/* harmony export */ });\nfunction map(values, mapper) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n    return Array.from(values, (value, index)=>mapper(value, index, values));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tYXAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLElBQUlDLE1BQU0sRUFBRUMsTUFBTTtJQUN4QyxJQUFJLE9BQU9ELE1BQU0sQ0FBQ0UsT0FBT0MsUUFBUSxDQUFDLEtBQUssWUFBWSxNQUFNLElBQUlDLFVBQVU7SUFDdkUsSUFBSSxPQUFPSCxXQUFXLFlBQVksTUFBTSxJQUFJRyxVQUFVO0lBQ3RELE9BQU9DLE1BQU1DLElBQUksQ0FBQ04sUUFBUSxDQUFDTyxPQUFPQyxRQUFVUCxPQUFPTSxPQUFPQyxPQUFPUjtBQUNuRSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWFwLmpzPzhjZDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWFwKHZhbHVlcywgbWFwcGVyKSB7XG4gIGlmICh0eXBlb2YgdmFsdWVzW1N5bWJvbC5pdGVyYXRvcl0gIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInZhbHVlcyBpcyBub3QgaXRlcmFibGVcIik7XG4gIGlmICh0eXBlb2YgbWFwcGVyICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJtYXBwZXIgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIHJldHVybiBBcnJheS5mcm9tKHZhbHVlcywgKHZhbHVlLCBpbmRleCkgPT4gbWFwcGVyKHZhbHVlLCBpbmRleCwgdmFsdWVzKSk7XG59XG4iXSwibmFtZXMiOlsibWFwIiwidmFsdWVzIiwibWFwcGVyIiwiU3ltYm9sIiwiaXRlcmF0b3IiLCJUeXBlRXJyb3IiLCJBcnJheSIsImZyb20iLCJ2YWx1ZSIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/map.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/max.js":
/*!**********************************************!*\
  !*** ../../node_modules/d3-array/src/max.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ max)\n/* harmony export */ });\nfunction max(values, valueof) {\n    let max;\n    if (valueof === undefined) {\n        for (const value of values){\n            if (value != null && (max < value || max === undefined && value >= value)) {\n                max = value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n                max = value;\n            }\n        }\n    }\n    return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tYXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLElBQUlDLE1BQU0sRUFBRUMsT0FBTztJQUN6QyxJQUFJRjtJQUNKLElBQUlFLFlBQVlDLFdBQVc7UUFDekIsS0FBSyxNQUFNQyxTQUFTSCxPQUFRO1lBQzFCLElBQUlHLFNBQVMsUUFDTEosQ0FBQUEsTUFBTUksU0FBVUosUUFBUUcsYUFBYUMsU0FBU0EsS0FBSyxHQUFJO2dCQUM3REosTUFBTUk7WUFDUjtRQUNGO0lBQ0YsT0FBTztRQUNMLElBQUlDLFFBQVEsQ0FBQztRQUNiLEtBQUssSUFBSUQsU0FBU0gsT0FBUTtZQUN4QixJQUFJLENBQUNHLFFBQVFGLFFBQVFFLE9BQU8sRUFBRUMsT0FBT0osT0FBTSxLQUFNLFFBQ3pDRCxDQUFBQSxNQUFNSSxTQUFVSixRQUFRRyxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESixNQUFNSTtZQUNSO1FBQ0Y7SUFDRjtJQUNBLE9BQU9KO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heC5qcz9mMmVkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1heCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1heDtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1heDtcbn1cbiJdLCJuYW1lcyI6WyJtYXgiLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwidW5kZWZpbmVkIiwidmFsdWUiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/max.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/maxIndex.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/maxIndex.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ maxIndex)\n/* harmony export */ });\nfunction maxIndex(values, valueof) {\n    let max;\n    let maxIndex = -1;\n    let index = -1;\n    if (valueof === undefined) {\n        for (const value of values){\n            ++index;\n            if (value != null && (max < value || max === undefined && value >= value)) {\n                max = value, maxIndex = index;\n            }\n        }\n    } else {\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n                max = value, maxIndex = index;\n            }\n        }\n    }\n    return maxIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tYXhJbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsU0FBU0MsTUFBTSxFQUFFQyxPQUFPO0lBQzlDLElBQUlDO0lBQ0osSUFBSUgsV0FBVyxDQUFDO0lBQ2hCLElBQUlJLFFBQVEsQ0FBQztJQUNiLElBQUlGLFlBQVlHLFdBQVc7UUFDekIsS0FBSyxNQUFNQyxTQUFTTCxPQUFRO1lBQzFCLEVBQUVHO1lBQ0YsSUFBSUUsU0FBUyxRQUNMSCxDQUFBQSxNQUFNRyxTQUFVSCxRQUFRRSxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESCxNQUFNRyxPQUFPTixXQUFXSTtZQUMxQjtRQUNGO0lBQ0YsT0FBTztRQUNMLEtBQUssSUFBSUUsU0FBU0wsT0FBUTtZQUN4QixJQUFJLENBQUNLLFFBQVFKLFFBQVFJLE9BQU8sRUFBRUYsT0FBT0gsT0FBTSxLQUFNLFFBQ3pDRSxDQUFBQSxNQUFNRyxTQUFVSCxRQUFRRSxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESCxNQUFNRyxPQUFPTixXQUFXSTtZQUMxQjtRQUNGO0lBQ0Y7SUFDQSxPQUFPSjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tYXhJbmRleC5qcz8wZDA5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1heEluZGV4KHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgbWF4O1xuICBsZXQgbWF4SW5kZXggPSAtMTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgKytpbmRleDtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1heCA8IHZhbHVlIHx8IChtYXggPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1heCA9IHZhbHVlLCBtYXhJbmRleCA9IGluZGV4O1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1heCA8IHZhbHVlIHx8IChtYXggPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1heCA9IHZhbHVlLCBtYXhJbmRleCA9IGluZGV4O1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWF4SW5kZXg7XG59XG4iXSwibmFtZXMiOlsibWF4SW5kZXgiLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwibWF4IiwiaW5kZXgiLCJ1bmRlZmluZWQiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/maxIndex.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/mean.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/mean.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mean)\n/* harmony export */ });\nfunction mean(values, valueof) {\n    let count = 0;\n    let sum = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                ++count, sum += value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                ++count, sum += value;\n            }\n        }\n    }\n    if (count) return sum / count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tZWFuLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxLQUFLQyxNQUFNLEVBQUVDLE9BQU87SUFDMUMsSUFBSUMsUUFBUTtJQUNaLElBQUlDLE1BQU07SUFDVixJQUFJRixZQUFZRyxXQUFXO1FBQ3pCLEtBQUssSUFBSUMsU0FBU0wsT0FBUTtZQUN4QixJQUFJSyxTQUFTLFFBQVEsQ0FBQ0EsUUFBUSxDQUFDQSxLQUFJLEtBQU1BLE9BQU87Z0JBQzlDLEVBQUVILE9BQU9DLE9BQU9FO1lBQ2xCO1FBQ0Y7SUFDRixPQUFPO1FBQ0wsSUFBSUMsUUFBUSxDQUFDO1FBQ2IsS0FBSyxJQUFJRCxTQUFTTCxPQUFRO1lBQ3hCLElBQUksQ0FBQ0ssUUFBUUosUUFBUUksT0FBTyxFQUFFQyxPQUFPTixPQUFNLEtBQU0sUUFBUSxDQUFDSyxRQUFRLENBQUNBLEtBQUksS0FBTUEsT0FBTztnQkFDbEYsRUFBRUgsT0FBT0MsT0FBT0U7WUFDbEI7UUFDRjtJQUNGO0lBQ0EsSUFBSUgsT0FBTyxPQUFPQyxNQUFNRDtBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWVhbi5qcz9jYTJjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1lYW4odmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBjb3VudCA9IDA7XG4gIGxldCBzdW0gPSAwO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQsIHN1bSArPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQsIHN1bSArPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgaWYgKGNvdW50KSByZXR1cm4gc3VtIC8gY291bnQ7XG59XG4iXSwibmFtZXMiOlsibWVhbiIsInZhbHVlcyIsInZhbHVlb2YiLCJjb3VudCIsInN1bSIsInVuZGVmaW5lZCIsInZhbHVlIiwiaW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/mean.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/median.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/median.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ median),\n/* harmony export */   medianIndex: () => (/* binding */ medianIndex)\n/* harmony export */ });\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/../../node_modules/d3-array/src/quantile.js\");\n\nfunction median(values, valueof) {\n    return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, 0.5, valueof);\n}\nfunction medianIndex(values, valueof) {\n    return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__.quantileIndex)(values, 0.5, valueof);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tZWRpYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNEO0FBRXZDLFNBQVNFLE9BQU9DLE1BQU0sRUFBRUMsT0FBTztJQUM1QyxPQUFPSix3REFBUUEsQ0FBQ0csUUFBUSxLQUFLQztBQUMvQjtBQUVPLFNBQVNDLFlBQVlGLE1BQU0sRUFBRUMsT0FBTztJQUN6QyxPQUFPSCwyREFBYUEsQ0FBQ0UsUUFBUSxLQUFLQztBQUNwQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWVkaWFuLmpzPzNlYTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHF1YW50aWxlLCB7cXVhbnRpbGVJbmRleH0gZnJvbSBcIi4vcXVhbnRpbGUuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWVkaWFuKHZhbHVlcywgdmFsdWVvZikge1xuICByZXR1cm4gcXVhbnRpbGUodmFsdWVzLCAwLjUsIHZhbHVlb2YpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gbWVkaWFuSW5kZXgodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIHJldHVybiBxdWFudGlsZUluZGV4KHZhbHVlcywgMC41LCB2YWx1ZW9mKTtcbn1cbiJdLCJuYW1lcyI6WyJxdWFudGlsZSIsInF1YW50aWxlSW5kZXgiLCJtZWRpYW4iLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwibWVkaWFuSW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/median.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/merge.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/merge.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ merge)\n/* harmony export */ });\nfunction* flatten(arrays) {\n    for (const array of arrays){\n        yield* array;\n    }\n}\nfunction merge(arrays) {\n    return Array.from(flatten(arrays));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsVUFBVUEsUUFBUUMsTUFBTTtJQUN0QixLQUFLLE1BQU1DLFNBQVNELE9BQVE7UUFDMUIsT0FBT0M7SUFDVDtBQUNGO0FBRWUsU0FBU0MsTUFBTUYsTUFBTTtJQUNsQyxPQUFPRyxNQUFNQyxJQUFJLENBQUNMLFFBQVFDO0FBQzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tZXJnZS5qcz9kZDU4Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uKiBmbGF0dGVuKGFycmF5cykge1xuICBmb3IgKGNvbnN0IGFycmF5IG9mIGFycmF5cykge1xuICAgIHlpZWxkKiBhcnJheTtcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtZXJnZShhcnJheXMpIHtcbiAgcmV0dXJuIEFycmF5LmZyb20oZmxhdHRlbihhcnJheXMpKTtcbn1cbiJdLCJuYW1lcyI6WyJmbGF0dGVuIiwiYXJyYXlzIiwiYXJyYXkiLCJtZXJnZSIsIkFycmF5IiwiZnJvbSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/merge.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/min.js":
/*!**********************************************!*\
  !*** ../../node_modules/d3-array/src/min.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ min)\n/* harmony export */ });\nfunction min(values, valueof) {\n    let min;\n    if (valueof === undefined) {\n        for (const value of values){\n            if (value != null && (min > value || min === undefined && value >= value)) {\n                min = value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n                min = value;\n            }\n        }\n    }\n    return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9taW4uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLElBQUlDLE1BQU0sRUFBRUMsT0FBTztJQUN6QyxJQUFJRjtJQUNKLElBQUlFLFlBQVlDLFdBQVc7UUFDekIsS0FBSyxNQUFNQyxTQUFTSCxPQUFRO1lBQzFCLElBQUlHLFNBQVMsUUFDTEosQ0FBQUEsTUFBTUksU0FBVUosUUFBUUcsYUFBYUMsU0FBU0EsS0FBSyxHQUFJO2dCQUM3REosTUFBTUk7WUFDUjtRQUNGO0lBQ0YsT0FBTztRQUNMLElBQUlDLFFBQVEsQ0FBQztRQUNiLEtBQUssSUFBSUQsU0FBU0gsT0FBUTtZQUN4QixJQUFJLENBQUNHLFFBQVFGLFFBQVFFLE9BQU8sRUFBRUMsT0FBT0osT0FBTSxLQUFNLFFBQ3pDRCxDQUFBQSxNQUFNSSxTQUFVSixRQUFRRyxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESixNQUFNSTtZQUNSO1FBQ0Y7SUFDRjtJQUNBLE9BQU9KO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbi5qcz81YTFiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1pbih2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1pbjtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1pbjtcbn1cbiJdLCJuYW1lcyI6WyJtaW4iLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwidW5kZWZpbmVkIiwidmFsdWUiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/min.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/minIndex.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/minIndex.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ minIndex)\n/* harmony export */ });\nfunction minIndex(values, valueof) {\n    let min;\n    let minIndex = -1;\n    let index = -1;\n    if (valueof === undefined) {\n        for (const value of values){\n            ++index;\n            if (value != null && (min > value || min === undefined && value >= value)) {\n                min = value, minIndex = index;\n            }\n        }\n    } else {\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n                min = value, minIndex = index;\n            }\n        }\n    }\n    return minIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9taW5JbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsU0FBU0MsTUFBTSxFQUFFQyxPQUFPO0lBQzlDLElBQUlDO0lBQ0osSUFBSUgsV0FBVyxDQUFDO0lBQ2hCLElBQUlJLFFBQVEsQ0FBQztJQUNiLElBQUlGLFlBQVlHLFdBQVc7UUFDekIsS0FBSyxNQUFNQyxTQUFTTCxPQUFRO1lBQzFCLEVBQUVHO1lBQ0YsSUFBSUUsU0FBUyxRQUNMSCxDQUFBQSxNQUFNRyxTQUFVSCxRQUFRRSxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESCxNQUFNRyxPQUFPTixXQUFXSTtZQUMxQjtRQUNGO0lBQ0YsT0FBTztRQUNMLEtBQUssSUFBSUUsU0FBU0wsT0FBUTtZQUN4QixJQUFJLENBQUNLLFFBQVFKLFFBQVFJLE9BQU8sRUFBRUYsT0FBT0gsT0FBTSxLQUFNLFFBQ3pDRSxDQUFBQSxNQUFNRyxTQUFVSCxRQUFRRSxhQUFhQyxTQUFTQSxLQUFLLEdBQUk7Z0JBQzdESCxNQUFNRyxPQUFPTixXQUFXSTtZQUMxQjtRQUNGO0lBQ0Y7SUFDQSxPQUFPSjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9taW5JbmRleC5qcz8yMjliIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1pbkluZGV4KHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgbWluO1xuICBsZXQgbWluSW5kZXggPSAtMTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgKytpbmRleDtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1pbiA+IHZhbHVlIHx8IChtaW4gPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1pbiA9IHZhbHVlLCBtaW5JbmRleCA9IGluZGV4O1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1pbiA+IHZhbHVlIHx8IChtaW4gPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1pbiA9IHZhbHVlLCBtaW5JbmRleCA9IGluZGV4O1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWluSW5kZXg7XG59XG4iXSwibmFtZXMiOlsibWluSW5kZXgiLCJ2YWx1ZXMiLCJ2YWx1ZW9mIiwibWluIiwiaW5kZXgiLCJ1bmRlZmluZWQiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/minIndex.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/mode.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/mode.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mode)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/../../node_modules/internmap/src/index.js\");\n\nfunction mode(values, valueof) {\n    const counts = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternMap();\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && value >= value) {\n                counts.set(value, (counts.get(value) || 0) + 1);\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && value >= value) {\n                counts.set(value, (counts.get(value) || 0) + 1);\n            }\n        }\n    }\n    let modeValue;\n    let modeCount = 0;\n    for (const [value, count] of counts){\n        if (count > modeCount) {\n            modeCount = count;\n            modeValue = value;\n        }\n    }\n    return modeValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/mode.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/nice.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/nice.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ nice)\n/* harmony export */ });\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/../../node_modules/d3-array/src/ticks.js\");\n\nfunction nice(start, stop, count) {\n    let prestep;\n    while(true){\n        const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_0__.tickIncrement)(start, stop, count);\n        if (step === prestep || step === 0 || !isFinite(step)) {\n            return [\n                start,\n                stop\n            ];\n        } else if (step > 0) {\n            start = Math.floor(start / step) * step;\n            stop = Math.ceil(stop / step) * step;\n        } else if (step < 0) {\n            start = Math.ceil(start * step) / step;\n            stop = Math.floor(stop * step) / step;\n        }\n        prestep = step;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9uaWNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlDO0FBRTFCLFNBQVNDLEtBQUtDLEtBQUssRUFBRUMsSUFBSSxFQUFFQyxLQUFLO0lBQzdDLElBQUlDO0lBQ0osTUFBTyxLQUFNO1FBQ1gsTUFBTUMsT0FBT04sd0RBQWFBLENBQUNFLE9BQU9DLE1BQU1DO1FBQ3hDLElBQUlFLFNBQVNELFdBQVdDLFNBQVMsS0FBSyxDQUFDQyxTQUFTRCxPQUFPO1lBQ3JELE9BQU87Z0JBQUNKO2dCQUFPQzthQUFLO1FBQ3RCLE9BQU8sSUFBSUcsT0FBTyxHQUFHO1lBQ25CSixRQUFRTSxLQUFLQyxLQUFLLENBQUNQLFFBQVFJLFFBQVFBO1lBQ25DSCxPQUFPSyxLQUFLRSxJQUFJLENBQUNQLE9BQU9HLFFBQVFBO1FBQ2xDLE9BQU8sSUFBSUEsT0FBTyxHQUFHO1lBQ25CSixRQUFRTSxLQUFLRSxJQUFJLENBQUNSLFFBQVFJLFFBQVFBO1lBQ2xDSCxPQUFPSyxLQUFLQyxLQUFLLENBQUNOLE9BQU9HLFFBQVFBO1FBQ25DO1FBQ0FELFVBQVVDO0lBQ1o7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbmljZS5qcz9jN2FmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dGlja0luY3JlbWVudH0gZnJvbSBcIi4vdGlja3MuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbmljZShzdGFydCwgc3RvcCwgY291bnQpIHtcbiAgbGV0IHByZXN0ZXA7XG4gIHdoaWxlICh0cnVlKSB7XG4gICAgY29uc3Qgc3RlcCA9IHRpY2tJbmNyZW1lbnQoc3RhcnQsIHN0b3AsIGNvdW50KTtcbiAgICBpZiAoc3RlcCA9PT0gcHJlc3RlcCB8fCBzdGVwID09PSAwIHx8ICFpc0Zpbml0ZShzdGVwKSkge1xuICAgICAgcmV0dXJuIFtzdGFydCwgc3RvcF07XG4gICAgfSBlbHNlIGlmIChzdGVwID4gMCkge1xuICAgICAgc3RhcnQgPSBNYXRoLmZsb29yKHN0YXJ0IC8gc3RlcCkgKiBzdGVwO1xuICAgICAgc3RvcCA9IE1hdGguY2VpbChzdG9wIC8gc3RlcCkgKiBzdGVwO1xuICAgIH0gZWxzZSBpZiAoc3RlcCA8IDApIHtcbiAgICAgIHN0YXJ0ID0gTWF0aC5jZWlsKHN0YXJ0ICogc3RlcCkgLyBzdGVwO1xuICAgICAgc3RvcCA9IE1hdGguZmxvb3Ioc3RvcCAqIHN0ZXApIC8gc3RlcDtcbiAgICB9XG4gICAgcHJlc3RlcCA9IHN0ZXA7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJ0aWNrSW5jcmVtZW50IiwibmljZSIsInN0YXJ0Iiwic3RvcCIsImNvdW50IiwicHJlc3RlcCIsInN0ZXAiLCJpc0Zpbml0ZSIsIk1hdGgiLCJmbG9vciIsImNlaWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/nice.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/number.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/number.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ number),\n/* harmony export */   numbers: () => (/* binding */ numbers)\n/* harmony export */ });\nfunction number(x) {\n    return x === null ? NaN : +x;\n}\nfunction* numbers(values, valueof) {\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                yield value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                yield value;\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9udW1iZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZSxTQUFTQSxPQUFPQyxDQUFDO0lBQzlCLE9BQU9BLE1BQU0sT0FBT0MsTUFBTSxDQUFDRDtBQUM3QjtBQUVPLFVBQVVFLFFBQVFDLE1BQU0sRUFBRUMsT0FBTztJQUN0QyxJQUFJQSxZQUFZQyxXQUFXO1FBQ3pCLEtBQUssSUFBSUMsU0FBU0gsT0FBUTtZQUN4QixJQUFJRyxTQUFTLFFBQVEsQ0FBQ0EsUUFBUSxDQUFDQSxLQUFJLEtBQU1BLE9BQU87Z0JBQzlDLE1BQU1BO1lBQ1I7UUFDRjtJQUNGLE9BQU87UUFDTCxJQUFJQyxRQUFRLENBQUM7UUFDYixLQUFLLElBQUlELFNBQVNILE9BQVE7WUFDeEIsSUFBSSxDQUFDRyxRQUFRRixRQUFRRSxPQUFPLEVBQUVDLE9BQU9KLE9BQU0sS0FBTSxRQUFRLENBQUNHLFFBQVEsQ0FBQ0EsS0FBSSxLQUFNQSxPQUFPO2dCQUNsRixNQUFNQTtZQUNSO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9udW1iZXIuanM/OWRiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBudW1iZXIoeCkge1xuICByZXR1cm4geCA9PT0gbnVsbCA/IE5hTiA6ICt4O1xufVxuXG5leHBvcnQgZnVuY3Rpb24qIG51bWJlcnModmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgeWllbGQgdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICB5aWVsZCB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJudW1iZXIiLCJ4IiwiTmFOIiwibnVtYmVycyIsInZhbHVlcyIsInZhbHVlb2YiLCJ1bmRlZmluZWQiLCJ2YWx1ZSIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/number.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/pairs.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/pairs.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pairs),\n/* harmony export */   pair: () => (/* binding */ pair)\n/* harmony export */ });\nfunction pairs(values, pairof = pair) {\n    const pairs = [];\n    let previous;\n    let first = false;\n    for (const value of values){\n        if (first) pairs.push(pairof(previous, value));\n        previous = value;\n        first = true;\n    }\n    return pairs;\n}\nfunction pair(a, b) {\n    return [\n        a,\n        b\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9wYWlycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFlLFNBQVNBLE1BQU1DLE1BQU0sRUFBRUMsU0FBU0MsSUFBSTtJQUNqRCxNQUFNSCxRQUFRLEVBQUU7SUFDaEIsSUFBSUk7SUFDSixJQUFJQyxRQUFRO0lBQ1osS0FBSyxNQUFNQyxTQUFTTCxPQUFRO1FBQzFCLElBQUlJLE9BQU9MLE1BQU1PLElBQUksQ0FBQ0wsT0FBT0UsVUFBVUU7UUFDdkNGLFdBQVdFO1FBQ1hELFFBQVE7SUFDVjtJQUNBLE9BQU9MO0FBQ1Q7QUFFTyxTQUFTRyxLQUFLSyxDQUFDLEVBQUVDLENBQUM7SUFDdkIsT0FBTztRQUFDRDtRQUFHQztLQUFFO0FBQ2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3BhaXJzLmpzPzZjZmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcGFpcnModmFsdWVzLCBwYWlyb2YgPSBwYWlyKSB7XG4gIGNvbnN0IHBhaXJzID0gW107XG4gIGxldCBwcmV2aW91cztcbiAgbGV0IGZpcnN0ID0gZmFsc2U7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgaWYgKGZpcnN0KSBwYWlycy5wdXNoKHBhaXJvZihwcmV2aW91cywgdmFsdWUpKTtcbiAgICBwcmV2aW91cyA9IHZhbHVlO1xuICAgIGZpcnN0ID0gdHJ1ZTtcbiAgfVxuICByZXR1cm4gcGFpcnM7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBwYWlyKGEsIGIpIHtcbiAgcmV0dXJuIFthLCBiXTtcbn1cbiJdLCJuYW1lcyI6WyJwYWlycyIsInZhbHVlcyIsInBhaXJvZiIsInBhaXIiLCJwcmV2aW91cyIsImZpcnN0IiwidmFsdWUiLCJwdXNoIiwiYSIsImIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/pairs.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/permute.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-array/src/permute.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ permute)\n/* harmony export */ });\nfunction permute(source, keys) {\n    return Array.from(keys, (key)=>source[key]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9wZXJtdXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxRQUFRQyxNQUFNLEVBQUVDLElBQUk7SUFDMUMsT0FBT0MsTUFBTUMsSUFBSSxDQUFDRixNQUFNRyxDQUFBQSxNQUFPSixNQUFNLENBQUNJLElBQUk7QUFDNUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3Blcm11dGUuanM/ZDgwNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBwZXJtdXRlKHNvdXJjZSwga2V5cykge1xuICByZXR1cm4gQXJyYXkuZnJvbShrZXlzLCBrZXkgPT4gc291cmNlW2tleV0pO1xufVxuIl0sIm5hbWVzIjpbInBlcm11dGUiLCJzb3VyY2UiLCJrZXlzIiwiQXJyYXkiLCJmcm9tIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/permute.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/quantile.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/quantile.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantile),\n/* harmony export */   quantileIndex: () => (/* binding */ quantileIndex),\n/* harmony export */   quantileSorted: () => (/* binding */ quantileSorted)\n/* harmony export */ });\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./max.js */ \"(ssr)/../../node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/../../node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./min.js */ \"(ssr)/../../node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/../../node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/../../node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.js */ \"(ssr)/../../node_modules/d3-array/src/number.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/../../node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/../../node_modules/d3-array/src/greatest.js\");\n\n\n\n\n\n\n\n\nfunction quantile(values, p, valueof) {\n    values = Float64Array.from((0,_number_js__WEBPACK_IMPORTED_MODULE_0__.numbers)(values, valueof));\n    if (!(n = values.length) || isNaN(p = +p)) return;\n    if (p <= 0 || n < 2) return (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n    if (p >= 1) return (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(values);\n    var n, i = (n - 1) * p, i0 = Math.floor(i), value0 = (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values, i0).subarray(0, i0 + 1)), value1 = (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values.subarray(i0 + 1));\n    return value0 + (value1 - value0) * (i - i0);\n}\nfunction quantileSorted(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (!(n = values.length) || isNaN(p = +p)) return;\n    if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n    if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n    var n, i = (n - 1) * p, i0 = Math.floor(i), value0 = +valueof(values[i0], i0, values), value1 = +valueof(values[i0 + 1], i0 + 1, values);\n    return value0 + (value1 - value0) * (i - i0);\n}\nfunction quantileIndex(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (isNaN(p = +p)) return;\n    numbers = Float64Array.from(values, (_, i)=>(0,_number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(valueof(values[i], i, values)));\n    if (p <= 0) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(numbers);\n    if (p >= 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(numbers);\n    var numbers, index = Uint32Array.from(values, (_, i)=>i), j = numbers.length - 1, i = Math.floor(j * p);\n    (0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(index, i, 0, j, (i, j)=>(0,_sort_js__WEBPACK_IMPORTED_MODULE_6__.ascendingDefined)(numbers[i], numbers[j]));\n    i = (0,_greatest_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(index.subarray(0, i + 1), (i)=>numbers[i]);\n    return i >= 0 ? i : -1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/quantile.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/quickselect.js":
/*!******************************************************!*\
  !*** ../../node_modules/d3-array/src/quickselect.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quickselect)\n/* harmony export */ });\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/../../node_modules/d3-array/src/sort.js\");\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = Infinity, compare) {\n    k = Math.floor(k);\n    left = Math.floor(Math.max(0, left));\n    right = Math.floor(Math.min(array.length - 1, right));\n    if (!(left <= k && k <= right)) return array;\n    compare = compare === undefined ? _sort_js__WEBPACK_IMPORTED_MODULE_0__.ascendingDefined : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__.compareDefined)(compare);\n    while(right > left){\n        if (right - left > 600) {\n            const n = right - left + 1;\n            const m = k - left + 1;\n            const z = Math.log(n);\n            const s = 0.5 * Math.exp(2 * z / 3);\n            const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n            const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n            const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n            quickselect(array, k, newLeft, newRight, compare);\n        }\n        const t = array[k];\n        let i = left;\n        let j = right;\n        swap(array, left, k);\n        if (compare(array[right], t) > 0) swap(array, left, right);\n        while(i < j){\n            swap(array, i, j), ++i, --j;\n            while(compare(array[i], t) < 0)++i;\n            while(compare(array[j], t) > 0)--j;\n        }\n        if (compare(array[left], t) === 0) swap(array, left, j);\n        else ++j, swap(array, j, right);\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n    return array;\n}\nfunction swap(array, i, j) {\n    const t = array[i];\n    array[i] = array[j];\n    array[j] = t;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/quickselect.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/range.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/range.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ range)\n/* harmony export */ });\nfunction range(start, stop, step) {\n    start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n    var i = -1, n = Math.max(0, Math.ceil((stop - start) / step)) | 0, range = new Array(n);\n    while(++i < n){\n        range[i] = start + i * step;\n    }\n    return range;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9yYW5nZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsTUFBTUMsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLElBQUk7SUFDN0NGLFFBQVEsQ0FBQ0EsT0FBT0MsT0FBTyxDQUFDQSxNQUFNQyxPQUFPLENBQUNDLElBQUlDLFVBQVVDLE1BQU0sSUFBSSxJQUFLSixDQUFBQSxPQUFPRCxPQUFPQSxRQUFRLEdBQUcsS0FBS0csSUFBSSxJQUFJLElBQUksQ0FBQ0Q7SUFFOUcsSUFBSUksSUFBSSxDQUFDLEdBQ0xILElBQUlJLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRCxLQUFLRSxJQUFJLENBQUMsQ0FBQ1IsT0FBT0QsS0FBSSxJQUFLRSxTQUFTLEdBQ3BESCxRQUFRLElBQUlXLE1BQU1QO0lBRXRCLE1BQU8sRUFBRUcsSUFBSUgsRUFBRztRQUNkSixLQUFLLENBQUNPLEVBQUUsR0FBR04sUUFBUU0sSUFBSUo7SUFDekI7SUFFQSxPQUFPSDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9yYW5nZS5qcz9hNmZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJhbmdlKHN0YXJ0LCBzdG9wLCBzdGVwKSB7XG4gIHN0YXJ0ID0gK3N0YXJ0LCBzdG9wID0gK3N0b3AsIHN0ZXAgPSAobiA9IGFyZ3VtZW50cy5sZW5ndGgpIDwgMiA/IChzdG9wID0gc3RhcnQsIHN0YXJ0ID0gMCwgMSkgOiBuIDwgMyA/IDEgOiArc3RlcDtcblxuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IE1hdGgubWF4KDAsIE1hdGguY2VpbCgoc3RvcCAtIHN0YXJ0KSAvIHN0ZXApKSB8IDAsXG4gICAgICByYW5nZSA9IG5ldyBBcnJheShuKTtcblxuICB3aGlsZSAoKytpIDwgbikge1xuICAgIHJhbmdlW2ldID0gc3RhcnQgKyBpICogc3RlcDtcbiAgfVxuXG4gIHJldHVybiByYW5nZTtcbn1cbiJdLCJuYW1lcyI6WyJyYW5nZSIsInN0YXJ0Iiwic3RvcCIsInN0ZXAiLCJuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiaSIsIk1hdGgiLCJtYXgiLCJjZWlsIiwiQXJyYXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/range.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/rank.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/rank.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rank)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/../../node_modules/d3-array/src/sort.js\");\n\n\nfunction rank(values, valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    let V = Array.from(values);\n    const R = new Float64Array(V.length);\n    if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n    const compareIndex = (i, j)=>valueof(V[i], V[j]);\n    let k, r;\n    values = Uint32Array.from(V, (_, i)=>i);\n    // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n    values.sort(valueof === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] ? (i, j)=>(0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.ascendingDefined)(V[i], V[j]) : (0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.compareDefined)(compareIndex));\n    values.forEach((j, i)=>{\n        const c = compareIndex(j, k === undefined ? j : k);\n        if (c >= 0) {\n            if (k === undefined || c > 0) k = j, r = i;\n            R[j] = r;\n        } else {\n            R[j] = NaN;\n        }\n    });\n    return R;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/rank.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/reduce.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/reduce.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(values, reducer, value) {\n    if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n    const iterator = values[Symbol.iterator]();\n    let done, next, index = -1;\n    if (arguments.length < 3) {\n        ({ done, value } = iterator.next());\n        if (done) return;\n        ++index;\n    }\n    while({ done, value: next } = iterator.next(), !done){\n        value = reducer(value, next, ++index, values);\n    }\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9yZWR1Y2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLE9BQU9DLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxLQUFLO0lBQ25ELElBQUksT0FBT0QsWUFBWSxZQUFZLE1BQU0sSUFBSUUsVUFBVTtJQUN2RCxNQUFNQyxXQUFXSixNQUFNLENBQUNLLE9BQU9ELFFBQVEsQ0FBQztJQUN4QyxJQUFJRSxNQUFNQyxNQUFNQyxRQUFRLENBQUM7SUFDekIsSUFBSUMsVUFBVUMsTUFBTSxHQUFHLEdBQUc7UUFDdkIsR0FBQ0osSUFBSSxFQUFFSixLQUFLLEVBQUMsR0FBR0UsU0FBU0csSUFBSSxFQUFDO1FBQy9CLElBQUlELE1BQU07UUFDVixFQUFFRTtJQUNKO0lBQ0EsTUFBTyxFQUFFRixJQUFJLEVBQUVKLE9BQU9LLElBQUksRUFBQyxHQUFHSCxTQUFTRyxJQUFJLElBQUssQ0FBQ0QsS0FBTTtRQUNyREosUUFBUUQsUUFBUUMsT0FBT0ssTUFBTSxFQUFFQyxPQUFPUjtJQUN4QztJQUNBLE9BQU9FO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JlZHVjZS5qcz9iZTM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJlZHVjZSh2YWx1ZXMsIHJlZHVjZXIsIHZhbHVlKSB7XG4gIGlmICh0eXBlb2YgcmVkdWNlciAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwicmVkdWNlciBpcyBub3QgYSBmdW5jdGlvblwiKTtcbiAgY29uc3QgaXRlcmF0b3IgPSB2YWx1ZXNbU3ltYm9sLml0ZXJhdG9yXSgpO1xuICBsZXQgZG9uZSwgbmV4dCwgaW5kZXggPSAtMTtcbiAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPCAzKSB7XG4gICAgKHtkb25lLCB2YWx1ZX0gPSBpdGVyYXRvci5uZXh0KCkpO1xuICAgIGlmIChkb25lKSByZXR1cm47XG4gICAgKytpbmRleDtcbiAgfVxuICB3aGlsZSAoKHtkb25lLCB2YWx1ZTogbmV4dH0gPSBpdGVyYXRvci5uZXh0KCkpLCAhZG9uZSkge1xuICAgIHZhbHVlID0gcmVkdWNlcih2YWx1ZSwgbmV4dCwgKytpbmRleCwgdmFsdWVzKTtcbiAgfVxuICByZXR1cm4gdmFsdWU7XG59XG4iXSwibmFtZXMiOlsicmVkdWNlIiwidmFsdWVzIiwicmVkdWNlciIsInZhbHVlIiwiVHlwZUVycm9yIiwiaXRlcmF0b3IiLCJTeW1ib2wiLCJkb25lIiwibmV4dCIsImluZGV4IiwiYXJndW1lbnRzIiwibGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/reduce.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/reverse.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-array/src/reverse.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reverse)\n/* harmony export */ });\nfunction reverse(values) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    return Array.from(values).reverse();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9yZXZlcnNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxRQUFRQyxNQUFNO0lBQ3BDLElBQUksT0FBT0EsTUFBTSxDQUFDQyxPQUFPQyxRQUFRLENBQUMsS0FBSyxZQUFZLE1BQU0sSUFBSUMsVUFBVTtJQUN2RSxPQUFPQyxNQUFNQyxJQUFJLENBQUNMLFFBQVFELE9BQU87QUFDbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JldmVyc2UuanM/YmFiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByZXZlcnNlKHZhbHVlcykge1xuICBpZiAodHlwZW9mIHZhbHVlc1tTeW1ib2wuaXRlcmF0b3JdICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJ2YWx1ZXMgaXMgbm90IGl0ZXJhYmxlXCIpO1xuICByZXR1cm4gQXJyYXkuZnJvbSh2YWx1ZXMpLnJldmVyc2UoKTtcbn1cbiJdLCJuYW1lcyI6WyJyZXZlcnNlIiwidmFsdWVzIiwiU3ltYm9sIiwiaXRlcmF0b3IiLCJUeXBlRXJyb3IiLCJBcnJheSIsImZyb20iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/reverse.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/scan.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/scan.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ scan)\n/* harmony export */ });\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/../../node_modules/d3-array/src/leastIndex.js\");\n\nfunction scan(values, compare) {\n    const index = (0,_leastIndex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, compare);\n    return index < 0 ? undefined : index;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zY2FuLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlDO0FBRTFCLFNBQVNDLEtBQUtDLE1BQU0sRUFBRUMsT0FBTztJQUMxQyxNQUFNQyxRQUFRSiwwREFBVUEsQ0FBQ0UsUUFBUUM7SUFDakMsT0FBT0MsUUFBUSxJQUFJQyxZQUFZRDtBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvc2Nhbi5qcz9jOWU4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBsZWFzdEluZGV4IGZyb20gXCIuL2xlYXN0SW5kZXguanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc2Nhbih2YWx1ZXMsIGNvbXBhcmUpIHtcbiAgY29uc3QgaW5kZXggPSBsZWFzdEluZGV4KHZhbHVlcywgY29tcGFyZSk7XG4gIHJldHVybiBpbmRleCA8IDAgPyB1bmRlZmluZWQgOiBpbmRleDtcbn1cbiJdLCJuYW1lcyI6WyJsZWFzdEluZGV4Iiwic2NhbiIsInZhbHVlcyIsImNvbXBhcmUiLCJpbmRleCIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/scan.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/shuffle.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-array/src/shuffle.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   shuffler: () => (/* binding */ shuffler)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (shuffler(Math.random));\nfunction shuffler(random) {\n    return function shuffle(array, i0 = 0, i1 = array.length) {\n        let m = i1 - (i0 = +i0);\n        while(m){\n            const i = random() * m-- | 0, t = array[m + i0];\n            array[m + i0] = array[i + i0];\n            array[i + i0] = t;\n        }\n        return array;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zaHVmZmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsaUVBQWVBLFNBQVNDLEtBQUtDLE1BQU0sQ0FBQyxFQUFDO0FBRTlCLFNBQVNGLFNBQVNFLE1BQU07SUFDN0IsT0FBTyxTQUFTQyxRQUFRQyxLQUFLLEVBQUVDLEtBQUssQ0FBQyxFQUFFQyxLQUFLRixNQUFNRyxNQUFNO1FBQ3RELElBQUlDLElBQUlGLEtBQU1ELENBQUFBLEtBQUssQ0FBQ0EsRUFBQztRQUNyQixNQUFPRyxFQUFHO1lBQ1IsTUFBTUMsSUFBSVAsV0FBV00sTUFBTSxHQUFHRSxJQUFJTixLQUFLLENBQUNJLElBQUlILEdBQUc7WUFDL0NELEtBQUssQ0FBQ0ksSUFBSUgsR0FBRyxHQUFHRCxLQUFLLENBQUNLLElBQUlKLEdBQUc7WUFDN0JELEtBQUssQ0FBQ0ssSUFBSUosR0FBRyxHQUFHSztRQUNsQjtRQUNBLE9BQU9OO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvc2h1ZmZsZS5qcz9kYzg2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHNodWZmbGVyKE1hdGgucmFuZG9tKTtcblxuZXhwb3J0IGZ1bmN0aW9uIHNodWZmbGVyKHJhbmRvbSkge1xuICByZXR1cm4gZnVuY3Rpb24gc2h1ZmZsZShhcnJheSwgaTAgPSAwLCBpMSA9IGFycmF5Lmxlbmd0aCkge1xuICAgIGxldCBtID0gaTEgLSAoaTAgPSAraTApO1xuICAgIHdoaWxlIChtKSB7XG4gICAgICBjb25zdCBpID0gcmFuZG9tKCkgKiBtLS0gfCAwLCB0ID0gYXJyYXlbbSArIGkwXTtcbiAgICAgIGFycmF5W20gKyBpMF0gPSBhcnJheVtpICsgaTBdO1xuICAgICAgYXJyYXlbaSArIGkwXSA9IHQ7XG4gICAgfVxuICAgIHJldHVybiBhcnJheTtcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJzaHVmZmxlciIsIk1hdGgiLCJyYW5kb20iLCJzaHVmZmxlIiwiYXJyYXkiLCJpMCIsImkxIiwibGVuZ3RoIiwibSIsImkiLCJ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/shuffle.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/some.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/some.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ some)\n/* harmony export */ });\nfunction some(values, test) {\n    if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n    let index = -1;\n    for (const value of values){\n        if (test(value, ++index, values)) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zb21lLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxLQUFLQyxNQUFNLEVBQUVDLElBQUk7SUFDdkMsSUFBSSxPQUFPQSxTQUFTLFlBQVksTUFBTSxJQUFJQyxVQUFVO0lBQ3BELElBQUlDLFFBQVEsQ0FBQztJQUNiLEtBQUssTUFBTUMsU0FBU0osT0FBUTtRQUMxQixJQUFJQyxLQUFLRyxPQUFPLEVBQUVELE9BQU9ILFNBQVM7WUFDaEMsT0FBTztRQUNUO0lBQ0Y7SUFDQSxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NvbWUuanM/NjczOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzb21lKHZhbHVlcywgdGVzdCkge1xuICBpZiAodHlwZW9mIHRlc3QgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInRlc3QgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIGxldCBpbmRleCA9IC0xO1xuICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGlmICh0ZXN0KHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGZhbHNlO1xufVxuIl0sIm5hbWVzIjpbInNvbWUiLCJ2YWx1ZXMiLCJ0ZXN0IiwiVHlwZUVycm9yIiwiaW5kZXgiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/some.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/sort.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-array/src/sort.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ascendingDefined: () => (/* binding */ ascendingDefined),\n/* harmony export */   compareDefined: () => (/* binding */ compareDefined),\n/* harmony export */   \"default\": () => (/* binding */ sort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/../../node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/../../node_modules/d3-array/src/permute.js\");\n\n\nfunction sort(values, ...F) {\n    if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n    values = Array.from(values);\n    let [f] = F;\n    if (f && f.length !== 2 || F.length > 1) {\n        const index = Uint32Array.from(values, (d, i)=>i);\n        if (F.length > 1) {\n            F = F.map((f)=>values.map(f));\n            index.sort((i, j)=>{\n                for (const f of F){\n                    const c = ascendingDefined(f[i], f[j]);\n                    if (c) return c;\n                }\n            });\n        } else {\n            f = values.map(f);\n            index.sort((i, j)=>ascendingDefined(f[i], f[j]));\n        }\n        return (0,_permute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, index);\n    }\n    return values.sort(compareDefined(f));\n}\nfunction compareDefined(compare = _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n    if (compare === _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) return ascendingDefined;\n    if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n    return (a, b)=>{\n        const x = compare(a, b);\n        if (x || x === 0) return x;\n        return (compare(b, b) === 0) - (compare(a, a) === 0);\n    };\n}\nfunction ascendingDefined(a, b) {\n    return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/sort.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/subset.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-array/src/subset.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ subset)\n/* harmony export */ });\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/../../node_modules/d3-array/src/superset.js\");\n\nfunction subset(values, other) {\n    return (0,_superset_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(other, values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zdWJzZXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFdEIsU0FBU0MsT0FBT0MsTUFBTSxFQUFFQyxLQUFLO0lBQzFDLE9BQU9ILHdEQUFRQSxDQUFDRyxPQUFPRDtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvc3Vic2V0LmpzPzM2NzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHN1cGVyc2V0IGZyb20gXCIuL3N1cGVyc2V0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHN1YnNldCh2YWx1ZXMsIG90aGVyKSB7XG4gIHJldHVybiBzdXBlcnNldChvdGhlciwgdmFsdWVzKTtcbn1cbiJdLCJuYW1lcyI6WyJzdXBlcnNldCIsInN1YnNldCIsInZhbHVlcyIsIm90aGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/subset.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/sum.js":
/*!**********************************************!*\
  !*** ../../node_modules/d3-array/src/sum.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ sum)\n/* harmony export */ });\nfunction sum(values, valueof) {\n    let sum = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value = +value) {\n                sum += value;\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if (value = +valueof(value, ++index, values)) {\n                sum += value;\n            }\n        }\n    }\n    return sum;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zdW0uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLElBQUlDLE1BQU0sRUFBRUMsT0FBTztJQUN6QyxJQUFJRixNQUFNO0lBQ1YsSUFBSUUsWUFBWUMsV0FBVztRQUN6QixLQUFLLElBQUlDLFNBQVNILE9BQVE7WUFDeEIsSUFBSUcsUUFBUSxDQUFDQSxPQUFPO2dCQUNsQkosT0FBT0k7WUFDVDtRQUNGO0lBQ0YsT0FBTztRQUNMLElBQUlDLFFBQVEsQ0FBQztRQUNiLEtBQUssSUFBSUQsU0FBU0gsT0FBUTtZQUN4QixJQUFJRyxRQUFRLENBQUNGLFFBQVFFLE9BQU8sRUFBRUMsT0FBT0osU0FBUztnQkFDNUNELE9BQU9JO1lBQ1Q7UUFDRjtJQUNGO0lBQ0EsT0FBT0o7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvc3VtLmpzP2M2NWMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc3VtKHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgc3VtID0gMDtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlID0gK3ZhbHVlKSB7XG4gICAgICAgIHN1bSArPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgPSArdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkge1xuICAgICAgICBzdW0gKz0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBzdW07XG59XG4iXSwibmFtZXMiOlsic3VtIiwidmFsdWVzIiwidmFsdWVvZiIsInVuZGVmaW5lZCIsInZhbHVlIiwiaW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/sum.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/superset.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/superset.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ superset)\n/* harmony export */ });\nfunction superset(values, other) {\n    const iterator = values[Symbol.iterator](), set = new Set();\n    for (const o of other){\n        const io = intern(o);\n        if (set.has(io)) continue;\n        let value, done;\n        while({ value, done } = iterator.next()){\n            if (done) return false;\n            const ivalue = intern(value);\n            set.add(ivalue);\n            if (Object.is(io, ivalue)) break;\n        }\n    }\n    return true;\n}\nfunction intern(value) {\n    return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zdXBlcnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsU0FBU0MsTUFBTSxFQUFFQyxLQUFLO0lBQzVDLE1BQU1DLFdBQVdGLE1BQU0sQ0FBQ0csT0FBT0QsUUFBUSxDQUFDLElBQUlFLE1BQU0sSUFBSUM7SUFDdEQsS0FBSyxNQUFNQyxLQUFLTCxNQUFPO1FBQ3JCLE1BQU1NLEtBQUtDLE9BQU9GO1FBQ2xCLElBQUlGLElBQUlLLEdBQUcsQ0FBQ0YsS0FBSztRQUNqQixJQUFJRyxPQUFPQztRQUNYLE1BQVEsRUFBQ0QsS0FBSyxFQUFFQyxJQUFJLEVBQUMsR0FBR1QsU0FBU1UsSUFBSSxHQUFLO1lBQ3hDLElBQUlELE1BQU0sT0FBTztZQUNqQixNQUFNRSxTQUFTTCxPQUFPRTtZQUN0Qk4sSUFBSVUsR0FBRyxDQUFDRDtZQUNSLElBQUlFLE9BQU9DLEVBQUUsQ0FBQ1QsSUFBSU0sU0FBUztRQUM3QjtJQUNGO0lBQ0EsT0FBTztBQUNUO0FBRUEsU0FBU0wsT0FBT0UsS0FBSztJQUNuQixPQUFPQSxVQUFVLFFBQVEsT0FBT0EsVUFBVSxXQUFXQSxNQUFNTyxPQUFPLEtBQUtQO0FBQ3pFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zdXBlcnNldC5qcz80NzNiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHN1cGVyc2V0KHZhbHVlcywgb3RoZXIpIHtcbiAgY29uc3QgaXRlcmF0b3IgPSB2YWx1ZXNbU3ltYm9sLml0ZXJhdG9yXSgpLCBzZXQgPSBuZXcgU2V0KCk7XG4gIGZvciAoY29uc3QgbyBvZiBvdGhlcikge1xuICAgIGNvbnN0IGlvID0gaW50ZXJuKG8pO1xuICAgIGlmIChzZXQuaGFzKGlvKSkgY29udGludWU7XG4gICAgbGV0IHZhbHVlLCBkb25lO1xuICAgIHdoaWxlICgoe3ZhbHVlLCBkb25lfSA9IGl0ZXJhdG9yLm5leHQoKSkpIHtcbiAgICAgIGlmIChkb25lKSByZXR1cm4gZmFsc2U7XG4gICAgICBjb25zdCBpdmFsdWUgPSBpbnRlcm4odmFsdWUpO1xuICAgICAgc2V0LmFkZChpdmFsdWUpO1xuICAgICAgaWYgKE9iamVjdC5pcyhpbywgaXZhbHVlKSkgYnJlYWs7XG4gICAgfVxuICB9XG4gIHJldHVybiB0cnVlO1xufVxuXG5mdW5jdGlvbiBpbnRlcm4odmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlICE9PSBudWxsICYmIHR5cGVvZiB2YWx1ZSA9PT0gXCJvYmplY3RcIiA/IHZhbHVlLnZhbHVlT2YoKSA6IHZhbHVlO1xufVxuIl0sIm5hbWVzIjpbInN1cGVyc2V0IiwidmFsdWVzIiwib3RoZXIiLCJpdGVyYXRvciIsIlN5bWJvbCIsInNldCIsIlNldCIsIm8iLCJpbyIsImludGVybiIsImhhcyIsInZhbHVlIiwiZG9uZSIsIm5leHQiLCJpdmFsdWUiLCJhZGQiLCJPYmplY3QiLCJpcyIsInZhbHVlT2YiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/superset.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/threshold/freedmanDiaconis.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/d3-array/src/threshold/freedmanDiaconis.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdFreedmanDiaconis)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/../../node_modules/d3-array/src/count.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../quantile.js */ \"(ssr)/../../node_modules/d3-array/src/quantile.js\");\n\n\nfunction thresholdFreedmanDiaconis(values, min, max) {\n    const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.75) - (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.25);\n    return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy90aHJlc2hvbGQvZnJlZWRtYW5EaWFjb25pcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFDTTtBQUV2QixTQUFTRSwwQkFBMEJDLE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxHQUFHO0lBQ2hFLE1BQU1DLElBQUlOLHFEQUFLQSxDQUFDRyxTQUFTSSxJQUFJTix3REFBUUEsQ0FBQ0UsUUFBUSxRQUFRRix3REFBUUEsQ0FBQ0UsUUFBUTtJQUN2RSxPQUFPRyxLQUFLQyxJQUFJQyxLQUFLQyxJQUFJLENBQUMsQ0FBQ0osTUFBTUQsR0FBRSxJQUFNLEtBQUlHLElBQUlDLEtBQUtFLEdBQUcsQ0FBQ0osR0FBRyxDQUFDLElBQUksRUFBQyxLQUFNO0FBQzNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy90aHJlc2hvbGQvZnJlZWRtYW5EaWFjb25pcy5qcz9iMzAxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjb3VudCBmcm9tIFwiLi4vY291bnQuanNcIjtcbmltcG9ydCBxdWFudGlsZSBmcm9tIFwiLi4vcXVhbnRpbGUuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdGhyZXNob2xkRnJlZWRtYW5EaWFjb25pcyh2YWx1ZXMsIG1pbiwgbWF4KSB7XG4gIGNvbnN0IGMgPSBjb3VudCh2YWx1ZXMpLCBkID0gcXVhbnRpbGUodmFsdWVzLCAwLjc1KSAtIHF1YW50aWxlKHZhbHVlcywgMC4yNSk7XG4gIHJldHVybiBjICYmIGQgPyBNYXRoLmNlaWwoKG1heCAtIG1pbikgLyAoMiAqIGQgKiBNYXRoLnBvdyhjLCAtMSAvIDMpKSkgOiAxO1xufVxuIl0sIm5hbWVzIjpbImNvdW50IiwicXVhbnRpbGUiLCJ0aHJlc2hvbGRGcmVlZG1hbkRpYWNvbmlzIiwidmFsdWVzIiwibWluIiwibWF4IiwiYyIsImQiLCJNYXRoIiwiY2VpbCIsInBvdyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/threshold/freedmanDiaconis.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/threshold/scott.js":
/*!**********************************************************!*\
  !*** ../../node_modules/d3-array/src/threshold/scott.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdScott)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/../../node_modules/d3-array/src/count.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../deviation.js */ \"(ssr)/../../node_modules/d3-array/src/deviation.js\");\n\n\nfunction thresholdScott(values, min, max) {\n    const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_deviation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n    return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy90aHJlc2hvbGQvc2NvdHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBQ1E7QUFFekIsU0FBU0UsZUFBZUMsTUFBTSxFQUFFQyxHQUFHLEVBQUVDLEdBQUc7SUFDckQsTUFBTUMsSUFBSU4scURBQUtBLENBQUNHLFNBQVNJLElBQUlOLHlEQUFTQSxDQUFDRTtJQUN2QyxPQUFPRyxLQUFLQyxJQUFJQyxLQUFLQyxJQUFJLENBQUMsQ0FBQ0osTUFBTUQsR0FBRSxJQUFLSSxLQUFLRSxJQUFJLENBQUNKLEtBQU0sUUFBT0MsQ0FBQUEsS0FBTTtBQUN2RSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdGhyZXNob2xkL3Njb3R0LmpzPzhkZjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvdW50IGZyb20gXCIuLi9jb3VudC5qc1wiO1xuaW1wb3J0IGRldmlhdGlvbiBmcm9tIFwiLi4vZGV2aWF0aW9uLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRocmVzaG9sZFNjb3R0KHZhbHVlcywgbWluLCBtYXgpIHtcbiAgY29uc3QgYyA9IGNvdW50KHZhbHVlcyksIGQgPSBkZXZpYXRpb24odmFsdWVzKTtcbiAgcmV0dXJuIGMgJiYgZCA/IE1hdGguY2VpbCgobWF4IC0gbWluKSAqIE1hdGguY2JydChjKSAvICgzLjQ5ICogZCkpIDogMTtcbn1cbiJdLCJuYW1lcyI6WyJjb3VudCIsImRldmlhdGlvbiIsInRocmVzaG9sZFNjb3R0IiwidmFsdWVzIiwibWluIiwibWF4IiwiYyIsImQiLCJNYXRoIiwiY2VpbCIsImNicnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/threshold/scott.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/threshold/sturges.js":
/*!************************************************************!*\
  !*** ../../node_modules/d3-array/src/threshold/sturges.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdSturges)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/../../node_modules/d3-array/src/count.js\");\n\nfunction thresholdSturges(values) {\n    return Math.max(1, Math.ceil(Math.log((0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values)) / Math.LN2) + 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy90aHJlc2hvbGQvc3R1cmdlcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQztBQUVqQixTQUFTQyxpQkFBaUJDLE1BQU07SUFDN0MsT0FBT0MsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLElBQUksQ0FBQ0YsS0FBS0csR0FBRyxDQUFDTixxREFBS0EsQ0FBQ0UsV0FBV0MsS0FBS0ksR0FBRyxJQUFJO0FBQ3JFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy90aHJlc2hvbGQvc3R1cmdlcy5qcz80YmNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjb3VudCBmcm9tIFwiLi4vY291bnQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdGhyZXNob2xkU3R1cmdlcyh2YWx1ZXMpIHtcbiAgcmV0dXJuIE1hdGgubWF4KDEsIE1hdGguY2VpbChNYXRoLmxvZyhjb3VudCh2YWx1ZXMpKSAvIE1hdGguTE4yKSArIDEpO1xufVxuIl0sIm5hbWVzIjpbImNvdW50IiwidGhyZXNob2xkU3R1cmdlcyIsInZhbHVlcyIsIk1hdGgiLCJtYXgiLCJjZWlsIiwibG9nIiwiTE4yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/threshold/sturges.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/ticks.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/ticks.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ticks),\n/* harmony export */   tickIncrement: () => (/* binding */ tickIncrement),\n/* harmony export */   tickStep: () => (/* binding */ tickStep)\n/* harmony export */ });\nconst e10 = Math.sqrt(50), e5 = Math.sqrt(10), e2 = Math.sqrt(2);\nfunction tickSpec(start, stop, count) {\n    const step = (stop - start) / Math.max(0, count), power = Math.floor(Math.log10(step)), error = step / Math.pow(10, power), factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n    let i1, i2, inc;\n    if (power < 0) {\n        inc = Math.pow(10, -power) / factor;\n        i1 = Math.round(start * inc);\n        i2 = Math.round(stop * inc);\n        if (i1 / inc < start) ++i1;\n        if (i2 / inc > stop) --i2;\n        inc = -inc;\n    } else {\n        inc = Math.pow(10, power) * factor;\n        i1 = Math.round(start / inc);\n        i2 = Math.round(stop / inc);\n        if (i1 * inc < start) ++i1;\n        if (i2 * inc > stop) --i2;\n    }\n    if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n    return [\n        i1,\n        i2,\n        inc\n    ];\n}\nfunction ticks(start, stop, count) {\n    stop = +stop, start = +start, count = +count;\n    if (!(count > 0)) return [];\n    if (start === stop) return [\n        start\n    ];\n    const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n    if (!(i2 >= i1)) return [];\n    const n = i2 - i1 + 1, ticks = new Array(n);\n    if (reverse) {\n        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) / -inc;\n        else for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) * inc;\n    } else {\n        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) / -inc;\n        else for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) * inc;\n    }\n    return ticks;\n}\nfunction tickIncrement(start, stop, count) {\n    stop = +stop, start = +start, count = +count;\n    return tickSpec(start, stop, count)[2];\n}\nfunction tickStep(start, stop, count) {\n    stop = +stop, start = +start, count = +count;\n    const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n    return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy90aWNrcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxNQUFNQSxNQUFNQyxLQUFLQyxJQUFJLENBQUMsS0FDbEJDLEtBQUtGLEtBQUtDLElBQUksQ0FBQyxLQUNmRSxLQUFLSCxLQUFLQyxJQUFJLENBQUM7QUFFbkIsU0FBU0csU0FBU0MsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLEtBQUs7SUFDbEMsTUFBTUMsT0FBTyxDQUFDRixPQUFPRCxLQUFJLElBQUtMLEtBQUtTLEdBQUcsQ0FBQyxHQUFHRixRQUN0Q0csUUFBUVYsS0FBS1csS0FBSyxDQUFDWCxLQUFLWSxLQUFLLENBQUNKLFFBQzlCSyxRQUFRTCxPQUFPUixLQUFLYyxHQUFHLENBQUMsSUFBSUosUUFDNUJLLFNBQVNGLFNBQVNkLE1BQU0sS0FBS2MsU0FBU1gsS0FBSyxJQUFJVyxTQUFTVixLQUFLLElBQUk7SUFDckUsSUFBSWEsSUFBSUMsSUFBSUM7SUFDWixJQUFJUixRQUFRLEdBQUc7UUFDYlEsTUFBTWxCLEtBQUtjLEdBQUcsQ0FBQyxJQUFJLENBQUNKLFNBQVNLO1FBQzdCQyxLQUFLaEIsS0FBS21CLEtBQUssQ0FBQ2QsUUFBUWE7UUFDeEJELEtBQUtqQixLQUFLbUIsS0FBSyxDQUFDYixPQUFPWTtRQUN2QixJQUFJRixLQUFLRSxNQUFNYixPQUFPLEVBQUVXO1FBQ3hCLElBQUlDLEtBQUtDLE1BQU1aLE1BQU0sRUFBRVc7UUFDdkJDLE1BQU0sQ0FBQ0E7SUFDVCxPQUFPO1FBQ0xBLE1BQU1sQixLQUFLYyxHQUFHLENBQUMsSUFBSUosU0FBU0s7UUFDNUJDLEtBQUtoQixLQUFLbUIsS0FBSyxDQUFDZCxRQUFRYTtRQUN4QkQsS0FBS2pCLEtBQUttQixLQUFLLENBQUNiLE9BQU9ZO1FBQ3ZCLElBQUlGLEtBQUtFLE1BQU1iLE9BQU8sRUFBRVc7UUFDeEIsSUFBSUMsS0FBS0MsTUFBTVosTUFBTSxFQUFFVztJQUN6QjtJQUNBLElBQUlBLEtBQUtELE1BQU0sT0FBT1QsU0FBU0EsUUFBUSxHQUFHLE9BQU9ILFNBQVNDLE9BQU9DLE1BQU1DLFFBQVE7SUFDL0UsT0FBTztRQUFDUztRQUFJQztRQUFJQztLQUFJO0FBQ3RCO0FBRWUsU0FBU0UsTUFBTWYsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLEtBQUs7SUFDOUNELE9BQU8sQ0FBQ0EsTUFBTUQsUUFBUSxDQUFDQSxPQUFPRSxRQUFRLENBQUNBO0lBQ3ZDLElBQUksQ0FBRUEsQ0FBQUEsUUFBUSxJQUFJLE9BQU8sRUFBRTtJQUMzQixJQUFJRixVQUFVQyxNQUFNLE9BQU87UUFBQ0Q7S0FBTTtJQUNsQyxNQUFNZ0IsVUFBVWYsT0FBT0QsT0FBTyxDQUFDVyxJQUFJQyxJQUFJQyxJQUFJLEdBQUdHLFVBQVVqQixTQUFTRSxNQUFNRCxPQUFPRSxTQUFTSCxTQUFTQyxPQUFPQyxNQUFNQztJQUM3RyxJQUFJLENBQUVVLENBQUFBLE1BQU1ELEVBQUMsR0FBSSxPQUFPLEVBQUU7SUFDMUIsTUFBTU0sSUFBSUwsS0FBS0QsS0FBSyxHQUFHSSxRQUFRLElBQUlHLE1BQU1EO0lBQ3pDLElBQUlELFNBQVM7UUFDWCxJQUFJSCxNQUFNLEdBQUcsSUFBSyxJQUFJTSxJQUFJLEdBQUdBLElBQUlGLEdBQUcsRUFBRUUsRUFBR0osS0FBSyxDQUFDSSxFQUFFLEdBQUcsQ0FBQ1AsS0FBS08sQ0FBQUEsSUFBSyxDQUFDTjthQUMzRCxJQUFLLElBQUlNLElBQUksR0FBR0EsSUFBSUYsR0FBRyxFQUFFRSxFQUFHSixLQUFLLENBQUNJLEVBQUUsR0FBRyxDQUFDUCxLQUFLTyxDQUFBQSxJQUFLTjtJQUN6RCxPQUFPO1FBQ0wsSUFBSUEsTUFBTSxHQUFHLElBQUssSUFBSU0sSUFBSSxHQUFHQSxJQUFJRixHQUFHLEVBQUVFLEVBQUdKLEtBQUssQ0FBQ0ksRUFBRSxHQUFHLENBQUNSLEtBQUtRLENBQUFBLElBQUssQ0FBQ047YUFDM0QsSUFBSyxJQUFJTSxJQUFJLEdBQUdBLElBQUlGLEdBQUcsRUFBRUUsRUFBR0osS0FBSyxDQUFDSSxFQUFFLEdBQUcsQ0FBQ1IsS0FBS1EsQ0FBQUEsSUFBS047SUFDekQ7SUFDQSxPQUFPRTtBQUNUO0FBRU8sU0FBU0ssY0FBY3BCLEtBQUssRUFBRUMsSUFBSSxFQUFFQyxLQUFLO0lBQzlDRCxPQUFPLENBQUNBLE1BQU1ELFFBQVEsQ0FBQ0EsT0FBT0UsUUFBUSxDQUFDQTtJQUN2QyxPQUFPSCxTQUFTQyxPQUFPQyxNQUFNQyxNQUFNLENBQUMsRUFBRTtBQUN4QztBQUVPLFNBQVNtQixTQUFTckIsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLEtBQUs7SUFDekNELE9BQU8sQ0FBQ0EsTUFBTUQsUUFBUSxDQUFDQSxPQUFPRSxRQUFRLENBQUNBO0lBQ3ZDLE1BQU1jLFVBQVVmLE9BQU9ELE9BQU9hLE1BQU1HLFVBQVVJLGNBQWNuQixNQUFNRCxPQUFPRSxTQUFTa0IsY0FBY3BCLE9BQU9DLE1BQU1DO0lBQzdHLE9BQU8sQ0FBQ2MsVUFBVSxDQUFDLElBQUksS0FBTUgsQ0FBQUEsTUFBTSxJQUFJLElBQUksQ0FBQ0EsTUFBTUEsR0FBRTtBQUN0RCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdGlja3MuanM/ZjNiMCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBlMTAgPSBNYXRoLnNxcnQoNTApLFxuICAgIGU1ID0gTWF0aC5zcXJ0KDEwKSxcbiAgICBlMiA9IE1hdGguc3FydCgyKTtcblxuZnVuY3Rpb24gdGlja1NwZWMoc3RhcnQsIHN0b3AsIGNvdW50KSB7XG4gIGNvbnN0IHN0ZXAgPSAoc3RvcCAtIHN0YXJ0KSAvIE1hdGgubWF4KDAsIGNvdW50KSxcbiAgICAgIHBvd2VyID0gTWF0aC5mbG9vcihNYXRoLmxvZzEwKHN0ZXApKSxcbiAgICAgIGVycm9yID0gc3RlcCAvIE1hdGgucG93KDEwLCBwb3dlciksXG4gICAgICBmYWN0b3IgPSBlcnJvciA+PSBlMTAgPyAxMCA6IGVycm9yID49IGU1ID8gNSA6IGVycm9yID49IGUyID8gMiA6IDE7XG4gIGxldCBpMSwgaTIsIGluYztcbiAgaWYgKHBvd2VyIDwgMCkge1xuICAgIGluYyA9IE1hdGgucG93KDEwLCAtcG93ZXIpIC8gZmFjdG9yO1xuICAgIGkxID0gTWF0aC5yb3VuZChzdGFydCAqIGluYyk7XG4gICAgaTIgPSBNYXRoLnJvdW5kKHN0b3AgKiBpbmMpO1xuICAgIGlmIChpMSAvIGluYyA8IHN0YXJ0KSArK2kxO1xuICAgIGlmIChpMiAvIGluYyA+IHN0b3ApIC0taTI7XG4gICAgaW5jID0gLWluYztcbiAgfSBlbHNlIHtcbiAgICBpbmMgPSBNYXRoLnBvdygxMCwgcG93ZXIpICogZmFjdG9yO1xuICAgIGkxID0gTWF0aC5yb3VuZChzdGFydCAvIGluYyk7XG4gICAgaTIgPSBNYXRoLnJvdW5kKHN0b3AgLyBpbmMpO1xuICAgIGlmIChpMSAqIGluYyA8IHN0YXJ0KSArK2kxO1xuICAgIGlmIChpMiAqIGluYyA+IHN0b3ApIC0taTI7XG4gIH1cbiAgaWYgKGkyIDwgaTEgJiYgMC41IDw9IGNvdW50ICYmIGNvdW50IDwgMikgcmV0dXJuIHRpY2tTcGVjKHN0YXJ0LCBzdG9wLCBjb3VudCAqIDIpO1xuICByZXR1cm4gW2kxLCBpMiwgaW5jXTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdGlja3Moc3RhcnQsIHN0b3AsIGNvdW50KSB7XG4gIHN0b3AgPSArc3RvcCwgc3RhcnQgPSArc3RhcnQsIGNvdW50ID0gK2NvdW50O1xuICBpZiAoIShjb3VudCA+IDApKSByZXR1cm4gW107XG4gIGlmIChzdGFydCA9PT0gc3RvcCkgcmV0dXJuIFtzdGFydF07XG4gIGNvbnN0IHJldmVyc2UgPSBzdG9wIDwgc3RhcnQsIFtpMSwgaTIsIGluY10gPSByZXZlcnNlID8gdGlja1NwZWMoc3RvcCwgc3RhcnQsIGNvdW50KSA6IHRpY2tTcGVjKHN0YXJ0LCBzdG9wLCBjb3VudCk7XG4gIGlmICghKGkyID49IGkxKSkgcmV0dXJuIFtdO1xuICBjb25zdCBuID0gaTIgLSBpMSArIDEsIHRpY2tzID0gbmV3IEFycmF5KG4pO1xuICBpZiAocmV2ZXJzZSkge1xuICAgIGlmIChpbmMgPCAwKSBmb3IgKGxldCBpID0gMDsgaSA8IG47ICsraSkgdGlja3NbaV0gPSAoaTIgLSBpKSAvIC1pbmM7XG4gICAgZWxzZSBmb3IgKGxldCBpID0gMDsgaSA8IG47ICsraSkgdGlja3NbaV0gPSAoaTIgLSBpKSAqIGluYztcbiAgfSBlbHNlIHtcbiAgICBpZiAoaW5jIDwgMCkgZm9yIChsZXQgaSA9IDA7IGkgPCBuOyArK2kpIHRpY2tzW2ldID0gKGkxICsgaSkgLyAtaW5jO1xuICAgIGVsc2UgZm9yIChsZXQgaSA9IDA7IGkgPCBuOyArK2kpIHRpY2tzW2ldID0gKGkxICsgaSkgKiBpbmM7XG4gIH1cbiAgcmV0dXJuIHRpY2tzO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdGlja0luY3JlbWVudChzdGFydCwgc3RvcCwgY291bnQpIHtcbiAgc3RvcCA9ICtzdG9wLCBzdGFydCA9ICtzdGFydCwgY291bnQgPSArY291bnQ7XG4gIHJldHVybiB0aWNrU3BlYyhzdGFydCwgc3RvcCwgY291bnQpWzJdO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdGlja1N0ZXAoc3RhcnQsIHN0b3AsIGNvdW50KSB7XG4gIHN0b3AgPSArc3RvcCwgc3RhcnQgPSArc3RhcnQsIGNvdW50ID0gK2NvdW50O1xuICBjb25zdCByZXZlcnNlID0gc3RvcCA8IHN0YXJ0LCBpbmMgPSByZXZlcnNlID8gdGlja0luY3JlbWVudChzdG9wLCBzdGFydCwgY291bnQpIDogdGlja0luY3JlbWVudChzdGFydCwgc3RvcCwgY291bnQpO1xuICByZXR1cm4gKHJldmVyc2UgPyAtMSA6IDEpICogKGluYyA8IDAgPyAxIC8gLWluYyA6IGluYyk7XG59XG4iXSwibmFtZXMiOlsiZTEwIiwiTWF0aCIsInNxcnQiLCJlNSIsImUyIiwidGlja1NwZWMiLCJzdGFydCIsInN0b3AiLCJjb3VudCIsInN0ZXAiLCJtYXgiLCJwb3dlciIsImZsb29yIiwibG9nMTAiLCJlcnJvciIsInBvdyIsImZhY3RvciIsImkxIiwiaTIiLCJpbmMiLCJyb3VuZCIsInRpY2tzIiwicmV2ZXJzZSIsIm4iLCJBcnJheSIsImkiLCJ0aWNrSW5jcmVtZW50IiwidGlja1N0ZXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/ticks.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/transpose.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-array/src/transpose.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ transpose)\n/* harmony export */ });\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./min.js */ \"(ssr)/../../node_modules/d3-array/src/min.js\");\n\nfunction transpose(matrix) {\n    if (!(n = matrix.length)) return [];\n    for(var i = -1, m = (0,_min_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(matrix, length), transpose = new Array(m); ++i < m;){\n        for(var j = -1, n, row = transpose[i] = new Array(n); ++j < n;){\n            row[j] = matrix[j][i];\n        }\n    }\n    return transpose;\n}\nfunction length(d) {\n    return d.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy90cmFuc3Bvc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkI7QUFFWixTQUFTQyxVQUFVQyxNQUFNO0lBQ3RDLElBQUksQ0FBRUMsQ0FBQUEsSUFBSUQsT0FBT0UsTUFBTSxHQUFHLE9BQU8sRUFBRTtJQUNuQyxJQUFLLElBQUlDLElBQUksQ0FBQyxHQUFHQyxJQUFJTixtREFBR0EsQ0FBQ0UsUUFBUUUsU0FBU0gsWUFBWSxJQUFJTSxNQUFNRCxJQUFJLEVBQUVELElBQUlDLEdBQUk7UUFDNUUsSUFBSyxJQUFJRSxJQUFJLENBQUMsR0FBR0wsR0FBR00sTUFBTVIsU0FBUyxDQUFDSSxFQUFFLEdBQUcsSUFBSUUsTUFBTUosSUFBSSxFQUFFSyxJQUFJTCxHQUFJO1lBQy9ETSxHQUFHLENBQUNELEVBQUUsR0FBR04sTUFBTSxDQUFDTSxFQUFFLENBQUNILEVBQUU7UUFDdkI7SUFDRjtJQUNBLE9BQU9KO0FBQ1Q7QUFFQSxTQUFTRyxPQUFPTSxDQUFDO0lBQ2YsT0FBT0EsRUFBRU4sTUFBTTtBQUNqQiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdHJhbnNwb3NlLmpzPzY1YWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1pbiBmcm9tIFwiLi9taW4uanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdHJhbnNwb3NlKG1hdHJpeCkge1xuICBpZiAoIShuID0gbWF0cml4Lmxlbmd0aCkpIHJldHVybiBbXTtcbiAgZm9yICh2YXIgaSA9IC0xLCBtID0gbWluKG1hdHJpeCwgbGVuZ3RoKSwgdHJhbnNwb3NlID0gbmV3IEFycmF5KG0pOyArK2kgPCBtOykge1xuICAgIGZvciAodmFyIGogPSAtMSwgbiwgcm93ID0gdHJhbnNwb3NlW2ldID0gbmV3IEFycmF5KG4pOyArK2ogPCBuOykge1xuICAgICAgcm93W2pdID0gbWF0cml4W2pdW2ldO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJhbnNwb3NlO1xufVxuXG5mdW5jdGlvbiBsZW5ndGgoZCkge1xuICByZXR1cm4gZC5sZW5ndGg7XG59XG4iXSwibmFtZXMiOlsibWluIiwidHJhbnNwb3NlIiwibWF0cml4IiwibiIsImxlbmd0aCIsImkiLCJtIiwiQXJyYXkiLCJqIiwicm93IiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/transpose.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/union.js":
/*!************************************************!*\
  !*** ../../node_modules/d3-array/src/union.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ union)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/../../node_modules/internmap/src/index.js\");\n\nfunction union(...others) {\n    const set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n    for (const other of others){\n        for (const o of other){\n            set.add(o);\n        }\n    }\n    return set;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy91bmlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQztBQUVyQixTQUFTQyxNQUFNLEdBQUdDLE1BQU07SUFDckMsTUFBTUMsTUFBTSxJQUFJSCxnREFBU0E7SUFDekIsS0FBSyxNQUFNSSxTQUFTRixPQUFRO1FBQzFCLEtBQUssTUFBTUcsS0FBS0QsTUFBTztZQUNyQkQsSUFBSUcsR0FBRyxDQUFDRDtRQUNWO0lBQ0Y7SUFDQSxPQUFPRjtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy91bmlvbi5qcz9iMmVlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7SW50ZXJuU2V0fSBmcm9tIFwiaW50ZXJubWFwXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVuaW9uKC4uLm90aGVycykge1xuICBjb25zdCBzZXQgPSBuZXcgSW50ZXJuU2V0KCk7XG4gIGZvciAoY29uc3Qgb3RoZXIgb2Ygb3RoZXJzKSB7XG4gICAgZm9yIChjb25zdCBvIG9mIG90aGVyKSB7XG4gICAgICBzZXQuYWRkKG8pO1xuICAgIH1cbiAgfVxuICByZXR1cm4gc2V0O1xufVxuIl0sIm5hbWVzIjpbIkludGVyblNldCIsInVuaW9uIiwib3RoZXJzIiwic2V0Iiwib3RoZXIiLCJvIiwiYWRkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/union.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/variance.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-array/src/variance.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ variance)\n/* harmony export */ });\nfunction variance(values, valueof) {\n    let count = 0;\n    let delta;\n    let mean = 0;\n    let sum = 0;\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value != null && (value = +value) >= value) {\n                delta = value - mean;\n                mean += delta / ++count;\n                sum += delta * (value - mean);\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n                delta = value - mean;\n                mean += delta / ++count;\n                sum += delta * (value - mean);\n            }\n        }\n    }\n    if (count > 1) return sum / (count - 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/variance.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-array/src/zip.js":
/*!**********************************************!*\
  !*** ../../node_modules/d3-array/src/zip.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ zip)\n/* harmony export */ });\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/../../node_modules/d3-array/src/transpose.js\");\n\nfunction zip() {\n    return (0,_transpose_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy96aXAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7QUFFeEIsU0FBU0M7SUFDdEIsT0FBT0QseURBQVNBLENBQUNFO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy96aXAuanM/NjMzNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHJhbnNwb3NlIGZyb20gXCIuL3RyYW5zcG9zZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB6aXAoKSB7XG4gIHJldHVybiB0cmFuc3Bvc2UoYXJndW1lbnRzKTtcbn1cbiJdLCJuYW1lcyI6WyJ0cmFuc3Bvc2UiLCJ6aXAiLCJhcmd1bWVudHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-array/src/zip.js\n");

/***/ })

};
;