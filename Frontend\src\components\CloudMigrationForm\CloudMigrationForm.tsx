'use client';

import React, { useState } from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import useForm from '@hooks/useForm';
import Button from '@components/Button';
import Heading from '@components/Heading';
import classNames from '@utils/classNames';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import getUserLocation from '@utils/getUserLocation';
import styles from './CloudMigrationForm.module.css';

export default function CloudMigrationForm({
  formData,
  handleResult,
  handleVisibleSection,
}: any) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const userCountryCode = getUserLocation();

  const initialValues = {
    firstName: '',
    lastName: '',
    emailAddress: '',
    companyName: '',
    phoneNumber: '',
    howCanWeHelpYou: '',
    consent: false,
  };

  const initialErrors = {
    firstName: { empty: false, invalid: false },
    lastName: { empty: false, invalid: false },
    emailAddress: { empty: false, invalid: false },
    companyName: { empty: false, invalid: false },
    phoneNumber: { empty: false, invalid: false },
    howCanWeHelpYou: { empty: false, invalid: false },
    consent: { empty: false, invalid: false },
  };

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleSubmitCloudMigration,
  } = useForm(
    initialValues,
    initialErrors,
    'cloudMigration',
    'Cloud Migration Cost Calculator',
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Calculate the migration cost first
      const calculationResult = handleResult();

      if (calculationResult) {
        // Use the new cloud migration specific form handler
        await handleSubmitCloudMigration(
          calculationResult.data,
          calculationResult.newResult,
          handleVisibleSection,
        );
      }
    } catch (error) {
      console.error('Error in form submission:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container fluid>
      <div className={styles.formWrapper}>
        <Heading
          title="Contact Details"
          headingType="h2"
          className={styles.heading}
        />
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.formFields}>
            <div className={styles.personalDetailsWrapper}>
              <div className={classNames(styles.row, styles.firstRow)}>
                <div className={classNames(styles.row, styles.nameFields)}>
                  <div className={classNames(styles.nameAndInputWrapper)}>
                    <label
                      htmlFor="firstName"
                      className={
                        errors.firstName.empty
                          ? styles.errorLabel
                          : styles.formLabel
                      }
                    >
                      {formData?.formFields?.fieldNameFor_FirstName ||
                        'First Name*'}
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={values.firstName}
                      onChange={e =>
                        handleChange({
                          name: e.target.name,
                          value: e.target.value,
                        })
                      }
                      className={
                        errors.firstName.empty
                          ? `${styles.errorInput} ${styles.formInput}`
                          : `${styles.formInput}`
                      }
                      required
                    />
                  </div>

                  <div className={classNames(styles.nameAndInputWrapper)}>
                    <label
                      htmlFor="lastName"
                      className={
                        errors.lastName.empty
                          ? styles.errorLabel
                          : styles.formLabel
                      }
                    >
                      {formData?.formFields?.fieldNameFor_LastName ||
                        'Last Name*'}
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={values.lastName}
                      onChange={e =>
                        handleChange({
                          name: e.target.name,
                          value: e.target.value,
                        })
                      }
                      className={
                        errors.lastName.empty
                          ? `${styles.errorInput} ${styles.formInput}`
                          : `${styles.formInput}`
                      }
                      required
                    />
                  </div>
                </div>
              </div>

              <div className={styles.row}>
                <div className={styles.nameAndInputWrapper}>
                  <label
                    htmlFor="emailAddress"
                    className={
                      errors.emailAddress.empty || errors.emailAddress.invalid
                        ? styles.errorLabel
                        : styles.formLabel
                    }
                  >
                    {formData?.formFields?.fieldNameFor_EmailAddress ||
                      'Email Address*'}
                  </label>
                  <input
                    type="email"
                    id="emailAddress"
                    name="emailAddress"
                    value={values.emailAddress}
                    onChange={e =>
                      handleChange({
                        name: e.target.name,
                        value: e.target.value,
                      })
                    }
                    className={`${styles.formInput} ${errors.emailAddress.empty || errors.emailAddress.invalid ? styles.errorInput : ''}`}
                    required
                  />
                </div>

                <div className={styles.nameAndInputWrapper}>
                  <label
                    htmlFor="companyName"
                    className={
                      errors.companyName.empty || errors.companyName.invalid
                        ? styles.errorLabel
                        : styles.formLabel
                    }
                  >
                    {formData?.formFields?.fieldNameFor_CompanyName ||
                      'Company Name*'}
                  </label>
                  <input
                    type="text"
                    id="companyName"
                    name="companyName"
                    value={values.companyName}
                    onChange={e =>
                      handleChange({
                        name: e.target.name,
                        value: e.target.value,
                      })
                    }
                    className={`${styles.formInput} ${errors.companyName.empty || errors.companyName.invalid ? styles.errorInput : ''}`}
                    required
                  />
                </div>
              </div>

              <div className={styles.row}>
                <div className={styles.nameAndInputWrapper}>
                  <label
                    htmlFor="phoneNumber"
                    className={
                      errors.phoneNumber.empty || errors.phoneNumber.invalid
                        ? styles.errorLabel
                        : styles.formLabel
                    }
                  >
                    {formData?.formFields?.fieldNameFor_PhoneNumber ||
                      'Phone Number*'}
                  </label>
                  <div className={styles.phoneInputWrapper}>
                    <PhoneInput
                      country={userCountryCode}
                      value={values.phoneNumber}
                      onChange={phone =>
                        handleChange({
                          name: 'phoneNumber',
                          value: phone,
                        })
                      }
                      inputClass={
                        errors.phoneNumber.empty || errors.phoneNumber.invalid
                          ? `${styles.errorInput} ${styles.formInput}`
                          : `${styles.formInput}`
                      }
                      containerClass={styles.phoneInputContainer}
                      buttonClass={styles.phoneInputButton}
                      dropdownClass={styles.phoneInputDropdown}
                    />
                  </div>
                </div>

                <div className={styles.nameAndInputWrapper}>
                  <label htmlFor="howCanWeHelpYou" className={styles.formLabel}>
                    {formData?.formFields?.fieldNameFor_HowCanWeHelpYou ||
                      'How Can We Help You?'}
                  </label>
                  <input
                    type="text"
                    id="howCanWeHelpYou"
                    name="howCanWeHelpYou"
                    value={values.howCanWeHelpYou}
                    onChange={e =>
                      handleChange({
                        name: e.target.name,
                        value: e.target.value,
                      })
                    }
                    className={styles.formInput}
                  />
                </div>
              </div>

              <div className={`${styles.row} ${styles.submitButtonRow}`}>
                <div className={styles.consentRow}>
                  <div
                    className={
                      errors.consent.empty
                        ? styles.errorLabel_consentText
                        : styles.consentText
                    }
                    onClick={() =>
                      handleChange({
                        name: 'consent',
                        value: !values.consent,
                        type: 'checkbox',
                        checked: !values.consent,
                      })
                    }
                  >
                    <input
                      type="checkbox"
                      name="consent"
                      checked={values.consent}
                      onChange={e =>
                        handleChange({
                          name: e.target.name,
                          value: e.target.checked,
                          type: 'checkbox',
                          checked: e.target.checked,
                        })
                      }
                      required
                    />
                    {formData?.formFields?.consentText ||
                      'I consent to processing of my personal data entered above for Maruti Techlabs to contact me.*'}
                  </div>
                </div>

                {errors.consent.empty && (
                  <div className={styles.errorMessages}>
                    You must consent to processing of your personal data
                  </div>
                )}

                {isSubmitting ? (
                  <div className={styles.container_spinner}>
                    <div className={styles.spinner}></div>
                  </div>
                ) : (
                  <Button
                    type="submit"
                    className={styles.result_button}
                    label="Check my Cloud Migration Cost"
                  />
                )}

                {formData?.LinkedInButton_title && (
                  <a className={styles.linkedInButton} href="#">
                    {formData.LinkedInButton_title}
                    <Image
                      src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/linkedin_c13ca9a536.png`}
                      width={32}
                      height={32}
                      alt="LinkedIn Logo"
                    />
                  </a>
                )}
              </div>
            </div>
          </div>
        </form>
      </div>
    </Container>
  );
}
