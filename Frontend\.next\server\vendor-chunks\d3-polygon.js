"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-polygon";
exports.ids = ["vendor-chunks/d3-polygon"];
exports.modules = {

/***/ "(ssr)/../../node_modules/d3-polygon/src/area.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-polygon/src/area.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n    var i = -1, n = polygon.length, a, b = polygon[n - 1], area = 0;\n    while(++i < n){\n        a = b;\n        b = polygon[i];\n        area += a[1] * b[0] - a[0] * b[1];\n    }\n    return area / 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2FyZWEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxPQUFPO0lBQzdCLElBQUlDLElBQUksQ0FBQyxHQUNMQyxJQUFJRixRQUFRRyxNQUFNLEVBQ2xCQyxHQUNBQyxJQUFJTCxPQUFPLENBQUNFLElBQUksRUFBRSxFQUNsQkksT0FBTztJQUVYLE1BQU8sRUFBRUwsSUFBSUMsRUFBRztRQUNkRSxJQUFJQztRQUNKQSxJQUFJTCxPQUFPLENBQUNDLEVBQUU7UUFDZEssUUFBUUYsQ0FBQyxDQUFDLEVBQUUsR0FBR0MsQ0FBQyxDQUFDLEVBQUUsR0FBR0QsQ0FBQyxDQUFDLEVBQUUsR0FBR0MsQ0FBQyxDQUFDLEVBQUU7SUFDbkM7SUFFQSxPQUFPQyxPQUFPO0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2FyZWEuanM/MGIwNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwb2x5Z29uKSB7XG4gIHZhciBpID0gLTEsXG4gICAgICBuID0gcG9seWdvbi5sZW5ndGgsXG4gICAgICBhLFxuICAgICAgYiA9IHBvbHlnb25bbiAtIDFdLFxuICAgICAgYXJlYSA9IDA7XG5cbiAgd2hpbGUgKCsraSA8IG4pIHtcbiAgICBhID0gYjtcbiAgICBiID0gcG9seWdvbltpXTtcbiAgICBhcmVhICs9IGFbMV0gKiBiWzBdIC0gYVswXSAqIGJbMV07XG4gIH1cblxuICByZXR1cm4gYXJlYSAvIDI7XG59XG4iXSwibmFtZXMiOlsicG9seWdvbiIsImkiLCJuIiwibGVuZ3RoIiwiYSIsImIiLCJhcmVhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-polygon/src/area.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-polygon/src/centroid.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-polygon/src/centroid.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n    var i = -1, n = polygon.length, x = 0, y = 0, a, b = polygon[n - 1], c, k = 0;\n    while(++i < n){\n        a = b;\n        b = polygon[i];\n        k += c = a[0] * b[1] - b[0] * a[1];\n        x += (a[0] + b[0]) * c;\n        y += (a[1] + b[1]) * c;\n    }\n    return k *= 3, [\n        x / k,\n        y / k\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2NlbnRyb2lkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsT0FBTztJQUM3QixJQUFJQyxJQUFJLENBQUMsR0FDTEMsSUFBSUYsUUFBUUcsTUFBTSxFQUNsQkMsSUFBSSxHQUNKQyxJQUFJLEdBQ0pDLEdBQ0FDLElBQUlQLE9BQU8sQ0FBQ0UsSUFBSSxFQUFFLEVBQ2xCTSxHQUNBQyxJQUFJO0lBRVIsTUFBTyxFQUFFUixJQUFJQyxFQUFHO1FBQ2RJLElBQUlDO1FBQ0pBLElBQUlQLE9BQU8sQ0FBQ0MsRUFBRTtRQUNkUSxLQUFLRCxJQUFJRixDQUFDLENBQUMsRUFBRSxHQUFHQyxDQUFDLENBQUMsRUFBRSxHQUFHQSxDQUFDLENBQUMsRUFBRSxHQUFHRCxDQUFDLENBQUMsRUFBRTtRQUNsQ0YsS0FBSyxDQUFDRSxDQUFDLENBQUMsRUFBRSxHQUFHQyxDQUFDLENBQUMsRUFBRSxJQUFJQztRQUNyQkgsS0FBSyxDQUFDQyxDQUFDLENBQUMsRUFBRSxHQUFHQyxDQUFDLENBQUMsRUFBRSxJQUFJQztJQUN2QjtJQUVBLE9BQU9DLEtBQUssR0FBRztRQUFDTCxJQUFJSztRQUFHSixJQUFJSTtLQUFFO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2NlbnRyb2lkLmpzPzhlYmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocG9seWdvbikge1xuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IHBvbHlnb24ubGVuZ3RoLFxuICAgICAgeCA9IDAsXG4gICAgICB5ID0gMCxcbiAgICAgIGEsXG4gICAgICBiID0gcG9seWdvbltuIC0gMV0sXG4gICAgICBjLFxuICAgICAgayA9IDA7XG5cbiAgd2hpbGUgKCsraSA8IG4pIHtcbiAgICBhID0gYjtcbiAgICBiID0gcG9seWdvbltpXTtcbiAgICBrICs9IGMgPSBhWzBdICogYlsxXSAtIGJbMF0gKiBhWzFdO1xuICAgIHggKz0gKGFbMF0gKyBiWzBdKSAqIGM7XG4gICAgeSArPSAoYVsxXSArIGJbMV0pICogYztcbiAgfVxuXG4gIHJldHVybiBrICo9IDMsIFt4IC8gaywgeSAvIGtdO1xufVxuIl0sIm5hbWVzIjpbInBvbHlnb24iLCJpIiwibiIsImxlbmd0aCIsIngiLCJ5IiwiYSIsImIiLCJjIiwiayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-polygon/src/centroid.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-polygon/src/contains.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-polygon/src/contains.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon, point) {\n    var n = polygon.length, p = polygon[n - 1], x = point[0], y = point[1], x0 = p[0], y0 = p[1], x1, y1, inside = false;\n    for(var i = 0; i < n; ++i){\n        p = polygon[i], x1 = p[0], y1 = p[1];\n        if (y1 > y !== y0 > y && x < (x0 - x1) * (y - y1) / (y0 - y1) + x1) inside = !inside;\n        x0 = x1, y0 = y1;\n    }\n    return inside;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2NvbnRhaW5zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsT0FBTyxFQUFFQyxLQUFLO0lBQ3BDLElBQUlDLElBQUlGLFFBQVFHLE1BQU0sRUFDbEJDLElBQUlKLE9BQU8sQ0FBQ0UsSUFBSSxFQUFFLEVBQ2xCRyxJQUFJSixLQUFLLENBQUMsRUFBRSxFQUFFSyxJQUFJTCxLQUFLLENBQUMsRUFBRSxFQUMxQk0sS0FBS0gsQ0FBQyxDQUFDLEVBQUUsRUFBRUksS0FBS0osQ0FBQyxDQUFDLEVBQUUsRUFDcEJLLElBQUlDLElBQ0pDLFNBQVM7SUFFYixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSVYsR0FBRyxFQUFFVSxFQUFHO1FBQzFCUixJQUFJSixPQUFPLENBQUNZLEVBQUUsRUFBRUgsS0FBS0wsQ0FBQyxDQUFDLEVBQUUsRUFBRU0sS0FBS04sQ0FBQyxDQUFDLEVBQUU7UUFDcEMsSUFBSSxLQUFPRSxNQUFRRSxLQUFLRixLQUFRRCxJQUFJLENBQUNFLEtBQUtFLEVBQUMsSUFBTUgsQ0FBQUEsSUFBSUksRUFBQyxJQUFNRixDQUFBQSxLQUFLRSxFQUFDLElBQUtELElBQUtFLFNBQVMsQ0FBQ0E7UUFDdEZKLEtBQUtFLElBQUlELEtBQUtFO0lBQ2hCO0lBRUEsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1wb2x5Z29uL3NyYy9jb250YWlucy5qcz9jZmQ3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHBvbHlnb24sIHBvaW50KSB7XG4gIHZhciBuID0gcG9seWdvbi5sZW5ndGgsXG4gICAgICBwID0gcG9seWdvbltuIC0gMV0sXG4gICAgICB4ID0gcG9pbnRbMF0sIHkgPSBwb2ludFsxXSxcbiAgICAgIHgwID0gcFswXSwgeTAgPSBwWzFdLFxuICAgICAgeDEsIHkxLFxuICAgICAgaW5zaWRlID0gZmFsc2U7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBuOyArK2kpIHtcbiAgICBwID0gcG9seWdvbltpXSwgeDEgPSBwWzBdLCB5MSA9IHBbMV07XG4gICAgaWYgKCgoeTEgPiB5KSAhPT0gKHkwID4geSkpICYmICh4IDwgKHgwIC0geDEpICogKHkgLSB5MSkgLyAoeTAgLSB5MSkgKyB4MSkpIGluc2lkZSA9ICFpbnNpZGU7XG4gICAgeDAgPSB4MSwgeTAgPSB5MTtcbiAgfVxuXG4gIHJldHVybiBpbnNpZGU7XG59XG4iXSwibmFtZXMiOlsicG9seWdvbiIsInBvaW50IiwibiIsImxlbmd0aCIsInAiLCJ4IiwieSIsIngwIiwieTAiLCJ4MSIsInkxIiwiaW5zaWRlIiwiaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-polygon/src/contains.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-polygon/src/cross.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-polygon/src/cross.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b, c) {\n    return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2Nyb3NzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw4RUFBOEU7QUFDOUUsMEVBQTBFO0FBQzFFLDBFQUEwRTtBQUMxRSwrREFBK0Q7QUFDL0QsNkJBQWUsb0NBQVNBLENBQUMsRUFBRUMsQ0FBQyxFQUFFQyxDQUFDO0lBQzdCLE9BQU8sQ0FBQ0QsQ0FBQyxDQUFDLEVBQUUsR0FBR0QsQ0FBQyxDQUFDLEVBQUUsSUFBS0UsQ0FBQUEsQ0FBQyxDQUFDLEVBQUUsR0FBR0YsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDQyxDQUFDLENBQUMsRUFBRSxHQUFHRCxDQUFDLENBQUMsRUFBRSxJQUFLRSxDQUFBQSxDQUFDLENBQUMsRUFBRSxHQUFHRixDQUFDLENBQUMsRUFBRTtBQUNyRSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1wb2x5Z29uL3NyYy9jcm9zcy5qcz84YmE4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFJldHVybnMgdGhlIDJEIGNyb3NzIHByb2R1Y3Qgb2YgQUIgYW5kIEFDIHZlY3RvcnMsIGkuZS4sIHRoZSB6LWNvbXBvbmVudCBvZlxuLy8gdGhlIDNEIGNyb3NzIHByb2R1Y3QgaW4gYSBxdWFkcmFudCBJIENhcnRlc2lhbiBjb29yZGluYXRlIHN5c3RlbSAoK3ggaXNcbi8vIHJpZ2h0LCAreSBpcyB1cCkuIFJldHVybnMgYSBwb3NpdGl2ZSB2YWx1ZSBpZiBBQkMgaXMgY291bnRlci1jbG9ja3dpc2UsXG4vLyBuZWdhdGl2ZSBpZiBjbG9ja3dpc2UsIGFuZCB6ZXJvIGlmIHRoZSBwb2ludHMgYXJlIGNvbGxpbmVhci5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGEsIGIsIGMpIHtcbiAgcmV0dXJuIChiWzBdIC0gYVswXSkgKiAoY1sxXSAtIGFbMV0pIC0gKGJbMV0gLSBhWzFdKSAqIChjWzBdIC0gYVswXSk7XG59XG4iXSwibmFtZXMiOlsiYSIsImIiLCJjIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-polygon/src/cross.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-polygon/src/hull.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-polygon/src/hull.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/../../node_modules/d3-polygon/src/cross.js\");\n\nfunction lexicographicOrder(a, b) {\n    return a[0] - b[0] || a[1] - b[1];\n}\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n    const n = points.length, indexes = [\n        0,\n        1\n    ];\n    let size = 2, i;\n    for(i = 2; i < n; ++i){\n        while(size > 1 && (0,_cross_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0)--size;\n        indexes[size++] = i;\n    }\n    return indexes.slice(0, size); // remove popped points\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(points) {\n    if ((n = points.length) < 3) return null;\n    var i, n, sortedPoints = new Array(n), flippedPoints = new Array(n);\n    for(i = 0; i < n; ++i)sortedPoints[i] = [\n        +points[i][0],\n        +points[i][1],\n        i\n    ];\n    sortedPoints.sort(lexicographicOrder);\n    for(i = 0; i < n; ++i)flippedPoints[i] = [\n        sortedPoints[i][0],\n        -sortedPoints[i][1]\n    ];\n    var upperIndexes = computeUpperHullIndexes(sortedPoints), lowerIndexes = computeUpperHullIndexes(flippedPoints);\n    // Construct the hull polygon, removing possible duplicate endpoints.\n    var skipLeft = lowerIndexes[0] === upperIndexes[0], skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1], hull = [];\n    // Add upper hull in right-to-l order.\n    // Then add lower hull in left-to-right order.\n    for(i = upperIndexes.length - 1; i >= 0; --i)hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n    for(i = +skipLeft; i < lowerIndexes.length - skipRight; ++i)hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n    return hull;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-polygon/src/hull.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-polygon/src/index.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-polygon/src/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polygonArea: () => (/* reexport safe */ _area_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   polygonCentroid: () => (/* reexport safe */ _centroid_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   polygonContains: () => (/* reexport safe */ _contains_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   polygonHull: () => (/* reexport safe */ _hull_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   polygonLength: () => (/* reexport safe */ _length_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./area.js */ \"(ssr)/../../node_modules/d3-polygon/src/area.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/../../node_modules/d3-polygon/src/centroid.js\");\n/* harmony import */ var _hull_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hull.js */ \"(ssr)/../../node_modules/d3-polygon/src/hull.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/../../node_modules/d3-polygon/src/contains.js\");\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./length.js */ \"(ssr)/../../node_modules/d3-polygon/src/length.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBaUQ7QUFDUTtBQUNSO0FBQ1E7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1wb2x5Z29uL3NyYy9pbmRleC5qcz9iZDg2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uQXJlYX0gZnJvbSBcIi4vYXJlYS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHBvbHlnb25DZW50cm9pZH0gZnJvbSBcIi4vY2VudHJvaWQuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uSHVsbH0gZnJvbSBcIi4vaHVsbC5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHBvbHlnb25Db250YWluc30gZnJvbSBcIi4vY29udGFpbnMuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uTGVuZ3RofSBmcm9tIFwiLi9sZW5ndGguanNcIjtcbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwicG9seWdvbkFyZWEiLCJwb2x5Z29uQ2VudHJvaWQiLCJwb2x5Z29uSHVsbCIsInBvbHlnb25Db250YWlucyIsInBvbHlnb25MZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-polygon/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-polygon/src/length.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-polygon/src/length.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n    var i = -1, n = polygon.length, b = polygon[n - 1], xa, ya, xb = b[0], yb = b[1], perimeter = 0;\n    while(++i < n){\n        xa = xb;\n        ya = yb;\n        b = polygon[i];\n        xb = b[0];\n        yb = b[1];\n        xa -= xb;\n        ya -= yb;\n        perimeter += Math.hypot(xa, ya);\n    }\n    return perimeter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2xlbmd0aC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLE9BQU87SUFDN0IsSUFBSUMsSUFBSSxDQUFDLEdBQ0xDLElBQUlGLFFBQVFHLE1BQU0sRUFDbEJDLElBQUlKLE9BQU8sQ0FBQ0UsSUFBSSxFQUFFLEVBQ2xCRyxJQUNBQyxJQUNBQyxLQUFLSCxDQUFDLENBQUMsRUFBRSxFQUNUSSxLQUFLSixDQUFDLENBQUMsRUFBRSxFQUNUSyxZQUFZO0lBRWhCLE1BQU8sRUFBRVIsSUFBSUMsRUFBRztRQUNkRyxLQUFLRTtRQUNMRCxLQUFLRTtRQUNMSixJQUFJSixPQUFPLENBQUNDLEVBQUU7UUFDZE0sS0FBS0gsQ0FBQyxDQUFDLEVBQUU7UUFDVEksS0FBS0osQ0FBQyxDQUFDLEVBQUU7UUFDVEMsTUFBTUU7UUFDTkQsTUFBTUU7UUFDTkMsYUFBYUMsS0FBS0MsS0FBSyxDQUFDTixJQUFJQztJQUM5QjtJQUVBLE9BQU9HO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvbGVuZ3RoLmpzPzBjYzMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocG9seWdvbikge1xuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IHBvbHlnb24ubGVuZ3RoLFxuICAgICAgYiA9IHBvbHlnb25bbiAtIDFdLFxuICAgICAgeGEsXG4gICAgICB5YSxcbiAgICAgIHhiID0gYlswXSxcbiAgICAgIHliID0gYlsxXSxcbiAgICAgIHBlcmltZXRlciA9IDA7XG5cbiAgd2hpbGUgKCsraSA8IG4pIHtcbiAgICB4YSA9IHhiO1xuICAgIHlhID0geWI7XG4gICAgYiA9IHBvbHlnb25baV07XG4gICAgeGIgPSBiWzBdO1xuICAgIHliID0gYlsxXTtcbiAgICB4YSAtPSB4YjtcbiAgICB5YSAtPSB5YjtcbiAgICBwZXJpbWV0ZXIgKz0gTWF0aC5oeXBvdCh4YSwgeWEpO1xuICB9XG5cbiAgcmV0dXJuIHBlcmltZXRlcjtcbn1cbiJdLCJuYW1lcyI6WyJwb2x5Z29uIiwiaSIsIm4iLCJsZW5ndGgiLCJiIiwieGEiLCJ5YSIsInhiIiwieWIiLCJwZXJpbWV0ZXIiLCJNYXRoIiwiaHlwb3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-polygon/src/length.js\n");

/***/ })

};
;