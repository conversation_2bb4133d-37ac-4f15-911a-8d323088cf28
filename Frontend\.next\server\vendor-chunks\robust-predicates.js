"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/robust-predicates";
exports.ids = ["vendor-chunks/robust-predicates"];
exports.modules = {

/***/ "(ssr)/../../node_modules/robust-predicates/esm/incircle.js":
/*!************************************************************!*\
  !*** ../../node_modules/robust-predicates/esm/incircle.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* binding */ incircle),\n/* harmony export */   incirclefast: () => (/* binding */ incirclefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/../../node_modules/robust-predicates/esm/util.js\");\n\nconst iccerrboundA = (10 + 96 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundB = (4 + 48 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundC = (44 + 576 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst aa = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst v = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst axtbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst aytbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bxtca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bytca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cxtab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cytab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abtt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bctt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst catt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _32 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _32b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _64 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(64);\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nfunction finadd(finlen, a, alen) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, a, alen, fin2);\n    const tmp = fin;\n    fin = fin2;\n    fin2 = tmp;\n    return finlen;\n}\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adx, _8), _8, adx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdx, _8), _8, bdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdx, _8), _8, cdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n    errbound = iccerrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += (adx * adx + ady * ady) * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + 2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx) + ((bdx * bdx + bdy * bdy) * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + 2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) + ((cdx * cdx + cdy * cdy) * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + 2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n    if (adxtail !== 0) {\n        axtbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, 2 * adx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdy, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, 2 * ady, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adytail, _8), _8, cdx, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, 2 * bdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdy, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, 2 * bdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdytail, _8), _8, adx, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, 2 * cdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, ady, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, 2 * cdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdytail, _8), _8, bdx, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, adxtail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * adx, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * adx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adxtail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, adytail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * ady, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * ady, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adytail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, bdxtail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdx, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdxtail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, bdytail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdy, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdytail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, cdxtail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdx, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdx, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdxtail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, cdytail, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdy, _32), _32, _48), _48);\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdy, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdytail, _16b), _16b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    return fin[finlen - 1];\n}\nfunction incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n    const det = alift * (bdxcdy - cdxbdy) + blift * (cdxady - adxcdy) + clift * (adxbdy - bdxady);\n    const permanent = (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift + (Math.abs(cdxady) + Math.abs(adxcdy)) * blift + (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n    const errbound = iccerrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\nfunction incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/robust-predicates/esm/incircle.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/robust-predicates/esm/insphere.js":
/*!************************************************************!*\
  !*** ../../node_modules/robust-predicates/esm/insphere.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   insphere: () => (/* binding */ insphere),\n/* harmony export */   inspherefast: () => (/* binding */ inspherefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/../../node_modules/robust-predicates/esm/util.js\");\n\nconst isperrboundA = (16 + 224 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundB = (5 + 72 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundC = (71 + 1408 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst de = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst da = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst eb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst abc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bcd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cde = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst dea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst abd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cda = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst deb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst adet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst bdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst cdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst ddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst edet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst abdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cdedet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(3456);\nconst deter = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(5760);\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _24 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _48b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _96 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst _192 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nconst _384x = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384y = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384z = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _768 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(768);\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, a, az, _8), _8, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, b, bz, _8b), _8b, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, c, cz, _8c), _8c, _16, out);\n}\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(alen, a, blen, b, _48), _48, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(clen, c, dlen, d, _48b), _48b), _48b, _96);\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, x, _192), _192, x, _384x), _384x, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, y, _192), _192, y, _384y), _384y, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n    s1 = ax * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n    const deterlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet, liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet, liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet, liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n    return deter[deterlen - 1];\n}\nconst xdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst ydet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst zdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, x, _48), _48, x, xdet), xdet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, y, _48), _48, y, ydet), ydet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n    s1 = aex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n    const finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet, liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet, liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 && bextail === 0 && beytail === 0 && beztail === 0 && cextail === 0 && ceytail === 0 && ceztail === 0 && dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n    errbound = isperrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    const abeps = aex * beytail + bey * aextail - (aey * bextail + bex * aeytail);\n    const bceps = bex * ceytail + cey * bextail - (bey * cextail + cex * beytail);\n    const cdeps = cex * deytail + dey * cextail - (cey * dextail + dex * ceytail);\n    const daeps = dex * aeytail + aey * dextail - (dey * aextail + aex * deytail);\n    const aceps = aex * ceytail + cey * aextail - (aey * cextail + cex * aeytail);\n    const bdeps = bex * deytail + dey * bextail - (bey * dextail + dex * beytail);\n    det += (bex * bex + bey * bey + bez * bez) * (cez * daeps + dez * aceps + aez * cdeps + (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) * (aez * bceps - bez * aceps + cez * abeps + (aeztail * bc3 - beztail * ac3 + ceztail * ab3)) - ((aex * aex + aey * aey + aez * aez) * (bez * cdeps - cez * bdeps + dez * bceps + (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) * (dez * abeps + aez * bdeps + bez * daeps + (deztail * ab3 + aeztail * bd3 + beztail * da3))) + 2 * ((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) + (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3) - ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) + (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\nfunction insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n    const det = clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab) + (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent = (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift + (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift + (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift + (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\nfunction inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n    return clift * dab - dlift * abc + (alift * bcd - blift * cda);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/robust-predicates/esm/insphere.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/robust-predicates/esm/orient2d.js":
/*!************************************************************!*\
  !*** ../../node_modules/robust-predicates/esm/orient2d.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient2d: () => (/* binding */ orient2d),\n/* harmony export */   orient2dfast: () => (/* binding */ orient2dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/../../node_modules/robust-predicates/esm/util.js\");\n\nconst ccwerrboundA = (3 + 16 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundB = (2 + 12 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundC = (9 + 64 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst B = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst C1 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst C2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\nconst D = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n    s1 = acx * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n    errbound = ccwerrboundC * detsum + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += acx * bcytail + bcy * acxtail - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n    s1 = acxtail * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, B, 4, u, C1);\n    s1 = acx * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C1len, C1, 4, u, C2);\n    s1 = acxtail * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C2len, C2, 4, u, D);\n    return D[Dlen - 1];\n}\nfunction orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\nfunction orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/robust-predicates/esm/orient2d.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/robust-predicates/esm/orient3d.js":
/*!************************************************************!*\
  !*** ../../node_modules/robust-predicates/esm/orient3d.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient3d: () => (/* binding */ orient3d),\n/* harmony export */   orient3dfast: () => (/* binding */ orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/../../node_modules/robust-predicates/esm/util.js\");\n\nconst o3derrboundA = (7 + 56 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundB = (3 + 28 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundC = (26 + 288 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _12 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nfunction finadd(finlen, alen, a) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, alen, a, fin2);\n    const tmp = fin;\n    fin = fin2;\n    fin2 = tmp;\n    return finlen;\n}\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adz, _8), _8, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdz, _8b), _8b, _16), _16, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdz, _8), _8, fin);\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0 && adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n    errbound = o3derrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) + bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) + cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n    const bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adz, _16), _16);\n    const catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdz, _16), _16);\n    const abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdz, _16), _16);\n    if (adztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdztail, _16), _16);\n    }\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n    return fin[finlen - 1];\n}\nfunction orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const det = adz * (bdxcdy - cdxbdy) + bdz * (cdxady - adxcdy) + cdz * (adxbdy - bdxady);\n    const permanent = (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) + (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) + (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\nfunction orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n    return adx * (bdy * cdz - bdz * cdy) + bdx * (cdy * adz - cdz * ady) + cdx * (ady * bdz - adz * bdy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/robust-predicates/esm/orient3d.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/robust-predicates/esm/util.js":
/*!********************************************************!*\
  !*** ../../node_modules/robust-predicates/esm/util.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   estimate: () => (/* binding */ estimate),\n/* harmony export */   negate: () => (/* binding */ negate),\n/* harmony export */   resulterrbound: () => (/* binding */ resulterrbound),\n/* harmony export */   scale: () => (/* binding */ scale),\n/* harmony export */   splitter: () => (/* binding */ splitter),\n/* harmony export */   sum: () => (/* binding */ sum),\n/* harmony export */   sum_three: () => (/* binding */ sum_three),\n/* harmony export */   vec: () => (/* binding */ vec)\n/* harmony export */ });\nconst epsilon = 1.1102230246251565e-16;\nconst splitter = 134217729;\nconst resulterrbound = (3 + 8 * epsilon) * epsilon;\n// fast_expansion_sum_zeroelim routine from oritinal code\nfunction sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if (fnow > enow === fnow > -enow) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if (fnow > enow === fnow > -enow) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while(eindex < elen && findex < flen){\n            if (fnow > enow === fnow > -enow) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while(eindex < elen){\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while(findex < flen){\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\nfunction sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n// scale_expansion_zeroelim routine from oritinal code\nfunction scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for(let i = 1; i < elen; i++){\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\nfunction negate(elen, e) {\n    for(let i = 0; i < elen; i++)e[i] = -e[i];\n    return elen;\n}\nfunction estimate(elen, e) {\n    let Q = e[0];\n    for(let i = 1; i < elen; i++)Q += e[i];\n    return Q;\n}\nfunction vec(n) {\n    return new Float64Array(n);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/robust-predicates/esm/util.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/robust-predicates/index.js":
/*!*****************************************************!*\
  !*** ../../node_modules/robust-predicates/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incircle),\n/* harmony export */   incirclefast: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incirclefast),\n/* harmony export */   insphere: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.insphere),\n/* harmony export */   inspherefast: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.inspherefast),\n/* harmony export */   orient2d: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2d),\n/* harmony export */   orient2dfast: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2dfast),\n/* harmony export */   orient3d: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3d),\n/* harmony export */   orient3dfast: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./esm/orient2d.js */ \"(ssr)/../../node_modules/robust-predicates/esm/orient2d.js\");\n/* harmony import */ var _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./esm/orient3d.js */ \"(ssr)/../../node_modules/robust-predicates/esm/orient3d.js\");\n/* harmony import */ var _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./esm/incircle.js */ \"(ssr)/../../node_modules/robust-predicates/esm/incircle.js\");\n/* harmony import */ var _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./esm/insphere.js */ \"(ssr)/../../node_modules/robust-predicates/esm/insphere.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JvYnVzdC1wcmVkaWNhdGVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUN5RDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9yb2J1c3QtcHJlZGljYXRlcy9pbmRleC5qcz85MGFmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHtvcmllbnQyZCwgb3JpZW50MmRmYXN0fSBmcm9tICcuL2VzbS9vcmllbnQyZC5qcyc7XG5leHBvcnQge29yaWVudDNkLCBvcmllbnQzZGZhc3R9IGZyb20gJy4vZXNtL29yaWVudDNkLmpzJztcbmV4cG9ydCB7aW5jaXJjbGUsIGluY2lyY2xlZmFzdH0gZnJvbSAnLi9lc20vaW5jaXJjbGUuanMnO1xuZXhwb3J0IHtpbnNwaGVyZSwgaW5zcGhlcmVmYXN0fSBmcm9tICcuL2VzbS9pbnNwaGVyZS5qcyc7XG4iXSwibmFtZXMiOlsib3JpZW50MmQiLCJvcmllbnQyZGZhc3QiLCJvcmllbnQzZCIsIm9yaWVudDNkZmFzdCIsImluY2lyY2xlIiwiaW5jaXJjbGVmYXN0IiwiaW5zcGhlcmUiLCJpbnNwaGVyZWZhc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/robust-predicates/index.js\n");

/***/ })

};
;