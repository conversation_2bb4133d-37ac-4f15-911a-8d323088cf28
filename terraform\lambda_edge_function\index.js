"use strict";

exports.handler = async (event) => {
  const request = event.Records[0].cf.request;
  let uri = request.uri.toLowerCase();
  const host = request.headers.host[0].value;

  // Helper function to determine target file extension based on original request
  const getTargetExtension = (originalUri) => {
    if (originalUri.endsWith(".txt") || originalUri.endsWith("/index.txt")) {
      return ".txt";
    }
    return ".html";
  };

  // Helper function to detect if URI has multiple extensions (like .xml.txt)
  const hasMultipleExtensions = (uri) => {
    const extensionMatches = uri.match(/\.[a-zA-Z0-9]+/g);
    return extensionMatches && extensionMatches.length > 1;
  };

  const targetExtension = getTargetExtension(uri);

  // === 0. Early blog handling (before index.txt blocking)
  if (
    uri === "/blog" ||
    uri === "/blog/" ||
    uri === "/blog.txt" ||
    uri === "/blog/index.txt"
  ) {
    request.uri = `/blog/index${targetExtension}`;
    return request;
  }

  // === 0.1. Handle /index path redirect (fix for blog navigation issue)
  if (uri === "/index" || uri === "/index/") {
    return {
      status: "301",
      statusDescription: "Moved Permanently",
      headers: {
        location: [{ key: "Location", value: `https://${host}/` }],
      },
    };
  }

  // === 0.2. Root index.txt handling moved to section 7 for consistency

  // Clean URI for mapping (remove trailing slash and any .txt extension)
  let cleanUri = uri.replace(/\/index\.txt$/, "").replace(/\.txt$/, "");
  const key = cleanUri.endsWith("/") ? cleanUri.slice(0, -1) : cleanUri;

  const redirectMap = {
    "/hire-dedicated-development-team":
      "/services/staff-augmentation/hire-dedicated-development-teams",
    "/it-outsourcing-services": "/services/staff-augmentation/it-outsourcing",
    "/agile-software-development-services":
      "/services/staff-augmentation/hire-agile-developers",
    "/hire-python-developers":
      "/services/staff-augmentation/hire-python-developers",
    "/hire-reactjs-developers":
      "/services/staff-augmentation/hire-react-developers",
    "/hire-angularjs-developers":
      "/services/staff-augmentation/hire-angular-developers",
    "/hire-asp-net-developers":
      "/services/staff-augmentation/hire-dot-net-developers",
    "/hire-node-js-developers":
      "/services/staff-augmentation/hire-node-js-developers",
    "/hire-mobile-app-developers":
      "/services/staff-augmentation/hire-mobile-app-developers",
    "/cto-as-a-service": "/services/staff-augmentation/virtual-cto-services",
    "/product-strategy-consulting-services":
      "/services/technology-advisory/product-strategy",
    "/product-management-consulting-services":
      "/services/technology-advisory/product-management-consulting",
    "/digital-transformation-services":
      "/services/technology-advisory/digital-transformation-consulting",
    "/enterprise-application-modernization-services":
      "/services/technology-advisory/enterprise-application-modernization",
    "/software-audit": "/services/technology-advisory/code-audit",
    "/risk-and-compliance":
      "/services/technology-advisory/risk-and-compliance-services",
    "/google-cloud-development-services":
      "/services/technology-advisory/google-cloud-development",
    "/bot-development-services":
      "/services/interactive-experience/chatbot-development",
    "/robotic-process-automation-services":
      "/services/interactive-experience/robotic-process-automation",
    "/saas-application-development-services":
      "/services/software-product-engineering/saas-application-development",
    "/web-application-development-services":
      "/services/software-product-engineering/web-application-development",
    "/mobile-app-development-services":
      "/services/software-product-engineering/mobile-app-development",
    "/low-code-no-code-services":
      "/services/software-product-engineering/low-code-no-code-development",
    "/ui-test-automation": "/services/quality-engineering/ui-test-automation",
    "/performance-testing-services":
      "/services/quality-engineering/performance-testing",
    "/security-testing-services":
      "/services/quality-engineering/security-testing",
    "/functional-testing-services":
      "/services/quality-engineering/functional-testing",
    "/infrastructure-support-services":
      "/services/maintenance-and-support/infrastructure-managed-services",
    "/application-maintenance-services":
      "/services/maintenance-and-support/application-support-services",
    "/cloud-native-application-development":
      "/services/cloud-application-development/cloud-native-application-development",
    "/microservices-consulting-services":
      "/services/cloud-application-development/microservices-consulting",
    "/serverless-app-development-services":
      "/services/cloud-application-development/serverless-app-development",
    "/cloud-migration-services":
      "/services/cloud-application-development/cloud-migration-consulting",
    "/cloud-consulting-services":
      "/services/cloud-application-development/cloud-computing-consulting",
    "/cloud-security-services":
      "/services/cloud-application-development/cloud-security-services",
    "/cloud-infrastructure-management-services":
      "/services/devops-consulting/cloud-infrastructure-services",
    "/devops-ci-cd-services": "/services/devops-consulting/ci-cd-solutions",
    "/containerization-services":
      "/services/devops-consulting/containerization-services",
    "/infrastructure-as-code-iac-services":
      "/services/devops-consulting/infrastructure-as-code",
    "/system-integration-services":
      "/services/devops-consulting/systems-integration",
    "/data-engineering-services":
      "/services/data-analytics-consulting/data-engineering",
    "/business-intelligence-consulting-services":
      "/services/data-analytics-consulting/business-intelligence-consulting",
    "/machine-learning-services":
      "/services/artificial-intelligence-consulting/machine-learning",
    "/natural-language-processing-services":
      "/services/artificial-intelligence-consulting/natural-language-processing",
    "/computer-vision-services":
      "/services/artificial-intelligence-consulting/computer-vision",
    "/user-research-testing":
      "/services/ui-ux-design-and-development/user-research-and-testing",
    "/software-prototyping-services":
      "/services/ui-ux-design-and-development/rapid-prototyping-software",
    "/software-product-development-services":
      "/services/software-product-engineering",
    "/quality-engineering-services": "/services/quality-engineering",
    "/cloud-application-development-services":
      "/services/cloud-application-development",
    "/devops-consulting-services": "/services/devops-consulting",
    "/data-analytics-services": "/services/data-analytics-consulting",
    "/artificial-intelligence-services":
      "/services/artificial-intelligence-consulting",
    "/ui-ux-design-and-development-services":
      "/services/ui-ux-design-and-development",
    "/business-technology-consulting": "/services/technology-advisory",
    "/it-staff-augmentation-services": "/services/staff-augmentation",
    "/free-consultation-page": "/contact-us",
    "/case-study/aws-cloud-cost-optimization": "/case-study",
    "/products/wotnot": "",
    "/home": "",
    "/software-product-development-services-abm":
      "/services/software-product-engineering",
    "/abm/product-dev-campaign": "/services/software-product-engineering",
    "/abm/devops-campaign": "/services/cloud-application-development",
    "/partners/aws/solutions-abm": "/partners/aws/solutions",
    "/insights": "/resources",
    "/white-paper": "",
    "/artificial-intelligence-in-insurance": "/top-ai-insurance-use-cases",
    "/bot-development": "/services/interactive-experience/chatbot-development",
    "/trends-chatbot": "/chatbot-trends",
    "/traits-good-chatbot": "/complete-guide-chatbots",
    "/chatbots-and-service-industry": "/complete-guide-chatbots",
    "/7-reasons-why-business-needs-chatbot": "/complete-guide-chatbots",
    "/heres-need-know-chatbots": "/complete-guide-chatbots",
    "/design-chatbot-conversation": "/complete-guide-chatbots",
    "/what-chatbots-can-do-for-e-commerce-industry": "/complete-guide-chatbots",
    "/ai-in-the-insurance-industry":
      "/ai-insurance-implementation-challenges-solutions",
    "/what-is-a-citizen-developer": "/citizen-developer-framework",
    "/ways-ai-transforming-finance":
      "/artificial-intelligence-and-machine-learning",
    "/case-study/chatbot-solution-artificial-intelligence":
      "/case-study/sms-chatbot",
    "/case-study/ai-medical-record-summarization-tool":
      "/case-study/medical-record-processing-using-nlp",
    "/business-process-automation-bot-solution":
      "/case-study/scheduling-chatbot",
    "/product-dev-services-designrush":
      "/services/software-product-engineering",
    "/product-development-manifest": "/services/software-product-engineering",
    "/mobile-application-development-trends":
      "/7-trends-of-mobile-application-development",
    "/cognitive-computing-features-scope":
      "/cognitive-computing-features-scope-limitations",
    "/predictive-analytics-modelsalgorithms":
      "/predictive-analytics-models-algorithms",
    "/campaign/retail-data-engineering-and-analytics": "/retail",
    "/retail-data-engineering-and-analytics": "/retail",
    "/industry/legal/legal": "/legal",
    "/maruti-techlabs-recognized-as-most-reviewed-ai-company": "",
    "/cloud-consulting-business-partner":
      "/services/cloud-application-development/cloud-computing-consulting",
    "/healthcare/healthcare": "/healthcare",
    "/services/ui-ux-design-consulting/rapid-prototyping-software":
      "/services/ui-ux-design-and-development/rapid-prototyping-software",
    "/services/devopsconsulting/containerization-services":
      "/services/devops-consulting/containerization-services",
    "/services/data-analytics/business-intelligence-consulting":
      "/services/data-analytics-consulting/business-intelligence-consulting",
    "/services/ui-ux-design-consulting/user-research-and-testing":
      "/services/ui-ux-design-and-development/user-research-and-testing",
    "/industry/insurance/insurance": "/insurance",
    "/ai-readiness": "/ai-readiness-audit",
    "/legal/legal": "/legal",
    "/retail/retail/retail": "/retail",
    "/services/data-analytics/data-engineering":
      "/services/data-analytics-consulting/data-engineering",
    "/industry/healthcare/healthcare": "/healthcare",
    "/insurance/insurance": "/insurance",
    "/ai-strategy-readiness": "/services/ai-strategy-readiness",
  };

  const rewriteMap = {
    "/healthcare": "/industry/healthcare",
    "/legal": "/industry/legal",
    "/retail": "/retail/retail",
    "/insurance": "/industry/insurance",
    "/cloud-consulting-solution": "/cloud-consulting/cloud-consulting-solution",
    "/data-visualization-services":
      "/cloud-consulting/data-visualization-services",
    "/top-ai-insurance-use-cases": "/blog/top-ai-insurance-use-cases",
    "/chatbot-trends": "/blog/chatbot-trends",
    "/complete-guide-chatbots": "/blog/complete-guide-chatbots",
    "/citizen-developer-framework": "/blog/citizen-developer-framework",
    "/ai-insurance-implementation-challenges-solutions":
      "/blog/ai-insurance-implementation-challenges-solutions",
    "/artificial-intelligence-and-machine-learning":
      "/blog/artificial-intelligence-and-machine-learning",
    "/7-trends-of-mobile-application-development":
      "/blog/7-trends-of-mobile-application-development",
    "/cognitive-computing-features-scope-limitations":
      "/blog/cognitive-computing-features-scope-limitations",
    "/predictive-analytics-modelsalgorithms":
      "/blog/predictive-analytics-models-algorithms",
    "/devsecops-principles-key-insights":
      "/blog/devSecOps-principles-key-insights",
    "/best-practices-insurance-mobile-app-development":
      "/blog/best-Practices-insurance-mobile-app-development",
  };

  // === 1. Handle static redirects (301)
  if (redirectMap.hasOwnProperty(key)) {
    return {
      status: "301",
      statusDescription: "Moved Permanently",
      headers: {
        location: [
          { key: "Location", value: `https://${host}${redirectMap[key]}/` },
        ],
      },
    };
  }

  // === 2. Blog root (moved to early handling above)

  // === 3. Handle /blog/slug → /slug/
  if (uri.startsWith("/blog/") && uri !== "/blog/" && uri !== "/blog") {
    let slug = uri
      .replace(/^\/blog\//, "")
      .replace(/\/index\.txt$/, "")
      .replace(/\.txt$/, "")
      .trim();
    if (!slug || slug === "undefined" || slug === "null") {
      return {
        status: "301",
        statusDescription: "Redirect to blog root",
        headers: {
          location: [{ key: "Location", value: `https://${host}/blog/` }],
        },
      };
    }

    const cleanedSlug = slug.replace(/^\/|\/$/g, "");
    return {
      status: "301",
      statusDescription: "Moved Permanently",
      headers: {
        location: [
          { key: "Location", value: `https://${host}/${cleanedSlug}/` },
        ],
      },
    };
  }

  // === 4. Handle static rewrite map
  if (rewriteMap.hasOwnProperty(key)) {
    request.uri = `${rewriteMap[key]}/index${targetExtension}`;
    return request;
  }

  // === 5. Handle dynamic clean slugs → /blog/slug/index.html
  const staticPrefixes = [
    "/services",
    "/case-study",
    "/industry",
    "/resources",
    "/partners",
    "/contact",
    "/about",
    "/careers",
    "/products",
    "/videos",
    "/ai-readiness-audit",
    "/podcasts",
    "/privacy-policy",
    "/cookie-policy",
    "/about-us",
    "/ebooks",
    "/events",
    "/search",
    "/thank-you",
    "/service",
    "/cloud-consulting",
    "/retail",
    "/sitemap",
    "/cloud-migration-cost-calculator",
  ];

  // Create a clean URI for blog logic that properly handles multiple extensions
  let blogCleanUri = uri.replace(/\/+$/, ""); // Remove trailing slashes

  // Handle multiple extensions properly - for files like sitemap.xml.txt
  if (hasMultipleExtensions(blogCleanUri)) {
    // For multiple extensions, only remove .txt or .html if it's the last extension
    if (blogCleanUri.endsWith(".txt") || blogCleanUri.endsWith(".html")) {
      blogCleanUri = blogCleanUri.replace(/\.(html|txt)$/, "");
    }
  } else {
    // For single extensions, remove .html or .txt extension if present
    if (blogCleanUri.endsWith(".html") || blogCleanUri.endsWith(".txt")) {
      blogCleanUri = blogCleanUri.replace(/\.(html|txt)$/, "");
    }
  }

  // Check if this is a file request (has extension) or a known static path
  const hasExtension = /\.[a-zA-Z0-9]+$/.test(blogCleanUri);
  const isStaticPath = staticPrefixes.some((prefix) =>
    blogCleanUri.startsWith(prefix)
  );

  if (
    !isStaticPath &&
    !hasExtension &&
    blogCleanUri !== "" &&
    blogCleanUri !== "/"
  ) {
    const blogPath = `/blog${blogCleanUri}/index${targetExtension}`;
    request.uri = blogPath;
    return request;
  }

  // === 6. Handle static paths without trailing slash
  if (isStaticPath && !hasExtension && !blogCleanUri.endsWith("/")) {
    // For services paths, try the parent directory first
    if (cleanUri.startsWith("/services/")) {
      const pathParts = cleanUri.split("/");
      if (pathParts.length > 2) {
        // If it's a sub-service page, redirect to parent service page
        const parentPath = `/${pathParts[1]}/${pathParts[2]}`;
        request.uri = `${parentPath}/index${targetExtension}`;
        return request;
      }
    }
    request.uri = `${blogCleanUri}/index${targetExtension}`;
    return request;
  }

  // === 6.5. Handle files with multiple extensions (like sitemap.xml.txt)
  if (
    hasMultipleExtensions(uri) &&
    (uri.endsWith(".txt") || uri.endsWith(".html"))
  ) {
    // For files like sitemap.xml.txt, rewrite to sitemap.xml
    const baseFile = uri.replace(/\.(html|txt)$/, "");
    request.uri = baseFile;
    return request;
  }

  // === 7. Handle root and trailing slash cases
  if (uri === "/" || uri === "" || uri === "/index.txt") {
    request.uri = `/index${targetExtension}`;
    return request;
  }

  // === 8. Append index file if ends with slash (but not already handled)
  if (
    uri.endsWith("/") &&
    !uri.endsWith("/index.html") &&
    !uri.endsWith("/index.txt")
  ) {
    request.uri = uri + `index${targetExtension}`;
    return request;
  }

  // === 9. Handle remaining .txt files (preserve extension for non-index files)
  // No conversion needed - let CloudFront serve the actual .txt files

  return request;
};
