{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "true", "access-control-allow-origin": "", "connection": "keep-alive", "content-length": "3594", "content-security-policy": "script-src 'self' 'unsafe-inline' cdn.marutitech.com;img-src 'self' data: strapi.io cdn.marutitech.com storage.googleapis.com cdn-gcp.new.marutitech.com;media-src cdn.marutitech.com cdn-gcp.new.marutitech.com blob:;connect-src 'self' https:;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "content-type": "application/json; charset=utf-8", "date": "Thu, 31 Jul 2025 09:54:01 GMT", "referrer-policy": "no-referrer", "server": "nginx/1.24.0", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-powered-by": "Strapi <strapi.io>"}, "body": "eyJkYXRhIjp7ImlkIjoxLCJhdHRyaWJ1dGVzIjp7ImNvc3RfcmFuZ2VfaGVhZGluZyI6bnVsbCwiY3JlYXRlZEF0IjoiMjAyNS0wNy0yMlQxNDowNzoyMC43NTBaIiwidXBkYXRlZEF0IjoiMjAyNS0wNy0yNVQwNjo0NzoyNi41OTlaIiwicHVibGlzaGVkQXQiOiIyMDI1LTA3LTIzVDEwOjA5OjUyLjQ5MloiLCJzZW8iOnsiaWQiOjI3ODksInRpdGxlIjoiQ2xvdWQgTWlncmF0aW9uIENvc3QgQ2FsY3VsYXRvciB8IEZhc3QgJiBBY2N1cmF0ZSBDb3N0IEVzdGltYXRpb24gVG9vbCIsImRlc2NyaXB0aW9uIjoiUXVpY2tseSBlc3RpbWF0ZSB0aGUgdG90YWwgY29zdCBvZiBtaWdyYXRpbmcgeW91ciB3b3JrbG9hZHMgdG8gdGhlIGNsb3VkLiBPdXIgQ2xvdWQgTWlncmF0aW9uIENvc3QgQ2FsY3VsYXRvciBzdXBwb3J0cyBBV1MsIEF6dXJlLCBhbmQgR29vZ2xlIENsb3VkIHdpdGggZGV0YWlsZWQgcHJpY2luZyBmb3Igc3RvcmFnZSwgY29tcHV0ZSwgbmV0d29yaywgYW5kIHNlcnZpY2VzLiIsInR5cGUiOiJ3ZWJzaXRlIiwidXJsIjoiaHR0cHM6Ly9tYXJ1dGl0ZWNoLmNvbS9jbG91ZC1taWdyYXRpb24tY29zdC1jYWxjdWxhdG9yLyIsInNpdGVfbmFtZSI6Ik1hcnV0aSBUZWNobGFicyIsImxvY2FsZSI6ImVuLVVTIiwic2NoZW1hIjpudWxsLCJrZXl3b3JkcyI6eyJpZCI6MjM0LCJrZXl3b3JkIjoiQ2xvdWQgTWlncmF0aW9uIENvc3QgQ2FsY3VsYXRvciJ9LCJtZXRhUHJvcGVydGllcyI6W3siaWQiOjUzODAsIm5hbWUiOiJvZzp0aXRsZSIsImNvbnRlbnQiOiJDbG91ZCBNaWdyYXRpb24gQ29zdCBDYWxjdWxhdG9yIHwgRmFzdCAmIEFjY3VyYXRlIENvc3QgRXN0aW1hdGlvbiBUb29sIn0seyJpZCI6NTM4MSwibmFtZSI6Im9nOmRlc2NyaXB0aW9uIiwiY29udGVudCI6IlF1aWNrbHkgZXN0aW1hdGUgdGhlIHRvdGFsIGNvc3Qgb2YgbWlncmF0aW5nIHlvdXIgd29ya2xvYWRzIHRvIHRoZSBjbG91ZC4gT3VyIENsb3VkIE1pZ3JhdGlvbiBDb3N0IENhbGN1bGF0b3Igc3VwcG9ydHMgQVdTLCBBenVyZSwgYW5kIEdvb2dsZSBDbG91ZCB3aXRoIGRldGFpbGVkIHByaWNpbmcgZm9yIHN0b3JhZ2UsIGNvbXB1dGUsIG5ldHdvcmssIGFuZCBzZXJ2aWNlcy4ifV0sImltYWdlIjp7ImRhdGEiOnsiaWQiOjM5MjQsImF0dHJpYnV0ZXMiOnsibmFtZSI6ImFtYXppbmctZGF5LWhhcHB5LWJlYXV0aWZ1bC13b21hbi13b3JraW5nLXNlcnZlci1jYWJpbmV0LWhvbGRpbmctaGVyLWxhcHRvcC53ZWJwIiwiYWx0ZXJuYXRpdmVUZXh0IjpudWxsLCJjYXB0aW9uIjpudWxsLCJ3aWR0aCI6NTc2MCwiaGVpZ2h0IjozODQwLCJmb3JtYXRzIjp7InNtYWxsIjp7Im5hbWUiOiJzbWFsbF9hbWF6aW5nLWRheS1oYXBweS1iZWF1dGlmdWwtd29tYW4td29ya2luZy1zZXJ2ZXItY2FiaW5ldC1ob2xkaW5nLWhlci1sYXB0b3Aud2VicCIsImhhc2giOiJzbWFsbF9hbWF6aW5nX2RheV9oYXBweV9iZWF1dGlmdWxfd29tYW5fd29ya2luZ19zZXJ2ZXJfY2FiaW5ldF9ob2xkaW5nX2hlcl9sYXB0b3BfNjU4ODNhNmM4NSIsImV4dCI6Ii53ZWJwIiwibWltZSI6ImltYWdlL3dlYnAiLCJwYXRoIjpudWxsLCJ3aWR0aCI6NTAwLCJoZWlnaHQiOjMzMywic2l6ZSI6MTkuNzgsInNpemVJbkJ5dGVzIjoxOTc4MiwidXJsIjoiaHR0cHM6Ly9jZG4ubWFydXRpdGVjaC5jb20vc21hbGxfYW1hemluZ19kYXlfaGFwcHlfYmVhdXRpZnVsX3dvbWFuX3dvcmtpbmdfc2VydmVyX2NhYmluZXRfaG9sZGluZ19oZXJfbGFwdG9wXzY1ODgzYTZjODUud2VicCJ9LCJtZWRpdW0iOnsibmFtZSI6Im1lZGl1bV9hbWF6aW5nLWRheS1oYXBweS1iZWF1dGlmdWwtd29tYW4td29ya2luZy1zZXJ2ZXItY2FiaW5ldC1ob2xkaW5nLWhlci1sYXB0b3Aud2VicCIsImhhc2giOiJtZWRpdW1fYW1hemluZ19kYXlfaGFwcHlfYmVhdXRpZnVsX3dvbWFuX3dvcmtpbmdfc2VydmVyX2NhYmluZXRfaG9sZGluZ19oZXJfbGFwdG9wXzY1ODgzYTZjODUiLCJleHQiOiIud2VicCIsIm1pbWUiOiJpbWFnZS93ZWJwIiwicGF0aCI6bnVsbCwid2lkdGgiOjc1MCwiaGVpZ2h0Ijo1MDAsInNpemUiOjQwLCJzaXplSW5CeXRlcyI6NDAwMDAsInVybCI6Imh0dHBzOi8vY2RuLm1hcnV0aXRlY2guY29tL21lZGl1bV9hbWF6aW5nX2RheV9oYXBweV9iZWF1dGlmdWxfd29tYW5fd29ya2luZ19zZXJ2ZXJfY2FiaW5ldF9ob2xkaW5nX2hlcl9sYXB0b3BfNjU4ODNhNmM4NS53ZWJwIn0sImxhcmdlIjp7Im5hbWUiOiJsYXJnZV9hbWF6aW5nLWRheS1oYXBweS1iZWF1dGlmdWwtd29tYW4td29ya2luZy1zZXJ2ZXItY2FiaW5ldC1ob2xkaW5nLWhlci1sYXB0b3Aud2VicCIsImhhc2giOiJsYXJnZV9hbWF6aW5nX2RheV9oYXBweV9iZWF1dGlmdWxfd29tYW5fd29ya2luZ19zZXJ2ZXJfY2FiaW5ldF9ob2xkaW5nX2hlcl9sYXB0b3BfNjU4ODNhNmM4NSIsImV4dCI6Ii53ZWJwIiwibWltZSI6ImltYWdlL3dlYnAiLCJwYXRoIjpudWxsLCJ3aWR0aCI6MTAwMCwiaGVpZ2h0Ijo2NjcsInNpemUiOjY0LjA4LCJzaXplSW5CeXRlcyI6NjQwNzYsInVybCI6Imh0dHBzOi8vY2RuLm1hcnV0aXRlY2guY29tL2xhcmdlX2FtYXppbmdfZGF5X2hhcHB5X2JlYXV0aWZ1bF93b21hbl93b3JraW5nX3NlcnZlcl9jYWJpbmV0X2hvbGRpbmdfaGVyX2xhcHRvcF82NTg4M2E2Yzg1LndlYnAifSwidGh1bWJuYWlsIjp7Im5hbWUiOiJ0aHVtYm5haWxfYW1hemluZy1kYXktaGFwcHktYmVhdXRpZnVsLXdvbWFuLXdvcmtpbmctc2VydmVyLWNhYmluZXQtaG9sZGluZy1oZXItbGFwdG9wLndlYnAiLCJoYXNoIjoidGh1bWJuYWlsX2FtYXppbmdfZGF5X2hhcHB5X2JlYXV0aWZ1bF93b21hbl93b3JraW5nX3NlcnZlcl9jYWJpbmV0X2hvbGRpbmdfaGVyX2xhcHRvcF82NTg4M2E2Yzg1IiwiZXh0IjoiLndlYnAiLCJtaW1lIjoiaW1hZ2Uvd2VicCIsInBhdGgiOm51bGwsIndpZHRoIjoyMzQsImhlaWdodCI6MTU2LCJzaXplIjo1LjYxLCJzaXplSW5CeXRlcyI6NTYxMCwidXJsIjoiaHR0cHM6Ly9jZG4ubWFydXRpdGVjaC5jb20vdGh1bWJuYWlsX2FtYXppbmdfZGF5X2hhcHB5X2JlYXV0aWZ1bF93b21hbl93b3JraW5nX3NlcnZlcl9jYWJpbmV0X2hvbGRpbmdfaGVyX2xhcHRvcF82NTg4M2E2Yzg1LndlYnAifX0sImhhc2giOiJhbWF6aW5nX2RheV9oYXBweV9iZWF1dGlmdWxfd29tYW5fd29ya2luZ19zZXJ2ZXJfY2FiaW5ldF9ob2xkaW5nX2hlcl9sYXB0b3BfNjU4ODNhNmM4NSIsImV4dCI6Ii53ZWJwIiwibWltZSI6ImltYWdlL3dlYnAiLCJzaXplIjo2MjUuNDUsInVybCI6Imh0dHBzOi8vY2RuLm1hcnV0aXRlY2guY29tL2FtYXppbmdfZGF5X2hhcHB5X2JlYXV0aWZ1bF93b21hbl93b3JraW5nX3NlcnZlcl9jYWJpbmV0X2hvbGRpbmdfaGVyX2xhcHRvcF82NTg4M2E2Yzg1LndlYnAiLCJwcmV2aWV3VXJsIjpudWxsLCJwcm92aWRlciI6IkBzdHJhcGktY29tbXVuaXR5L3N0cmFwaS1wcm92aWRlci11cGxvYWQtZ29vZ2xlLWNsb3VkLXN0b3JhZ2UiLCJwcm92aWRlcl9tZXRhZGF0YSI6bnVsbCwiY3JlYXRlZEF0IjoiMjAyNS0wNy0yM1QwOTo0MzoyMy41MzVaIiwidXBkYXRlZEF0IjoiMjAyNS0wNy0yM1QwOTo0MzoyMy41MzVaIn19fX19fSwibWV0YSI6e319", "status": 200, "url": "https://dev-content.marutitech.com/api/cloud-migration-cost-calculator?populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema"}, "revalidate": 31536000, "tags": []}