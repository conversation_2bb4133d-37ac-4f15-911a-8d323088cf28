{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "true", "access-control-allow-origin": "", "connection": "keep-alive", "content-length": "10819", "content-security-policy": "script-src 'self' 'unsafe-inline' cdn.marutitech.com;img-src 'self' data: strapi.io cdn.marutitech.com storage.googleapis.com cdn-gcp.new.marutitech.com;media-src cdn.marutitech.com cdn-gcp.new.marutitech.com blob:;connect-src 'self' https:;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "content-type": "application/json; charset=utf-8", "date": "Thu, 31 Jul 2025 09:54:01 GMT", "referrer-policy": "no-referrer", "server": "nginx/1.24.0", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-powered-by": "Strapi <strapi.io>"}, "body": "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", "status": 200, "url": "https://dev-content.marutitech.com/api/cloud-migration-cost-calculator?populate=hero_section.image,hero_section.mobile_image,cloud_migration_components.question.answers,cloud_migration_components.question.sub_question,cloud_migration_components.questions.answers,cloud_migration_components.questions.sub_question,form.formFields,form.button,restart_button,consultation_button,tag_list,tag,seo.schema"}, "revalidate": 31536000, "tags": []}