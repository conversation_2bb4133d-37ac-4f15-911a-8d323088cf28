"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-cookie-consent";
exports.ids = ["vendor-chunks/react-cookie-consent"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConditionalWrapper: () => (/* binding */ ConditionalWrapper),\n/* harmony export */   CookieConsent: () => (/* binding */ CookieConsent),\n/* harmony export */   Cookies: () => (/* reexport default from dynamic */ js_cookie__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   OPTIONS: () => (/* binding */ POSITION_OPTIONS),\n/* harmony export */   POSITION_OPTIONS: () => (/* binding */ POSITION_OPTIONS),\n/* harmony export */   SAME_SITE_OPTIONS: () => (/* binding */ SAME_SITE_OPTIONS),\n/* harmony export */   VISIBILITY_OPTIONS: () => (/* binding */ VISIBILITY_OPTIONS),\n/* harmony export */   VISIBLE_OPTIONS: () => (/* binding */ VISIBILITY_OPTIONS),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultCookieConsentName: () => (/* binding */ defaultCookieConsentName),\n/* harmony export */   getCookieConsentValue: () => (/* binding */ getCookieConsentValue),\n/* harmony export */   getLegacyCookieName: () => (/* binding */ getLegacyCookieName),\n/* harmony export */   resetCookieConsentValue: () => (/* binding */ resetCookieConsentValue)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/../../node_modules/js-cookie/src/js.cookie.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(js_cookie__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n/**\r\n * A function to wrap elements with a \"wrapper\" on a condition\r\n * @param {object} wrappingOptions\r\n *  condition == boolean condition, when to wrap\r\n *  wrapper == style to wrap. e.g <div>{children}</div>\r\n *  children == react passes whatever is between tags as children. Don't supply this yourself.\r\n */ var ConditionalWrapper = function ConditionalWrapper(_ref) {\n    var condition = _ref.condition, wrapper = _ref.wrapper, children = _ref.children;\n    return condition ? wrapper(children) : children;\n};\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nvar POSITION_OPTIONS = {\n    TOP: \"top\",\n    BOTTOM: \"bottom\",\n    NONE: \"none\"\n};\nvar SAME_SITE_OPTIONS;\n(function(SAME_SITE_OPTIONS) {\n    SAME_SITE_OPTIONS[\"STRICT\"] = \"strict\";\n    SAME_SITE_OPTIONS[\"LAX\"] = \"lax\";\n    SAME_SITE_OPTIONS[\"NONE\"] = \"none\";\n})(SAME_SITE_OPTIONS || (SAME_SITE_OPTIONS = {}));\nvar VISIBILITY_OPTIONS = {\n    HIDDEN: \"hidden\",\n    SHOW: \"show\",\n    BY_COOKIE_VALUE: \"byCookieValue\"\n};\nvar defaultCookieConsentName = \"CookieConsent\";\nvar _excluded = [\n    \"children\"\n];\nvar DefaultButtonComponent = function DefaultButtonComponent(_ref) {\n    var children = _ref.children, props = _objectWithoutPropertiesLoose(_ref, _excluded);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"button\", Object.assign({}, props), children);\n};\nvar defaultCookieConsentProps = {\n    disableStyles: false,\n    hideOnAccept: true,\n    hideOnDecline: true,\n    location: POSITION_OPTIONS.BOTTOM,\n    visible: VISIBILITY_OPTIONS.BY_COOKIE_VALUE,\n    onAccept: function onAccept(_acceptedByScrolling) {},\n    onDecline: function onDecline() {},\n    cookieName: defaultCookieConsentName,\n    cookieValue: \"true\",\n    declineCookieValue: \"false\",\n    setDeclineCookie: true,\n    buttonText: \"I understand\",\n    declineButtonText: \"I decline\",\n    debug: false,\n    expires: 365,\n    containerClasses: \"CookieConsent\",\n    contentClasses: \"\",\n    buttonClasses: \"\",\n    buttonWrapperClasses: \"\",\n    declineButtonClasses: \"\",\n    buttonId: \"rcc-confirm-button\",\n    declineButtonId: \"rcc-decline-button\",\n    extraCookieOptions: {},\n    disableButtonStyles: false,\n    enableDeclineButton: false,\n    flipButtons: false,\n    sameSite: SAME_SITE_OPTIONS.LAX,\n    ButtonComponent: DefaultButtonComponent,\n    overlay: false,\n    overlayClasses: \"\",\n    onOverlayClick: function onOverlayClick() {},\n    acceptOnOverlayClick: false,\n    ariaAcceptLabel: \"Accept cookies\",\n    ariaDeclineLabel: \"Decline cookies\",\n    acceptOnScroll: false,\n    acceptOnScrollPercentage: 25,\n    customContentAttributes: {},\n    customContainerAttributes: {},\n    customButtonProps: {},\n    customDeclineButtonProps: {},\n    customButtonWrapperAttributes: {},\n    style: {},\n    buttonStyle: {},\n    declineButtonStyle: {},\n    contentStyle: {},\n    overlayStyle: {}\n};\nvar defaultState = {\n    visible: false,\n    style: {\n        alignItems: \"baseline\",\n        background: \"#353535\",\n        color: \"white\",\n        display: \"flex\",\n        flexWrap: \"wrap\",\n        justifyContent: \"space-between\",\n        left: \"0\",\n        position: \"fixed\",\n        width: \"100%\",\n        zIndex: \"999\"\n    },\n    buttonStyle: {\n        background: \"#ffd42d\",\n        border: \"0\",\n        borderRadius: \"0px\",\n        boxShadow: \"none\",\n        color: \"black\",\n        cursor: \"pointer\",\n        flex: \"0 0 auto\",\n        padding: \"5px 10px\",\n        margin: \"15px\"\n    },\n    declineButtonStyle: {\n        background: \"#c12a2a\",\n        border: \"0\",\n        borderRadius: \"0px\",\n        boxShadow: \"none\",\n        color: \"#e5e5e5\",\n        cursor: \"pointer\",\n        flex: \"0 0 auto\",\n        padding: \"5px 10px\",\n        margin: \"15px\"\n    },\n    contentStyle: {\n        flex: \"1 0 300px\",\n        margin: \"15px\"\n    },\n    overlayStyle: {\n        position: \"fixed\",\n        left: 0,\n        top: 0,\n        width: \"100%\",\n        height: \"100%\",\n        zIndex: \"999\",\n        backgroundColor: \"rgba(0,0,0,0.3)\"\n    }\n};\n/**\r\n * Returns the value of the consent cookie\r\n * Retrieves the regular value first and if not found the legacy one according\r\n * to: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\r\n * @param {*} name optional name of the cookie\r\n */ var getCookieConsentValue = function getCookieConsentValue(name) {\n    if (name === void 0) {\n        name = defaultCookieConsentName;\n    }\n    var cookieValue = js_cookie__WEBPACK_IMPORTED_MODULE_0___default().get(name);\n    // if the cookieValue is undefined check for the legacy cookie\n    if (cookieValue === undefined) {\n        return js_cookie__WEBPACK_IMPORTED_MODULE_0___default().get(getLegacyCookieName(name));\n    }\n    return cookieValue;\n};\n/**\r\n * Reset the consent cookie\r\n * Remove the cookie on browser in order to allow user to change their consent\r\n * @param {*} name optional name of the cookie\r\n */ var resetCookieConsentValue = function resetCookieConsentValue(name) {\n    if (name === void 0) {\n        name = defaultCookieConsentName;\n    }\n    js_cookie__WEBPACK_IMPORTED_MODULE_0___default().remove(name);\n};\n/**\r\n * Get the legacy cookie name by the regular cookie name\r\n * @param {string} name of cookie to get\r\n */ var getLegacyCookieName = function getLegacyCookieName(name) {\n    return name + \"-legacy\";\n};\nvar CookieConsent = /*#__PURE__*/ function(_Component) {\n    _inheritsLoose(CookieConsent, _Component);\n    function CookieConsent() {\n        var _this;\n        _this = _Component.apply(this, arguments) || this;\n        _this.state = defaultState;\n        /**\r\n     * checks whether scroll has exceeded set amount and fire accept if so.\r\n     */ _this.handleScroll = function() {\n            var _defaultCookieConsent = _extends({}, defaultCookieConsentProps, _this.props), acceptOnScrollPercentage = _defaultCookieConsent.acceptOnScrollPercentage;\n            // (top / height) - height * 100\n            var rootNode = document.documentElement;\n            var body = document.body;\n            var top = \"scrollTop\";\n            var height = \"scrollHeight\";\n            var percentage = (rootNode[top] || body[top]) / ((rootNode[height] || body[height]) - rootNode.clientHeight) * 100;\n            if (percentage > acceptOnScrollPercentage) {\n                _this.accept(true);\n            }\n        };\n        _this.removeScrollListener = function() {\n            var acceptOnScroll = _this.props.acceptOnScroll;\n            if (acceptOnScroll) {\n                window.removeEventListener(\"scroll\", _this.handleScroll);\n            }\n        };\n        return _this;\n    }\n    var _proto = CookieConsent.prototype;\n    _proto.componentDidMount = function componentDidMount() {\n        var debug = this.props.debug;\n        // if cookie undefined or debug\n        if (this.getCookieValue() === undefined || debug) {\n            this.setState({\n                visible: true\n            });\n            // if acceptOnScroll is set to true and (cookie is undefined or debug is set to true), add a listener.\n            if (this.props.acceptOnScroll) {\n                window.addEventListener(\"scroll\", this.handleScroll, {\n                    passive: true\n                });\n            }\n        }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n        // remove listener if still set\n        this.removeScrollListener();\n    } /**\r\n   * Set a persistent accept cookie\r\n   */ ;\n    _proto.accept = function accept(acceptedByScrolling) {\n        var _acceptedByScrolling;\n        if (acceptedByScrolling === void 0) {\n            acceptedByScrolling = false;\n        }\n        var _defaultCookieConsent2 = _extends({}, defaultCookieConsentProps, this.props), cookieName = _defaultCookieConsent2.cookieName, cookieValue = _defaultCookieConsent2.cookieValue, hideOnAccept = _defaultCookieConsent2.hideOnAccept, onAccept = _defaultCookieConsent2.onAccept;\n        this.setCookie(cookieName, cookieValue);\n        onAccept((_acceptedByScrolling = acceptedByScrolling) != null ? _acceptedByScrolling : false);\n        if (hideOnAccept) {\n            this.setState({\n                visible: false\n            });\n            this.removeScrollListener();\n        }\n    } /**\r\n   * Handle a click on the overlay\r\n   */ ;\n    _proto.overlayClick = function overlayClick() {\n        var _defaultCookieConsent3 = _extends({}, defaultCookieConsentProps, this.props), acceptOnOverlayClick = _defaultCookieConsent3.acceptOnOverlayClick, onOverlayClick = _defaultCookieConsent3.onOverlayClick;\n        if (acceptOnOverlayClick) {\n            this.accept();\n        }\n        onOverlayClick();\n    } /**\r\n   * Set a persistent decline cookie\r\n   */ ;\n    _proto.decline = function decline() {\n        var _defaultCookieConsent4 = _extends({}, defaultCookieConsentProps, this.props), cookieName = _defaultCookieConsent4.cookieName, declineCookieValue = _defaultCookieConsent4.declineCookieValue, hideOnDecline = _defaultCookieConsent4.hideOnDecline, onDecline = _defaultCookieConsent4.onDecline, setDeclineCookie = _defaultCookieConsent4.setDeclineCookie;\n        if (setDeclineCookie) {\n            this.setCookie(cookieName, declineCookieValue);\n        }\n        onDecline();\n        if (hideOnDecline) {\n            this.setState({\n                visible: false\n            });\n        }\n    } /**\r\n   * Function to set the consent cookie based on the provided variables\r\n   * Sets two cookies to handle incompatible browsers, more details:\r\n   * https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\r\n   */ ;\n    _proto.setCookie = function setCookie(cookieName, cookieValue) {\n        var _this$props = this.props, extraCookieOptions = _this$props.extraCookieOptions, expires = _this$props.expires, sameSite = _this$props.sameSite;\n        var cookieSecurity = this.props.cookieSecurity;\n        if (cookieSecurity === undefined) {\n            cookieSecurity = window.location ? window.location.protocol === \"https:\" : true;\n        }\n        var cookieOptions = _extends({\n            expires: expires\n        }, extraCookieOptions, {\n            sameSite: sameSite,\n            secure: cookieSecurity\n        });\n        // Fallback for older browsers where can not set SameSite=None,\n        // SEE: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\n        if (sameSite === SAME_SITE_OPTIONS.NONE) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0___default().set(getLegacyCookieName(cookieName), cookieValue, cookieOptions);\n        }\n        // set the regular cookie\n        js_cookie__WEBPACK_IMPORTED_MODULE_0___default().set(cookieName, cookieValue, cookieOptions);\n    } /**\r\n   * Returns the value of the consent cookie\r\n   * Retrieves the regular value first and if not found the legacy one according\r\n   * to: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\r\n   */ ;\n    _proto.getCookieValue = function getCookieValue() {\n        var cookieName = this.props.cookieName;\n        return getCookieConsentValue(cookieName);\n    };\n    _proto.render = function render() {\n        var _this2 = this;\n        // If the bar shouldn't be visible don't render anything.\n        switch(this.props.visible){\n            case VISIBILITY_OPTIONS.HIDDEN:\n                return null;\n            case VISIBILITY_OPTIONS.BY_COOKIE_VALUE:\n                if (!this.state.visible) {\n                    return null;\n                }\n                break;\n        }\n        var _this$props2 = this.props, location = _this$props2.location, style = _this$props2.style, buttonStyle = _this$props2.buttonStyle, declineButtonStyle = _this$props2.declineButtonStyle, contentStyle = _this$props2.contentStyle, disableStyles = _this$props2.disableStyles, buttonText = _this$props2.buttonText, declineButtonText = _this$props2.declineButtonText, containerClasses = _this$props2.containerClasses, contentClasses = _this$props2.contentClasses, buttonClasses = _this$props2.buttonClasses, buttonWrapperClasses = _this$props2.buttonWrapperClasses, declineButtonClasses = _this$props2.declineButtonClasses, buttonId = _this$props2.buttonId, declineButtonId = _this$props2.declineButtonId, disableButtonStyles = _this$props2.disableButtonStyles, enableDeclineButton = _this$props2.enableDeclineButton, flipButtons = _this$props2.flipButtons, ButtonComponent = _this$props2.ButtonComponent, overlay = _this$props2.overlay, overlayClasses = _this$props2.overlayClasses, overlayStyle = _this$props2.overlayStyle, ariaAcceptLabel = _this$props2.ariaAcceptLabel, ariaDeclineLabel = _this$props2.ariaDeclineLabel, customContainerAttributes = _this$props2.customContainerAttributes, customContentAttributes = _this$props2.customContentAttributes, customButtonProps = _this$props2.customButtonProps, customDeclineButtonProps = _this$props2.customDeclineButtonProps, customButtonWrapperAttributes = _this$props2.customButtonWrapperAttributes;\n        var myStyle = {};\n        var myButtonStyle = {};\n        var myDeclineButtonStyle = {};\n        var myContentStyle = {};\n        var myOverlayStyle = {};\n        if (disableStyles) {\n            // if styles are disabled use the provided styles (or none)\n            myStyle = Object.assign({}, style);\n            myButtonStyle = Object.assign({}, buttonStyle);\n            myDeclineButtonStyle = Object.assign({}, declineButtonStyle);\n            myContentStyle = Object.assign({}, contentStyle);\n            myOverlayStyle = Object.assign({}, overlayStyle);\n        } else {\n            // if styles aren't disabled merge them with the styles that are provided (or use default styles)\n            myStyle = Object.assign({}, _extends({}, this.state.style, style));\n            myContentStyle = Object.assign({}, _extends({}, this.state.contentStyle, contentStyle));\n            myOverlayStyle = Object.assign({}, _extends({}, this.state.overlayStyle, overlayStyle));\n            // switch to disable JUST the button styles\n            if (disableButtonStyles) {\n                myButtonStyle = Object.assign({}, buttonStyle);\n                myDeclineButtonStyle = Object.assign({}, declineButtonStyle);\n            } else {\n                myButtonStyle = Object.assign({}, _extends({}, this.state.buttonStyle, buttonStyle));\n                myDeclineButtonStyle = Object.assign({}, _extends({}, this.state.declineButtonStyle, declineButtonStyle));\n            }\n        }\n        // syntactic sugar to enable user to easily select top / bottom\n        switch(location){\n            case POSITION_OPTIONS.TOP:\n                myStyle.top = \"0\";\n                break;\n            case POSITION_OPTIONS.BOTTOM:\n                myStyle.bottom = \"0\";\n                break;\n        }\n        var buttonsToRender = [];\n        // add decline button\n        enableDeclineButton && buttonsToRender.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ButtonComponent, Object.assign({\n            key: \"declineButton\",\n            style: myDeclineButtonStyle,\n            className: declineButtonClasses,\n            id: declineButtonId,\n            \"aria-label\": ariaDeclineLabel,\n            onClick: function onClick() {\n                _this2.decline();\n            }\n        }, customDeclineButtonProps), declineButtonText));\n        // add accept button\n        buttonsToRender.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ButtonComponent, Object.assign({\n            key: \"acceptButton\",\n            style: myButtonStyle,\n            className: buttonClasses,\n            id: buttonId,\n            \"aria-label\": ariaAcceptLabel,\n            onClick: function onClick() {\n                _this2.accept();\n            }\n        }, customButtonProps), buttonText));\n        if (flipButtons) {\n            buttonsToRender.reverse();\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ConditionalWrapper, {\n            condition: overlay,\n            wrapper: function wrapper(children) {\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n                    style: myOverlayStyle,\n                    className: overlayClasses,\n                    onClick: function onClick() {\n                        _this2.overlayClick();\n                    }\n                }, children);\n            }\n        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n            className: \"\" + containerClasses,\n            style: myStyle\n        }, customContainerAttributes), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n            style: myContentStyle,\n            className: contentClasses\n        }, customContentAttributes), this.props.children), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n            className: \"\" + buttonWrapperClasses\n        }, customButtonWrapperAttributes), buttonsToRender.map(function(button) {\n            return button;\n        }))));\n    };\n    return CookieConsent;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\nCookieConsent.defaultProps = defaultCookieConsentProps;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n //# sourceMappingURL=react-cookie-consent.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js\n");

/***/ })

};
;