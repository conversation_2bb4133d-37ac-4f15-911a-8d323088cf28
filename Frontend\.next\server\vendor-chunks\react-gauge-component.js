"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-gauge-component";
exports.ids = ["vendor-chunks/react-gauge-component"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.CONSTANTS = void 0;\nexports.CONSTANTS = {\n    arcTooltipClassname: \"gauge-component-arc-tooltip\",\n    tickLineClassname: \"tick-line\",\n    tickValueClassname: \"tick-value\",\n    valueLabelClassname: \"value-text\",\n    debugTicksRadius: false,\n    debugSingleGauge: false,\n    rangeBetweenCenteredTickValueLabel: [\n        0.35,\n        0.65\n    ]\n};\nexports[\"default\"] = exports.CONSTANTS;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9HYXVnZUNvbXBvbmVudC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELGlCQUFpQixHQUFHLEtBQUs7QUFDekJBLGlCQUFpQixHQUFHO0lBQ2hCRyxxQkFBcUI7SUFDckJDLG1CQUFtQjtJQUNuQkMsb0JBQW9CO0lBQ3BCQyxxQkFBcUI7SUFDckJDLGtCQUFrQjtJQUNsQkMsa0JBQWtCO0lBQ2xCQyxvQ0FBb0M7UUFBQztRQUFNO0tBQUs7QUFDcEQ7QUFDQVQsa0JBQWUsR0FBR0EsUUFBUUUsU0FBUyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1nYXVnZS1jb21wb25lbnQvZGlzdC9saWIvR2F1Z2VDb21wb25lbnQvY29uc3RhbnRzLmpzP2Q0YmYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkNPTlNUQU5UUyA9IHZvaWQgMDtcbmV4cG9ydHMuQ09OU1RBTlRTID0ge1xuICAgIGFyY1Rvb2x0aXBDbGFzc25hbWU6IFwiZ2F1Z2UtY29tcG9uZW50LWFyYy10b29sdGlwXCIsXG4gICAgdGlja0xpbmVDbGFzc25hbWU6IFwidGljay1saW5lXCIsXG4gICAgdGlja1ZhbHVlQ2xhc3NuYW1lOiBcInRpY2stdmFsdWVcIixcbiAgICB2YWx1ZUxhYmVsQ2xhc3NuYW1lOiBcInZhbHVlLXRleHRcIixcbiAgICBkZWJ1Z1RpY2tzUmFkaXVzOiBmYWxzZSxcbiAgICBkZWJ1Z1NpbmdsZUdhdWdlOiBmYWxzZSxcbiAgICByYW5nZUJldHdlZW5DZW50ZXJlZFRpY2tWYWx1ZUxhYmVsOiBbMC4zNSwgMC42NV1cbn07XG5leHBvcnRzLmRlZmF1bHQgPSBleHBvcnRzLkNPTlNUQU5UUztcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIkNPTlNUQU5UUyIsImFyY1Rvb2x0aXBDbGFzc25hbWUiLCJ0aWNrTGluZUNsYXNzbmFtZSIsInRpY2tWYWx1ZUNsYXNzbmFtZSIsInZhbHVlTGFiZWxDbGFzc25hbWUiLCJkZWJ1Z1RpY2tzUmFkaXVzIiwiZGVidWdTaW5nbGVHYXVnZSIsInJhbmdlQmV0d2VlbkNlbnRlcmVkVGlja1ZhbHVlTGFiZWwiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.validateArcs = exports.clearOuterArcs = exports.clearArcs = exports.redrawArcs = exports.getCoordByValue = exports.createGradientElement = exports.getColors = exports.applyGradientColors = exports.getArcDataByPercentage = exports.getArcDataByValue = exports.applyColors = exports.setupTooltip = exports.setupArcs = exports.drawArc = exports.setArcData = exports.hideTooltip = void 0;\nvar utils = __importStar(__webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\"));\nvar d3_1 = __webpack_require__(/*! d3 */ \"(ssr)/../../node_modules/d3/src/index.js\");\nvar arcHooks = __importStar(__webpack_require__(/*! ./arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar constants_1 = __importDefault(__webpack_require__(/*! ../constants */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js\"));\nvar Tooltip_1 = __webpack_require__(/*! ../types/Tooltip */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js\");\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar lodash_1 = __webpack_require__(/*! lodash */ \"(ssr)/../../node_modules/lodash/lodash.js\");\nvar onArcMouseMove = function(event, d, gauge) {\n    //event.target.style.stroke = \"#ffffff5e\";\n    if (d.data.tooltip != undefined) {\n        var shouldChangeText = d.data.tooltip.text != gauge.tooltip.current.text();\n        if (shouldChangeText) {\n            gauge.tooltip.current.html(d.data.tooltip.text).style(\"position\", \"absolute\").style(\"display\", \"block\").style(\"opacity\", 1);\n            applyTooltipStyles(d.data.tooltip, d.data.color, gauge);\n        }\n        gauge.tooltip.current.style(\"left\", event.pageX + 15 + \"px\").style(\"top\", event.pageY - 10 + \"px\");\n    }\n    if (d.data.onMouseMove != undefined) d.data.onMouseMove(event);\n};\nvar applyTooltipStyles = function(tooltip, arcColor, gauge) {\n    //Apply default styles\n    Object.entries(Tooltip_1.defaultTooltipStyle).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return gauge.tooltip.current.style(utils.camelCaseToKebabCase(key), value);\n    });\n    gauge.tooltip.current.style(\"background-color\", arcColor);\n    //Apply custom styles\n    if (tooltip.style != undefined) Object.entries(tooltip.style).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return gauge.tooltip.current.style(utils.camelCaseToKebabCase(key), value);\n    });\n};\nvar onArcMouseLeave = function(event, d, gauge, mousemoveCbThrottled) {\n    mousemoveCbThrottled.cancel();\n    (0, exports.hideTooltip)(gauge);\n    if (d.data.onMouseLeave != undefined) d.data.onMouseLeave(event);\n};\nvar hideTooltip = function(gauge) {\n    gauge.tooltip.current.html(\" \").style(\"display\", \"none\");\n};\nexports.hideTooltip = hideTooltip;\nvar onArcMouseOut = function(event, d, gauge) {\n    event.target.style.stroke = \"none\";\n};\nvar onArcMouseClick = function(event, d) {\n    if (d.data.onMouseClick != undefined) d.data.onMouseClick(event);\n};\nvar setArcData = function(gauge) {\n    var _a, _b;\n    var arc = gauge.props.arc;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    // Determine number of arcs to display\n    var nbArcsToDisplay = (arc === null || arc === void 0 ? void 0 : arc.nbSubArcs) || ((_a = arc === null || arc === void 0 ? void 0 : arc.subArcs) === null || _a === void 0 ? void 0 : _a.length) || 1;\n    var colorArray = (0, exports.getColors)(nbArcsToDisplay, gauge);\n    if ((arc === null || arc === void 0 ? void 0 : arc.subArcs) && !(arc === null || arc === void 0 ? void 0 : arc.nbSubArcs)) {\n        var lastSubArcLimit_1 = 0;\n        var lastSubArcLimitPercentageAcc_1 = 0;\n        var subArcsLength_1 = [];\n        var subArcsLimits_1 = [];\n        var subArcsTooltip_1 = [];\n        (_b = arc === null || arc === void 0 ? void 0 : arc.subArcs) === null || _b === void 0 ? void 0 : _b.forEach(function(subArc, i) {\n            var _a;\n            var subArcLength = 0;\n            //map limit for non defined subArcs limits\n            var subArcRange = 0;\n            var limit = subArc.limit;\n            if (subArc.length != undefined) {\n                subArcLength = subArc.length;\n                limit = utils.getCurrentGaugeValueByPercentage(subArcLength + lastSubArcLimitPercentageAcc_1, gauge);\n            } else if (subArc.limit == undefined) {\n                subArcRange = lastSubArcLimit_1;\n                var remainingPercentageEquallyDivided = undefined;\n                var remainingSubArcs = (_a = arc === null || arc === void 0 ? void 0 : arc.subArcs) === null || _a === void 0 ? void 0 : _a.slice(i);\n                var remainingPercentage = (1 - utils.calculatePercentage(minValue, maxValue, lastSubArcLimit_1)) * 100;\n                if (!remainingPercentageEquallyDivided) {\n                    remainingPercentageEquallyDivided = remainingPercentage / Math.max((remainingSubArcs === null || remainingSubArcs === void 0 ? void 0 : remainingSubArcs.length) || 1, 1) / 100;\n                }\n                limit = lastSubArcLimit_1 + remainingPercentageEquallyDivided * 100;\n                subArcLength = remainingPercentageEquallyDivided;\n            } else {\n                subArcRange = limit - lastSubArcLimit_1;\n                // Calculate arc length based on previous arc percentage\n                if (i !== 0) {\n                    subArcLength = utils.calculatePercentage(minValue, maxValue, limit) - lastSubArcLimitPercentageAcc_1;\n                } else {\n                    subArcLength = utils.calculatePercentage(minValue, maxValue, subArcRange);\n                }\n            }\n            subArcsLength_1.push(subArcLength);\n            subArcsLimits_1.push(limit);\n            lastSubArcLimitPercentageAcc_1 = subArcsLength_1.reduce(function(count, curr) {\n                return count + curr;\n            }, 0);\n            lastSubArcLimit_1 = limit;\n            if (subArc.tooltip != undefined) subArcsTooltip_1.push(subArc.tooltip);\n        });\n        var subArcs_1 = arc.subArcs;\n        gauge.arcData.current = subArcsLength_1.map(function(length, i) {\n            return {\n                value: length,\n                limit: subArcsLimits_1[i],\n                color: colorArray[i],\n                showTick: subArcs_1[i].showTick || false,\n                tooltip: subArcs_1[i].tooltip || undefined,\n                onMouseMove: subArcs_1[i].onMouseMove,\n                onMouseLeave: subArcs_1[i].onMouseLeave,\n                onMouseClick: subArcs_1[i].onClick\n            };\n        });\n    } else {\n        var arcValue_1 = maxValue / nbArcsToDisplay;\n        gauge.arcData.current = Array.from({\n            length: nbArcsToDisplay\n        }, function(_, i) {\n            return {\n                value: arcValue_1,\n                limit: (i + 1) * arcValue_1,\n                color: colorArray[i],\n                tooltip: undefined\n            };\n        });\n    }\n};\nexports.setArcData = setArcData;\nvar getGrafanaMainArcData = function(gauge, percent) {\n    var _a;\n    if (percent === void 0) {\n        percent = undefined;\n    }\n    var currentPercentage = percent != undefined ? percent : utils.calculatePercentage(gauge.props.minValue, gauge.props.maxValue, gauge.props.value);\n    var curArcData = (0, exports.getArcDataByPercentage)(currentPercentage, gauge);\n    var firstSubArc = {\n        value: currentPercentage,\n        //White indicate that no arc was found and work as an alert for debug\n        color: (curArcData === null || curArcData === void 0 ? void 0 : curArcData.color) || \"white\"\n    };\n    //This is the grey arc that will be displayed when the gauge is not full\n    var secondSubArc = {\n        value: 1 - currentPercentage,\n        color: (_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.emptyColor\n    };\n    return [\n        firstSubArc,\n        secondSubArc\n    ];\n};\nvar drawGrafanaOuterArc = function(gauge, resize) {\n    if (resize === void 0) {\n        resize = false;\n    }\n    var outerRadius = gauge.dimensions.current.outerRadius;\n    //Grafana's outer arc will be populates as the standard arc data would\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana && resize) {\n        gauge.doughnut.current.selectAll(\".outerSubArc\").remove();\n        var outerArc = (0, d3_1.arc)().outerRadius(outerRadius + 7).innerRadius(outerRadius + 2).cornerRadius(0).padAngle(0);\n        var arcPaths = gauge.doughnut.current.selectAll(\"anyString\").data(gauge.pieChart.current(gauge.arcData.current)).enter().append(\"g\").attr(\"class\", \"outerSubArc\");\n        var outerArcSubarcs = arcPaths.append(\"path\").attr(\"d\", outerArc);\n        (0, exports.applyColors)(outerArcSubarcs, gauge);\n        var mousemoveCbThrottled_1 = (0, lodash_1.throttle)(function(event, d) {\n            return onArcMouseMove(event, d, gauge);\n        }, 20);\n        arcPaths.on(\"mouseleave\", function(event, d) {\n            return onArcMouseLeave(event, d, gauge, mousemoveCbThrottled_1);\n        }).on(\"mouseout\", function(event, d) {\n            return onArcMouseOut(event, d, gauge);\n        }).on(\"mousemove\", mousemoveCbThrottled_1).on(\"click\", function(event, d) {\n            return onArcMouseClick(event, d);\n        });\n    }\n};\nvar drawArc = function(gauge, percent) {\n    var _a, _b;\n    if (percent === void 0) {\n        percent = undefined;\n    }\n    var _c = gauge.props.arc, padding = _c.padding, cornerRadius = _c.cornerRadius;\n    var _d = gauge.dimensions.current, innerRadius = _d.innerRadius, outerRadius = _d.outerRadius;\n    // chartHooks.clearChart(gauge);\n    var data = {};\n    //When gradient enabled, it'll have only 1 arc\n    if ((_b = (_a = gauge.props) === null || _a === void 0 ? void 0 : _a.arc) === null || _b === void 0 ? void 0 : _b.gradient) {\n        data = [\n            {\n                value: 1\n            }\n        ];\n    } else {\n        data = gauge.arcData.current;\n    }\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n        data = getGrafanaMainArcData(gauge, percent);\n    }\n    var arcPadding = gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana ? 0 : padding;\n    var arcCornerRadius = gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana ? 0 : cornerRadius;\n    var arcObj = (0, d3_1.arc)().outerRadius(outerRadius).innerRadius(innerRadius).cornerRadius(arcCornerRadius).padAngle(arcPadding);\n    var arcPaths = gauge.doughnut.current.selectAll(\"anyString\").data(gauge.pieChart.current(data)).enter().append(\"g\").attr(\"class\", \"subArc\");\n    var subArcs = arcPaths.append(\"path\").attr(\"d\", arcObj);\n    (0, exports.applyColors)(subArcs, gauge);\n    var mousemoveCbThrottled = (0, lodash_1.throttle)(function(event, d) {\n        return onArcMouseMove(event, d, gauge);\n    }, 20);\n    arcPaths.on(\"mouseleave\", function(event, d) {\n        return onArcMouseLeave(event, d, gauge, mousemoveCbThrottled);\n    }).on(\"mouseout\", function(event, d) {\n        return onArcMouseOut(event, d, gauge);\n    }).on(\"mousemove\", mousemoveCbThrottled).on(\"click\", function(event, d) {\n        return onArcMouseClick(event, d);\n    });\n};\nexports.drawArc = drawArc;\nvar setupArcs = function(gauge, resize) {\n    if (resize === void 0) {\n        resize = false;\n    }\n    //Setup the arc\n    (0, exports.setupTooltip)(gauge);\n    drawGrafanaOuterArc(gauge, resize);\n    (0, exports.drawArc)(gauge);\n};\nexports.setupArcs = setupArcs;\nvar setupTooltip = function(gauge) {\n    //Add tooltip\n    var isTooltipInTheDom = document.getElementsByClassName(constants_1.default.arcTooltipClassname).length != 0;\n    if (!isTooltipInTheDom) (0, d3_1.select)(\"body\").append(\"div\").attr(\"class\", constants_1.default.arcTooltipClassname);\n    gauge.tooltip.current = (0, d3_1.select)(\".\".concat(constants_1.default.arcTooltipClassname));\n    gauge.tooltip.current.on(\"mouseleave\", function() {\n        return arcHooks.hideTooltip(gauge);\n    }).on(\"mouseout\", function() {\n        return arcHooks.hideTooltip(gauge);\n    });\n};\nexports.setupTooltip = setupTooltip;\nvar applyColors = function(subArcsPath, gauge) {\n    var _a, _b;\n    if ((_b = (_a = gauge.props) === null || _a === void 0 ? void 0 : _a.arc) === null || _b === void 0 ? void 0 : _b.gradient) {\n        var uniqueId_1 = \"subArc-linear-gradient-\".concat(Math.random());\n        var gradEl = (0, exports.createGradientElement)(gauge.doughnut.current, uniqueId_1);\n        (0, exports.applyGradientColors)(gradEl, gauge);\n        subArcsPath.style(\"fill\", function(d) {\n            return \"url(#\".concat(uniqueId_1, \")\");\n        });\n    } else {\n        subArcsPath.style(\"fill\", function(d) {\n            return d.data.color;\n        });\n    }\n};\nexports.applyColors = applyColors;\nvar getArcDataByValue = function(value, gauge) {\n    return gauge.arcData.current.find(function(subArcData) {\n        return value <= subArcData.limit;\n    });\n};\nexports.getArcDataByValue = getArcDataByValue;\nvar getArcDataByPercentage = function(percentage, gauge) {\n    return (0, exports.getArcDataByValue)(utils.getCurrentGaugeValueByPercentage(percentage, gauge), gauge);\n};\nexports.getArcDataByPercentage = getArcDataByPercentage;\nvar applyGradientColors = function(gradEl, gauge) {\n    gauge.arcData.current.forEach(function(subArcData) {\n        var _a, _b, _c, _d;\n        var normalizedOffset = utils.normalize(subArcData === null || subArcData === void 0 ? void 0 : subArcData.limit, (_b = (_a = gauge === null || gauge === void 0 ? void 0 : gauge.props) === null || _a === void 0 ? void 0 : _a.minValue) !== null && _b !== void 0 ? _b : 0, (_d = (_c = gauge === null || gauge === void 0 ? void 0 : gauge.props) === null || _c === void 0 ? void 0 : _c.maxValue) !== null && _d !== void 0 ? _d : 100);\n        gradEl.append(\"stop\").attr(\"offset\", \"\".concat(normalizedOffset, \"%\")).style(\"stop-color\", subArcData.color) //end in red\n        .style(\"stop-opacity\", 1);\n    });\n};\nexports.applyGradientColors = applyGradientColors;\n//Depending on the number of levels in the chart\n//This function returns the same number of colors\nvar getColors = function(nbArcsToDisplay, gauge) {\n    var _a;\n    var arc = gauge.props.arc;\n    var colorsValue = [];\n    if (!arc.colorArray) {\n        var subArcColors = (_a = arc.subArcs) === null || _a === void 0 ? void 0 : _a.map(function(subArc) {\n            return subArc.color;\n        });\n        colorsValue = (subArcColors === null || subArcColors === void 0 ? void 0 : subArcColors.some(function(color) {\n            return color != undefined;\n        })) ? subArcColors : constants_1.default.defaultColors;\n    } else {\n        colorsValue = arc.colorArray;\n    }\n    //defaults colorsValue to white in order to avoid compilation error\n    if (!colorsValue) colorsValue = [\n        \"#fff\"\n    ];\n    //Check if the number of colors equals the number of levels\n    //Otherwise make an interpolation\n    var arcsEqualsColorsLength = nbArcsToDisplay === (colorsValue === null || colorsValue === void 0 ? void 0 : colorsValue.length);\n    if (arcsEqualsColorsLength) return colorsValue;\n    var colorScale = (0, d3_1.scaleLinear)().domain([\n        1,\n        nbArcsToDisplay\n    ])//@ts-ignore\n    .range([\n        colorsValue[0],\n        colorsValue[colorsValue.length - 1]\n    ]) //Use the first and the last color as range\n    //@ts-ignore\n    .interpolate(d3_1.interpolateHsl);\n    var colorArray = [];\n    for(var i = 1; i <= nbArcsToDisplay; i++){\n        colorArray.push(colorScale(i));\n    }\n    return colorArray;\n};\nexports.getColors = getColors;\nvar createGradientElement = function(div, uniqueId) {\n    //make defs and add the linear gradient\n    var lg = div.append(\"defs\").append(\"linearGradient\").attr(\"id\", uniqueId) //id of the gradient\n    .attr(\"x1\", \"0%\").attr(\"x2\", \"100%\").attr(\"y1\", \"0%\").attr(\"y2\", \"0%\");\n    return lg;\n};\nexports.createGradientElement = createGradientElement;\nvar getCoordByValue = function(value, gauge, position, centerToArcLengthSubtract, radiusFactor) {\n    var _a;\n    if (position === void 0) {\n        position = \"inner\";\n    }\n    if (centerToArcLengthSubtract === void 0) {\n        centerToArcLengthSubtract = 0;\n    }\n    if (radiusFactor === void 0) {\n        radiusFactor = 1;\n    }\n    var positionCenterToArcLength = {\n        \"outer\": function() {\n            return gauge.dimensions.current.outerRadius - centerToArcLengthSubtract + 2;\n        },\n        \"inner\": function() {\n            return gauge.dimensions.current.innerRadius * radiusFactor - centerToArcLengthSubtract + 9;\n        },\n        \"between\": function() {\n            var lengthBetweenOuterAndInner = gauge.dimensions.current.outerRadius - gauge.dimensions.current.innerRadius;\n            var middlePosition = gauge.dimensions.current.innerRadius + lengthBetweenOuterAndInner - 5;\n            return middlePosition;\n        }\n    };\n    var centerToArcLength = positionCenterToArcLength[position]();\n    // This normalizes the labels when distanceFromArc = 0 to be just touching the arcs \n    if (gauge.props.type === GaugeComponentProps_1.GaugeType.Grafana) {\n        centerToArcLength += 5;\n    } else if (gauge.props.type === GaugeComponentProps_1.GaugeType.Semicircle) {\n        centerToArcLength += -2;\n    }\n    var percent = utils.calculatePercentage(gauge.props.minValue, gauge.props.maxValue, value);\n    var gaugeTypesAngles = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Grafana] = {\n        startAngle: utils.degToRad(-23),\n        endAngle: utils.degToRad(203)\n    }, _a[GaugeComponentProps_1.GaugeType.Semicircle] = {\n        startAngle: utils.degToRad(0.9),\n        endAngle: utils.degToRad(179.1)\n    }, _a[GaugeComponentProps_1.GaugeType.Radial] = {\n        startAngle: utils.degToRad(-39),\n        endAngle: utils.degToRad(219)\n    }, _a);\n    var _b = gaugeTypesAngles[gauge.props.type], startAngle = _b.startAngle, endAngle = _b.endAngle;\n    var angle = startAngle + percent * (endAngle - startAngle);\n    var coordsRadius = 1 * (gauge.dimensions.current.width / 500);\n    var coord = [\n        0,\n        -coordsRadius / 2\n    ];\n    var coordMinusCenter = [\n        coord[0] - centerToArcLength * Math.cos(angle),\n        coord[1] - centerToArcLength * Math.sin(angle)\n    ];\n    var centerCoords = [\n        gauge.dimensions.current.outerRadius,\n        gauge.dimensions.current.outerRadius\n    ];\n    var x = centerCoords[0] + coordMinusCenter[0];\n    var y = centerCoords[1] + coordMinusCenter[1];\n    return {\n        x: x,\n        y: y\n    };\n};\nexports.getCoordByValue = getCoordByValue;\nvar redrawArcs = function(gauge) {\n    (0, exports.clearArcs)(gauge);\n    (0, exports.setArcData)(gauge);\n    (0, exports.setupArcs)(gauge);\n};\nexports.redrawArcs = redrawArcs;\nvar clearArcs = function(gauge) {\n    gauge.doughnut.current.selectAll(\".subArc\").remove();\n};\nexports.clearArcs = clearArcs;\nvar clearOuterArcs = function(gauge) {\n    gauge.doughnut.current.selectAll(\".outerSubArc\").remove();\n};\nexports.clearOuterArcs = clearOuterArcs;\nvar validateArcs = function(gauge) {\n    verifySubArcsLimits(gauge);\n};\nexports.validateArcs = validateArcs;\n/**\n * Reorders the subArcs within the gauge's arc property based on the limit property.\n * SubArcs with undefined limits are sorted last.\n*/ var reOrderSubArcs = function(gauge) {\n    var _a;\n    var subArcs = (_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.subArcs;\n    subArcs.sort(function(a, b) {\n        if (typeof a.limit === \"undefined\" && typeof b.limit === \"undefined\") {\n            return 0;\n        }\n        if (typeof a.limit === \"undefined\") {\n            return 1;\n        }\n        if (typeof b.limit === \"undefined\") {\n            return -1;\n        }\n        return a.limit - b.limit;\n    });\n};\nvar verifySubArcsLimits = function(gauge) {\n    var _a;\n    // disabled when length implemented.\n    // reOrderSubArcs(gauge);\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var arc = gauge.props.arc;\n    var subArcs = arc.subArcs;\n    var prevLimit = undefined;\n    for(var _i = 0, _b = ((_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.subArcs) || []; _i < _b.length; _i++){\n        var subArc = _b[_i];\n        var limit = subArc.limit;\n        if (typeof limit !== \"undefined\") {\n            // Check if the limit is within the valid range\n            if (limit < minValue || limit > maxValue) throw new Error(\"The limit of a subArc must be between the minValue and maxValue. The limit of the subArc is \".concat(limit));\n            // Check if the limit is greater than the previous limit\n            if (typeof prevLimit !== \"undefined\") {\n                if (limit <= prevLimit) throw new Error(\"The limit of a subArc must be greater than the limit of the previous subArc. The limit of the subArc is \".concat(limit, '. If you\\'re trying to specify length in percent of the arc, use property \"length\". refer to: https://github.com/antoniolago/react-gauge-component'));\n            }\n            prevLimit = limit;\n        }\n    }\n    // If the user has defined subArcs, make sure the last subArc has a limit equal to the maxValue\n    if (subArcs.length > 0) {\n        var lastSubArc = subArcs[subArcs.length - 1];\n        if (lastSubArc.limit < maxValue) lastSubArc.limit = maxValue;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.clearChart = exports.centerGraph = exports.calculateRadius = exports.updateDimensions = exports.renderChart = exports.calculateAngles = exports.initChart = void 0;\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar arcHooks = __importStar(__webpack_require__(/*! ./arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar labelsHooks = __importStar(__webpack_require__(/*! ./labels */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js\"));\nvar pointerHooks = __importStar(__webpack_require__(/*! ./pointer */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js\"));\nvar initChart = function(gauge, isFirstRender) {\n    var _a, _b, _c, _d;\n    var angles = gauge.dimensions.current.angles;\n    if ((_b = (_a = gauge.resizeObserver) === null || _a === void 0 ? void 0 : _a.current) === null || _b === void 0 ? void 0 : _b.disconnect) {\n        (_d = (_c = gauge.resizeObserver) === null || _c === void 0 ? void 0 : _c.current) === null || _d === void 0 ? void 0 : _d.disconnect();\n    }\n    var updatedValue = JSON.stringify(gauge.prevProps.current.value) !== JSON.stringify(gauge.props.value);\n    if (updatedValue && !isFirstRender) {\n        (0, exports.renderChart)(gauge, false);\n        return;\n    }\n    gauge.container.current.select(\"svg\").remove();\n    gauge.svg.current = gauge.container.current.append(\"svg\");\n    gauge.g.current = gauge.svg.current.append(\"g\"); //Used for margins\n    gauge.doughnut.current = gauge.g.current.append(\"g\").attr(\"class\", \"doughnut\");\n    //gauge.outerDougnut.current = gauge.g.current.append(\"g\").attr(\"class\", \"doughnut\");\n    (0, exports.calculateAngles)(gauge);\n    gauge.pieChart.current.value(function(d) {\n        return d.value;\n    })//.padAngle(15)\n    .startAngle(angles.startAngle).endAngle(angles.endAngle).sort(null);\n    //Set up pointer\n    pointerHooks.addPointerElement(gauge);\n    (0, exports.renderChart)(gauge, true);\n};\nexports.initChart = initChart;\nvar calculateAngles = function(gauge) {\n    var angles = gauge.dimensions.current.angles;\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle) {\n        angles.startAngle = -Math.PI / 2 + 0.02;\n        angles.endAngle = Math.PI / 2 - 0.02;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Radial) {\n        angles.startAngle = -Math.PI / 1.37;\n        angles.endAngle = Math.PI / 1.37;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n        angles.startAngle = -Math.PI / 1.6;\n        angles.endAngle = Math.PI / 1.6;\n    }\n};\nexports.calculateAngles = calculateAngles;\n//Renders the chart, should be called every time the window is resized\nvar renderChart = function(gauge, resize) {\n    var _a;\n    var _b, _c, _d, _e, _f;\n    if (resize === void 0) {\n        resize = false;\n    }\n    var dimensions = gauge.dimensions;\n    var arc = gauge.props.arc;\n    var labels = gauge.props.labels;\n    //if resize recalculate dimensions, clear chart and redraw\n    //if not resize, treat each prop separately\n    if (resize) {\n        (0, exports.updateDimensions)(gauge);\n        //Set dimensions of svg element and translations\n        gauge.g.current.attr(\"transform\", \"translate(\" + dimensions.current.margin.left + \", \" + 35 + \")\");\n        //Set the radius to lesser of width or height and remove the margins\n        //Calculate the new radius\n        (0, exports.calculateRadius)(gauge);\n        gauge.doughnut.current.attr(\"transform\", \"translate(\" + dimensions.current.outerRadius + \", \" + dimensions.current.outerRadius + \")\");\n        //Hide tooltip failsafe (sometimes subarcs events are not fired)\n        gauge.doughnut.current.on(\"mouseleave\", function() {\n            return arcHooks.hideTooltip(gauge);\n        }).on(\"mouseout\", function() {\n            return arcHooks.hideTooltip(gauge);\n        });\n        var arcWidth = arc.width;\n        dimensions.current.innerRadius = dimensions.current.outerRadius * (1 - arcWidth);\n        (0, exports.clearChart)(gauge);\n        arcHooks.setArcData(gauge);\n        arcHooks.setupArcs(gauge, resize);\n        labelsHooks.setupLabels(gauge);\n        if (!((_c = (_b = gauge.props) === null || _b === void 0 ? void 0 : _b.pointer) === null || _c === void 0 ? void 0 : _c.hide)) pointerHooks.drawPointer(gauge, resize);\n        var gaugeTypeHeightCorrection = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Semicircle] = 50, _a[GaugeComponentProps_1.GaugeType.Radial] = 55, _a[GaugeComponentProps_1.GaugeType.Grafana] = 55, _a);\n        var boundHeight = gauge.doughnut.current.node().getBoundingClientRect().height;\n        var boundWidth = gauge.container.current.node().getBoundingClientRect().width;\n        var gaugeType = gauge.props.type;\n        gauge.svg.current.attr(\"width\", boundWidth).attr(\"height\", boundHeight + gaugeTypeHeightCorrection[gaugeType]);\n    } else {\n        var arcsPropsChanged = JSON.stringify(gauge.prevProps.current.arc) !== JSON.stringify(gauge.props.arc);\n        var pointerPropsChanged = JSON.stringify(gauge.prevProps.current.pointer) !== JSON.stringify(gauge.props.pointer);\n        var valueChanged = JSON.stringify(gauge.prevProps.current.value) !== JSON.stringify(gauge.props.value);\n        var ticksChanged = JSON.stringify((_d = gauge.prevProps.current.labels) === null || _d === void 0 ? void 0 : _d.tickLabels) !== JSON.stringify(labels.tickLabels);\n        var shouldRedrawArcs = arcsPropsChanged;\n        if (shouldRedrawArcs) {\n            arcHooks.clearArcs(gauge);\n            arcHooks.setArcData(gauge);\n            arcHooks.setupArcs(gauge, resize);\n        }\n        //If pointer is hidden there's no need to redraw it when only value changes\n        var shouldRedrawPointer = pointerPropsChanged || valueChanged && !((_f = (_e = gauge.props) === null || _e === void 0 ? void 0 : _e.pointer) === null || _f === void 0 ? void 0 : _f.hide);\n        if (shouldRedrawPointer) {\n            pointerHooks.drawPointer(gauge);\n        }\n        if (arcsPropsChanged || ticksChanged) {\n            labelsHooks.clearTicks(gauge);\n            labelsHooks.setupTicks(gauge);\n        }\n        if (valueChanged) {\n            labelsHooks.clearValueLabel(gauge);\n            labelsHooks.setupValueLabel(gauge);\n        }\n    }\n};\nexports.renderChart = renderChart;\nvar updateDimensions = function(gauge) {\n    var marginInPercent = gauge.props.marginInPercent;\n    var dimensions = gauge.dimensions;\n    var divDimensions = gauge.container.current.node().getBoundingClientRect(), divWidth = divDimensions.width, divHeight = divDimensions.height;\n    if (dimensions.current.fixedHeight == 0) dimensions.current.fixedHeight = divHeight + 200;\n    //Set the new width and horizontal margins\n    var isMarginBox = typeof marginInPercent == \"number\";\n    var marginLeft = isMarginBox ? marginInPercent : marginInPercent.left;\n    var marginRight = isMarginBox ? marginInPercent : marginInPercent.right;\n    var marginTop = isMarginBox ? marginInPercent : marginInPercent.top;\n    var marginBottom = isMarginBox ? marginInPercent : marginInPercent.bottom;\n    dimensions.current.margin.left = divWidth * marginLeft;\n    dimensions.current.margin.right = divWidth * marginRight;\n    dimensions.current.width = divWidth - dimensions.current.margin.left - dimensions.current.margin.right;\n    dimensions.current.margin.top = dimensions.current.fixedHeight * marginTop;\n    dimensions.current.margin.bottom = dimensions.current.fixedHeight * marginBottom;\n    dimensions.current.height = dimensions.current.width / 2 - dimensions.current.margin.top - dimensions.current.margin.bottom;\n//gauge.height.current = divHeight - dimensions.current.margin.top - dimensions.current.margin.bottom;\n};\nexports.updateDimensions = updateDimensions;\nvar calculateRadius = function(gauge) {\n    var dimensions = gauge.dimensions;\n    //The radius needs to be constrained by the containing div\n    //Since it is a half circle we are dealing with the height of the div\n    //Only needs to be half of the width, because the width needs to be 2 * radius\n    //For the whole arc to fit\n    //First check if it is the width or the height that is the \"limiting\" dimension\n    if (dimensions.current.width < 2 * dimensions.current.height) {\n        //Then the width limits the size of the chart\n        //Set the radius to the width - the horizontal margins\n        dimensions.current.outerRadius = (dimensions.current.width - dimensions.current.margin.left - dimensions.current.margin.right) / 2;\n    } else {\n        dimensions.current.outerRadius = dimensions.current.height - dimensions.current.margin.top - dimensions.current.margin.bottom + 35;\n    }\n    (0, exports.centerGraph)(gauge);\n};\nexports.calculateRadius = calculateRadius;\n//Calculates new margins to make the graph centered\nvar centerGraph = function(gauge) {\n    var dimensions = gauge.dimensions;\n    dimensions.current.margin.left = dimensions.current.width / 2 - dimensions.current.outerRadius + dimensions.current.margin.right;\n    gauge.g.current.attr(\"transform\", \"translate(\" + dimensions.current.margin.left + \", \" + dimensions.current.margin.top + \")\");\n};\nexports.centerGraph = centerGraph;\nvar clearChart = function(gauge) {\n    //Remove the old stuff\n    labelsHooks.clearTicks(gauge);\n    labelsHooks.clearValueLabel(gauge);\n    pointerHooks.clearPointerElement(gauge);\n    arcHooks.clearArcs(gauge);\n};\nexports.clearChart = clearChart;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __assign = (void 0) && (void 0).__assign || function() {\n    __assign = Object.assign || function(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.calculateAnchorAndAngleByValue = exports.clearTicks = exports.clearValueLabel = exports.addValueText = exports.addText = exports.getLabelCoordsByValue = exports.addTick = exports.addTickValue = exports.addTickLine = exports.mapTick = exports.addArcTicks = exports.setupTicks = exports.setupValueLabel = exports.setupLabels = void 0;\nvar utils = __importStar(__webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\"));\nvar constants_1 = __importDefault(__webpack_require__(/*! ../constants */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/constants.js\"));\nvar Tick_1 = __webpack_require__(/*! ../types/Tick */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js\");\nvar d3 = __importStar(__webpack_require__(/*! d3 */ \"(ssr)/../../node_modules/d3/src/index.js\"));\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar arc_1 = __webpack_require__(/*! ./arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\");\nvar setupLabels = function(gauge) {\n    (0, exports.setupValueLabel)(gauge);\n    (0, exports.setupTicks)(gauge);\n};\nexports.setupLabels = setupLabels;\nvar setupValueLabel = function(gauge) {\n    var _a;\n    var labels = gauge.props.labels;\n    if (!((_a = labels === null || labels === void 0 ? void 0 : labels.valueLabel) === null || _a === void 0 ? void 0 : _a.hide)) (0, exports.addValueText)(gauge);\n};\nexports.setupValueLabel = setupValueLabel;\nvar setupTicks = function(gauge) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n    var labels = gauge.props.labels;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    if (constants_1.default.debugTicksRadius) {\n        for(var index = 0; index < maxValue; index++){\n            var indexTick = (0, exports.mapTick)(index, gauge);\n            (0, exports.addTick)(indexTick, gauge);\n        }\n    } else if (!((_a = labels.tickLabels) === null || _a === void 0 ? void 0 : _a.hideMinMax)) {\n        var alreadyHaveMinValueTick = (_c = (_b = labels.tickLabels) === null || _b === void 0 ? void 0 : _b.ticks) === null || _c === void 0 ? void 0 : _c.some(function(tick) {\n            return tick.value == minValue;\n        });\n        if (!alreadyHaveMinValueTick) {\n            //Add min value tick\n            var minValueTick = (0, exports.mapTick)(minValue, gauge);\n            (0, exports.addTick)(minValueTick, gauge);\n        }\n        var alreadyHaveMaxValueTick = (_e = (_d = labels.tickLabels) === null || _d === void 0 ? void 0 : _d.ticks) === null || _e === void 0 ? void 0 : _e.some(function(tick) {\n            return tick.value == maxValue;\n        });\n        if (!alreadyHaveMaxValueTick) {\n            // //Add max value tick\n            var maxValueTick = (0, exports.mapTick)(maxValue, gauge);\n            (0, exports.addTick)(maxValueTick, gauge);\n        }\n    }\n    if (((_g = (_f = labels.tickLabels) === null || _f === void 0 ? void 0 : _f.ticks) === null || _g === void 0 ? void 0 : _g.length) > 0) {\n        (_j = (_h = labels.tickLabels) === null || _h === void 0 ? void 0 : _h.ticks) === null || _j === void 0 ? void 0 : _j.forEach(function(tick) {\n            (0, exports.addTick)(tick, gauge);\n        });\n    }\n    (0, exports.addArcTicks)(gauge);\n};\nexports.setupTicks = setupTicks;\nvar addArcTicks = function(gauge) {\n    var _a;\n    (_a = gauge.arcData.current) === null || _a === void 0 ? void 0 : _a.map(function(subArc) {\n        if (subArc.showTick) return subArc.limit;\n    }).forEach(function(tickValue) {\n        if (tickValue) (0, exports.addTick)((0, exports.mapTick)(tickValue, gauge), gauge);\n    });\n};\nexports.addArcTicks = addArcTicks;\nvar mapTick = function(value, gauge) {\n    var tickLabels = gauge.props.labels.tickLabels;\n    return {\n        value: value,\n        valueConfig: tickLabels === null || tickLabels === void 0 ? void 0 : tickLabels.defaultTickValueConfig,\n        lineConfig: tickLabels === null || tickLabels === void 0 ? void 0 : tickLabels.defaultTickLineConfig\n    };\n};\nexports.mapTick = mapTick;\nvar addTickLine = function(tick, gauge) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;\n    var labels = gauge.props.labels;\n    var _u = (0, exports.calculateAnchorAndAngleByValue)(tick === null || tick === void 0 ? void 0 : tick.value, gauge), tickAnchor = _u.tickAnchor, angle = _u.angle;\n    var tickDistanceFromArc = ((_a = tick.lineConfig) === null || _a === void 0 ? void 0 : _a.distanceFromArc) || ((_c = (_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.defaultTickLineConfig) === null || _c === void 0 ? void 0 : _c.distanceFromArc) || 0;\n    if (((_e = (_d = gauge.props.labels) === null || _d === void 0 ? void 0 : _d.tickLabels) === null || _e === void 0 ? void 0 : _e.type) == \"outer\") tickDistanceFromArc = -tickDistanceFromArc;\n    // else tickDistanceFromArc = tickDistanceFromArc - 10;\n    var coords = (0, exports.getLabelCoordsByValue)(tick === null || tick === void 0 ? void 0 : tick.value, gauge, tickDistanceFromArc);\n    var tickColor = ((_f = tick.lineConfig) === null || _f === void 0 ? void 0 : _f.color) || ((_h = (_g = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _g === void 0 ? void 0 : _g.defaultTickLineConfig) === null || _h === void 0 ? void 0 : _h.color) || ((_j = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _j === void 0 ? void 0 : _j.color);\n    var tickWidth = ((_k = tick.lineConfig) === null || _k === void 0 ? void 0 : _k.width) || ((_m = (_l = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _l === void 0 ? void 0 : _l.defaultTickLineConfig) === null || _m === void 0 ? void 0 : _m.width) || ((_o = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _o === void 0 ? void 0 : _o.width);\n    var tickLength = ((_p = tick.lineConfig) === null || _p === void 0 ? void 0 : _p.length) || ((_r = (_q = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _q === void 0 ? void 0 : _q.defaultTickLineConfig) === null || _r === void 0 ? void 0 : _r.length) || ((_s = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _s === void 0 ? void 0 : _s.length);\n    // Calculate the end coordinates based on the adjusted position\n    var endX;\n    var endY;\n    // When inner should draw from outside to inside\n    // When outer should draw from inside to outside\n    if (((_t = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _t === void 0 ? void 0 : _t.type) == \"inner\") {\n        endX = coords.x + tickLength * Math.cos(angle * Math.PI / 180);\n        endY = coords.y + tickLength * Math.sin(angle * Math.PI / 180);\n    } else {\n        endX = coords.x - tickLength * Math.cos(angle * Math.PI / 180);\n        endY = coords.y - tickLength * Math.sin(angle * Math.PI / 180);\n    }\n    // (gauge.dimensions.current.outerRadius - gauge.dimensions.current.innerRadius)\n    // Create a D3 line generator\n    var lineGenerator = d3.line();\n    var lineCoordinates;\n    // Define the line coordinates\n    lineCoordinates = [\n        [\n            coords.x,\n            coords.y\n        ],\n        [\n            endX,\n            endY\n        ]\n    ];\n    // Append a path element for the line\n    gauge.g.current.append(\"path\").datum(lineCoordinates).attr(\"class\", constants_1.default.tickLineClassname).attr(\"d\", lineGenerator)// .attr(\"transform\", `translate(${0}, ${0})`)\n    .attr(\"stroke\", tickColor).attr(\"stroke-width\", tickWidth).attr(\"fill\", \"none\");\n// .attr(\"stroke-linecap\", \"round\")\n// .attr(\"stroke-linejoin\", \"round\")\n// .attr(\"transform\", `rotate(${angle})`);\n};\nexports.addTickLine = addTickLine;\nvar addTickValue = function(tick, gauge) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;\n    var labels = gauge.props.labels;\n    var arc = gauge.props.arc;\n    var arcWidth = arc.width;\n    var tickValue = tick === null || tick === void 0 ? void 0 : tick.value;\n    var tickAnchor = (0, exports.calculateAnchorAndAngleByValue)(tickValue, gauge).tickAnchor;\n    var centerToArcLengthSubtract = 27 - arcWidth * 10;\n    var isInner = ((_a = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _a === void 0 ? void 0 : _a.type) == \"inner\";\n    if (!isInner) centerToArcLengthSubtract = arcWidth * 10 - 10;\n    else centerToArcLengthSubtract -= 10;\n    var tickDistanceFromArc = ((_b = tick.lineConfig) === null || _b === void 0 ? void 0 : _b.distanceFromArc) || ((_d = (_c = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _c === void 0 ? void 0 : _c.defaultTickLineConfig) === null || _d === void 0 ? void 0 : _d.distanceFromArc) || 0;\n    var tickLength = ((_e = tick.lineConfig) === null || _e === void 0 ? void 0 : _e.length) || ((_g = (_f = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _f === void 0 ? void 0 : _f.defaultTickLineConfig) === null || _g === void 0 ? void 0 : _g.length) || 0;\n    var _shouldHideTickLine = shouldHideTickLine(tick, gauge);\n    if (!_shouldHideTickLine) {\n        if (isInner) {\n            centerToArcLengthSubtract += tickDistanceFromArc;\n            centerToArcLengthSubtract += tickLength;\n        } else {\n            centerToArcLengthSubtract -= tickDistanceFromArc;\n            centerToArcLengthSubtract -= tickLength;\n        }\n    }\n    var coords = (0, exports.getLabelCoordsByValue)(tickValue, gauge, centerToArcLengthSubtract);\n    var tickValueStyle = ((_h = tick.valueConfig) === null || _h === void 0 ? void 0 : _h.style) || ((_k = (_j = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _j === void 0 ? void 0 : _j.defaultTickValueConfig) === null || _k === void 0 ? void 0 : _k.style) || {};\n    tickValueStyle = __assign({}, tickValueStyle);\n    var text = \"\";\n    var maxDecimalDigits = ((_l = tick.valueConfig) === null || _l === void 0 ? void 0 : _l.maxDecimalDigits) || ((_o = (_m = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _m === void 0 ? void 0 : _m.defaultTickValueConfig) === null || _o === void 0 ? void 0 : _o.maxDecimalDigits);\n    if ((_p = tick.valueConfig) === null || _p === void 0 ? void 0 : _p.formatTextValue) {\n        text = tick.valueConfig.formatTextValue(utils.floatingNumber(tickValue, maxDecimalDigits));\n    } else if ((_r = (_q = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _q === void 0 ? void 0 : _q.defaultTickValueConfig) === null || _r === void 0 ? void 0 : _r.formatTextValue) {\n        text = labels.tickLabels.defaultTickValueConfig.formatTextValue(utils.floatingNumber(tickValue, maxDecimalDigits));\n    } else if (gauge.props.minValue === 0 && gauge.props.maxValue === 100) {\n        text = utils.floatingNumber(tickValue, maxDecimalDigits).toString();\n        text += \"%\";\n    } else {\n        text = utils.floatingNumber(tickValue, maxDecimalDigits).toString();\n    }\n    if (((_s = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _s === void 0 ? void 0 : _s.type) == \"inner\") {\n        if (tickAnchor === \"end\") coords.x += 10;\n        if (tickAnchor === \"start\") coords.x -= 10;\n    // if (tickAnchor === \"middle\") coords.y -= 0;\n    } else {\n        // if(tickAnchor === \"end\") coords.x -= 10;\n        // if(tickAnchor === \"start\") coords.x += 10;\n        if (tickAnchor === \"middle\") coords.y += 2;\n    }\n    if (tickAnchor === \"middle\") {\n        coords.y += 0;\n    } else {\n        coords.y += 3;\n    }\n    tickValueStyle.textAnchor = tickAnchor;\n    (0, exports.addText)(text, coords.x, coords.y, gauge, tickValueStyle, constants_1.default.tickValueClassname);\n};\nexports.addTickValue = addTickValue;\nvar addTick = function(tick, gauge) {\n    var labels = gauge.props.labels;\n    //Make validation for sequence of values respecting DEFAULT -> DEFAULT FROM USER -> SPECIFIC TICK VALUE\n    var _shouldHideTickLine = shouldHideTickLine(tick, gauge);\n    var _shouldHideTickValue = shouldHideTickValue(tick, gauge);\n    if (!_shouldHideTickLine) (0, exports.addTickLine)(tick, gauge);\n    if (!constants_1.default.debugTicksRadius && !_shouldHideTickValue) {\n        (0, exports.addTickValue)(tick, gauge);\n    }\n};\nexports.addTick = addTick;\nvar getLabelCoordsByValue = function(value, gauge, centerToArcLengthSubtract) {\n    var _a;\n    if (centerToArcLengthSubtract === void 0) {\n        centerToArcLengthSubtract = 0;\n    }\n    var labels = gauge.props.labels;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var type = (_a = labels.tickLabels) === null || _a === void 0 ? void 0 : _a.type;\n    var _b = (0, arc_1.getCoordByValue)(value, gauge, type, centerToArcLengthSubtract, 0.93), x = _b.x, y = _b.y;\n    var percent = utils.calculatePercentage(minValue, maxValue, value);\n    //This corrects labels in the cener being too close from the arc\n    // let isValueBetweenCenter = percent > CONSTANTS.rangeBetweenCenteredTickValueLabel[0] && \n    //                               percent < CONSTANTS.rangeBetweenCenteredTickValueLabel[1];\n    // if (isValueBetweenCenter){\n    //   let isInner = type == \"inner\";\n    //   y+= isInner ? 8 : -1;\n    // }\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Radial) {\n        y += 3;\n    }\n    return {\n        x: x,\n        y: y\n    };\n};\nexports.getLabelCoordsByValue = getLabelCoordsByValue;\nvar addText = function(html, x, y, gauge, style, className, rotate) {\n    if (rotate === void 0) {\n        rotate = 0;\n    }\n    var div = gauge.g.current.append(\"g\").attr(\"class\", className).attr(\"transform\", \"translate(\".concat(x, \", \").concat(y, \")\")).append(\"text\").text(html); // use html() instead of text()\n    applyTextStyles(div, style);\n    div.attr(\"transform\", \"rotate(\".concat(rotate, \")\"));\n};\nexports.addText = addText;\nvar applyTextStyles = function(div, style) {\n    //Apply default styles\n    Object.entries(style).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return div.style(utils.camelCaseToKebabCase(key), value);\n    });\n    //Apply custom styles\n    if (style != undefined) Object.entries(style).forEach(function(_a) {\n        var key = _a[0], value = _a[1];\n        return div.style(utils.camelCaseToKebabCase(key), value);\n    });\n};\n//Adds text undeneath the graft to display which percentage is the current one\nvar addValueText = function(gauge) {\n    var _a, _b, _c;\n    var labels = gauge.props.labels;\n    var value = gauge.props.value;\n    var valueLabel = labels === null || labels === void 0 ? void 0 : labels.valueLabel;\n    var textPadding = 20;\n    var text = \"\";\n    var maxDecimalDigits = (_a = labels === null || labels === void 0 ? void 0 : labels.valueLabel) === null || _a === void 0 ? void 0 : _a.maxDecimalDigits;\n    var floatValue = utils.floatingNumber(value, maxDecimalDigits);\n    if (valueLabel.formatTextValue) {\n        text = valueLabel.formatTextValue(floatValue);\n    } else if (gauge.props.minValue === 0 && gauge.props.maxValue === 100) {\n        text = floatValue.toString();\n        text += \"%\";\n    } else {\n        text = floatValue.toString();\n    }\n    var maxLengthBeforeComputation = 4;\n    var textLength = (text === null || text === void 0 ? void 0 : text.length) || 0;\n    var fontRatio = textLength > maxLengthBeforeComputation ? maxLengthBeforeComputation / textLength * 1.5 : 1; // Compute the font size ratio\n    var valueFontSize = (_b = valueLabel === null || valueLabel === void 0 ? void 0 : valueLabel.style) === null || _b === void 0 ? void 0 : _b.fontSize;\n    var valueTextStyle = __assign({}, valueLabel.style);\n    var x = gauge.dimensions.current.outerRadius;\n    var y = 0;\n    valueTextStyle.textAnchor = \"middle\";\n    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle) {\n        y = gauge.dimensions.current.outerRadius / 1.5 + textPadding;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Radial) {\n        y = gauge.dimensions.current.outerRadius * 1.45 + textPadding;\n    } else if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n        y = gauge.dimensions.current.outerRadius * 1.0 + textPadding;\n    }\n    //if(gauge.props.pointer.type == PointerType.Arrow){\n    //  y = gauge.dimensions.current.outerRadius * 0.79 + textPadding;\n    //}\n    var widthFactor = gauge.props.type == GaugeComponentProps_1.GaugeType.Radial ? 0.003 : 0.003;\n    fontRatio = gauge.dimensions.current.width * widthFactor * fontRatio;\n    var fontSizeNumber = parseInt(valueFontSize, 10) * fontRatio;\n    valueTextStyle.fontSize = fontSizeNumber + \"px\";\n    if (valueLabel.matchColorWithArc) valueTextStyle.fill = ((_c = (0, arc_1.getArcDataByValue)(value, gauge)) === null || _c === void 0 ? void 0 : _c.color) || \"white\";\n    (0, exports.addText)(text, x, y, gauge, valueTextStyle, constants_1.default.valueLabelClassname);\n};\nexports.addValueText = addValueText;\nvar clearValueLabel = function(gauge) {\n    return gauge.g.current.selectAll(\".\".concat(constants_1.default.valueLabelClassname)).remove();\n};\nexports.clearValueLabel = clearValueLabel;\nvar clearTicks = function(gauge) {\n    gauge.g.current.selectAll(\".\".concat(constants_1.default.tickLineClassname)).remove();\n    gauge.g.current.selectAll(\".\".concat(constants_1.default.tickValueClassname)).remove();\n};\nexports.clearTicks = clearTicks;\nvar calculateAnchorAndAngleByValue = function(value, gauge) {\n    var _a;\n    var _b;\n    var labels = gauge.props.labels;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var valuePercentage = utils.calculatePercentage(minValue, maxValue, value);\n    var gaugeTypesAngles = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Grafana] = {\n        startAngle: -20,\n        endAngle: 220\n    }, _a[GaugeComponentProps_1.GaugeType.Semicircle] = {\n        startAngle: 0,\n        endAngle: 180\n    }, _a[GaugeComponentProps_1.GaugeType.Radial] = {\n        startAngle: -42,\n        endAngle: 266\n    }, _a);\n    var _c = gaugeTypesAngles[gauge.props.type], startAngle = _c.startAngle, endAngle = _c.endAngle;\n    var angle = startAngle + valuePercentage * 100 * endAngle / 100;\n    var isValueLessThanHalf = valuePercentage < 0.5;\n    //Values between 40% and 60% are aligned in the middle\n    var isValueBetweenTolerance = valuePercentage > constants_1.default.rangeBetweenCenteredTickValueLabel[0] && valuePercentage < constants_1.default.rangeBetweenCenteredTickValueLabel[1];\n    var tickAnchor = \"\";\n    var isInner = ((_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.type) == \"inner\";\n    if (isValueBetweenTolerance) {\n        tickAnchor = \"middle\";\n    } else if (isValueLessThanHalf) {\n        tickAnchor = isInner ? \"start\" : \"end\";\n    } else {\n        tickAnchor = isInner ? \"end\" : \"start\";\n    }\n    // if(valuePercentage > 0.50) angle = angle - 180;\n    return {\n        tickAnchor: tickAnchor,\n        angle: angle\n    };\n};\nexports.calculateAnchorAndAngleByValue = calculateAnchorAndAngleByValue;\nvar shouldHideTickLine = function(tick, gauge) {\n    var _a, _b, _c, _d;\n    var labels = gauge.props.labels;\n    var defaultHideValue = (_a = Tick_1.defaultTickLabels.defaultTickLineConfig) === null || _a === void 0 ? void 0 : _a.hide;\n    var shouldHide = defaultHideValue;\n    var defaultHideLineFromUser = (_c = (_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.defaultTickLineConfig) === null || _c === void 0 ? void 0 : _c.hide;\n    if (defaultHideLineFromUser != undefined) {\n        shouldHide = defaultHideLineFromUser;\n    }\n    var specificHideValueFromUser = (_d = tick.lineConfig) === null || _d === void 0 ? void 0 : _d.hide;\n    if (specificHideValueFromUser != undefined) {\n        shouldHide = specificHideValueFromUser;\n    }\n    return shouldHide;\n};\nvar shouldHideTickValue = function(tick, gauge) {\n    var _a, _b, _c, _d;\n    var labels = gauge.props.labels;\n    var defaultHideValue = (_a = Tick_1.defaultTickLabels.defaultTickValueConfig) === null || _a === void 0 ? void 0 : _a.hide;\n    var shouldHide = defaultHideValue;\n    var defaultHideValueFromUser = (_c = (_b = labels === null || labels === void 0 ? void 0 : labels.tickLabels) === null || _b === void 0 ? void 0 : _b.defaultTickValueConfig) === null || _c === void 0 ? void 0 : _c.hide;\n    if (defaultHideValueFromUser != undefined) {\n        shouldHide = defaultHideValueFromUser;\n    }\n    var specificHideValueFromUser = (_d = tick.valueConfig) === null || _d === void 0 ? void 0 : _d.hide;\n    if (specificHideValueFromUser != undefined) {\n        shouldHide = specificHideValueFromUser;\n    }\n    return shouldHide;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/labels.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.clearPointerElement = exports.addPointerElement = exports.translatePointer = exports.drawPointer = void 0;\nvar d3_1 = __webpack_require__(/*! d3 */ \"(ssr)/../../node_modules/d3/src/index.js\");\nvar Pointer_1 = __webpack_require__(/*! ../types/Pointer */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\");\nvar arc_1 = __webpack_require__(/*! ./arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\");\nvar utils = __importStar(__webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\"));\nvar arcHooks = __importStar(__webpack_require__(/*! ./arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar GaugeComponentProps_1 = __webpack_require__(/*! ../types/GaugeComponentProps */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar drawPointer = function(gauge, resize) {\n    var _a;\n    if (resize === void 0) {\n        resize = false;\n    }\n    gauge.pointer.current.context = setupContext(gauge);\n    var _b = gauge.pointer.current.context, prevPercent = _b.prevPercent, currentPercent = _b.currentPercent, prevProgress = _b.prevProgress;\n    var pointer = gauge.props.pointer;\n    var isFirstTime = ((_a = gauge.prevProps) === null || _a === void 0 ? void 0 : _a.current.value) == undefined;\n    if ((isFirstTime || resize) && gauge.props.type != GaugeComponentProps_1.GaugeType.Grafana) initPointer(gauge);\n    var shouldAnimate = (!resize || isFirstTime) && pointer.animate;\n    if (shouldAnimate) {\n        gauge.doughnut.current.transition().delay(pointer.animationDelay).ease(pointer.elastic ? d3_1.easeElastic : d3_1.easeExpOut).duration(pointer.animationDuration).tween(\"progress\", function() {\n            var currentInterpolatedPercent = (0, d3_1.interpolateNumber)(prevPercent, currentPercent);\n            return function(percentOfPercent) {\n                var progress = currentInterpolatedPercent(percentOfPercent);\n                if (isProgressValid(progress, prevProgress, gauge)) {\n                    if (gauge.props.type == GaugeComponentProps_1.GaugeType.Grafana) {\n                        arcHooks.clearArcs(gauge);\n                        arcHooks.drawArc(gauge, progress);\n                    //arcHooks.setupArcs(gauge);\n                    } else {\n                        updatePointer(progress, gauge);\n                    }\n                }\n                gauge.pointer.current.context.prevProgress = progress;\n            };\n        });\n    } else {\n        updatePointer(currentPercent, gauge);\n    }\n};\nexports.drawPointer = drawPointer;\nvar setupContext = function(gauge) {\n    var _a;\n    var value = gauge.props.value;\n    var pointer = gauge.props.pointer;\n    var pointerLength = pointer.length;\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var pointerPath = gauge.pointer.current.context.pointerPath;\n    var pointerRadius = getPointerRadius(gauge);\n    var length = pointer.type == Pointer_1.PointerType.Needle ? pointerLength : 0.2;\n    var typesWithPath = [\n        Pointer_1.PointerType.Needle,\n        Pointer_1.PointerType.Arrow\n    ];\n    var pointerContext = {\n        centerPoint: [\n            0,\n            -pointerRadius / 2\n        ],\n        pointerRadius: getPointerRadius(gauge),\n        pathLength: gauge.dimensions.current.outerRadius * length,\n        currentPercent: utils.calculatePercentage(minValue, maxValue, value),\n        prevPercent: utils.calculatePercentage(minValue, maxValue, ((_a = gauge.prevProps) === null || _a === void 0 ? void 0 : _a.current.value) || minValue),\n        prevProgress: 0,\n        pathStr: \"\",\n        shouldDrawPath: typesWithPath.includes(pointer.type),\n        prevColor: \"\"\n    };\n    return pointerContext;\n};\nvar initPointer = function(gauge) {\n    var value = gauge.props.value;\n    var pointer = gauge.props.pointer;\n    var _a = gauge.pointer.current.context, shouldDrawPath = _a.shouldDrawPath, centerPoint = _a.centerPoint, pointerRadius = _a.pointerRadius, pathStr = _a.pathStr, currentPercent = _a.currentPercent, prevPercent = _a.prevPercent;\n    if (shouldDrawPath) {\n        gauge.pointer.current.context.pathStr = calculatePointerPath(gauge, prevPercent || currentPercent);\n        gauge.pointer.current.path = gauge.pointer.current.element.append(\"path\").attr(\"d\", gauge.pointer.current.context.pathStr).attr(\"fill\", pointer.color);\n    }\n    //Add a circle at the bottom of pointer\n    if (pointer.type == Pointer_1.PointerType.Needle) {\n        gauge.pointer.current.element.append(\"circle\").attr(\"cx\", centerPoint[0]).attr(\"cy\", centerPoint[1]).attr(\"r\", pointerRadius).attr(\"fill\", pointer.color);\n    } else if (pointer.type == Pointer_1.PointerType.Blob) {\n        gauge.pointer.current.element.append(\"circle\").attr(\"cx\", centerPoint[0]).attr(\"cy\", centerPoint[1]).attr(\"r\", pointerRadius).attr(\"fill\", pointer.baseColor).attr(\"stroke\", pointer.color).attr(\"stroke-width\", pointer.strokeWidth * pointerRadius / 10);\n    }\n    //Translate the pointer starting point of the arc\n    setPointerPosition(pointerRadius, value, gauge);\n};\nvar updatePointer = function(percentage, gauge) {\n    var _a;\n    var pointer = gauge.props.pointer;\n    var _b = gauge.pointer.current.context, pointerRadius = _b.pointerRadius, shouldDrawPath = _b.shouldDrawPath, prevColor = _b.prevColor;\n    setPointerPosition(pointerRadius, percentage, gauge);\n    if (shouldDrawPath && gauge.props.type != GaugeComponentProps_1.GaugeType.Grafana) gauge.pointer.current.path.attr(\"d\", calculatePointerPath(gauge, percentage));\n    if (pointer.type == Pointer_1.PointerType.Blob) {\n        var currentColor = (_a = arcHooks.getArcDataByPercentage(percentage, gauge)) === null || _a === void 0 ? void 0 : _a.color;\n        var shouldChangeColor = currentColor != prevColor;\n        if (shouldChangeColor) gauge.pointer.current.element.select(\"circle\").attr(\"stroke\", currentColor);\n        var strokeWidth = pointer.strokeWidth * pointerRadius / 10;\n        gauge.pointer.current.element.select(\"circle\").attr(\"stroke-width\", strokeWidth);\n        gauge.pointer.current.context.prevColor = currentColor;\n    }\n};\nvar setPointerPosition = function(pointerRadius, progress, gauge) {\n    var _a;\n    var pointer = gauge.props.pointer;\n    var pointerType = pointer.type;\n    var dimensions = gauge.dimensions;\n    var value = utils.getCurrentGaugeValueByPercentage(progress, gauge);\n    var pointers = (_a = {}, _a[Pointer_1.PointerType.Needle] = function() {\n        // Set needle position to center\n        (0, exports.translatePointer)(dimensions.current.outerRadius, dimensions.current.outerRadius, gauge);\n    }, _a[Pointer_1.PointerType.Arrow] = function() {\n        var _a = (0, arc_1.getCoordByValue)(value, gauge, \"inner\", 0, 0.70), x = _a.x, y = _a.y;\n        x -= 1;\n        y += pointerRadius - 3;\n        (0, exports.translatePointer)(x, y, gauge);\n    }, _a[Pointer_1.PointerType.Blob] = function() {\n        var _a = (0, arc_1.getCoordByValue)(value, gauge, \"between\", 0, 0.75), x = _a.x, y = _a.y;\n        x -= 1;\n        y += pointerRadius;\n        (0, exports.translatePointer)(x, y, gauge);\n    }, _a);\n    return pointers[pointerType]();\n};\nvar isProgressValid = function(currentPercent, prevPercent, gauge) {\n    //Avoid unnecessary re-rendering (when progress is too small) but allow the pointer to reach the final value\n    var overFlow = currentPercent > 1 || currentPercent < 0;\n    var tooSmallValue = Math.abs(currentPercent - prevPercent) < 0.0001;\n    var sameValueAsBefore = currentPercent == prevPercent;\n    return !tooSmallValue && !sameValueAsBefore && !overFlow;\n};\nvar calculatePointerPath = function(gauge, percent) {\n    var _a = gauge.pointer.current.context, centerPoint = _a.centerPoint, pointerRadius = _a.pointerRadius, pathLength = _a.pathLength;\n    var startAngle = utils.degToRad(gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle ? 0 : -42);\n    var endAngle = utils.degToRad(gauge.props.type == GaugeComponentProps_1.GaugeType.Semicircle ? 180 : 223);\n    var angle = startAngle + percent * (endAngle - startAngle);\n    var topPoint = [\n        centerPoint[0] - pathLength * Math.cos(angle),\n        centerPoint[1] - pathLength * Math.sin(angle)\n    ];\n    var thetaMinusHalfPi = angle - Math.PI / 2;\n    var leftPoint = [\n        centerPoint[0] - pointerRadius * Math.cos(thetaMinusHalfPi),\n        centerPoint[1] - pointerRadius * Math.sin(thetaMinusHalfPi)\n    ];\n    var thetaPlusHalfPi = angle + Math.PI / 2;\n    var rightPoint = [\n        centerPoint[0] - pointerRadius * Math.cos(thetaPlusHalfPi),\n        centerPoint[1] - pointerRadius * Math.sin(thetaPlusHalfPi)\n    ];\n    var pathStr = \"M \".concat(leftPoint[0], \" \").concat(leftPoint[1], \" L \").concat(topPoint[0], \" \").concat(topPoint[1], \" L \").concat(rightPoint[0], \" \").concat(rightPoint[1]);\n    return pathStr;\n};\nvar getPointerRadius = function(gauge) {\n    var pointer = gauge.props.pointer;\n    var pointerWidth = pointer.width;\n    return pointerWidth * (gauge.dimensions.current.width / 500);\n};\nvar translatePointer = function(x, y, gauge) {\n    return gauge.pointer.current.element.attr(\"transform\", \"translate(\" + x + \", \" + y + \")\");\n};\nexports.translatePointer = translatePointer;\nvar addPointerElement = function(gauge) {\n    return gauge.pointer.current.element = gauge.g.current.append(\"g\").attr(\"class\", \"pointer\");\n};\nexports.addPointerElement = addPointerElement;\nvar clearPointerElement = function(gauge) {\n    return gauge.pointer.current.element.selectAll(\"*\").remove();\n};\nexports.clearPointerElement = clearPointerElement;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/pointer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nvar __assign = (void 0) && (void 0).__assign || function() {\n    __assign = Object.assign || function(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.camelCaseToKebabCase = exports.getCurrentGaugeValueByPercentage = exports.getCurrentGaugePercentageByValue = exports.degToRad = exports.normalize = exports.floatingNumber = exports.percentToRad = exports.mergeObjects = exports.isEmptyObject = exports.calculatePercentage = void 0;\nvar calculatePercentage = function(minValue, maxValue, value) {\n    if (value < minValue) {\n        return 0;\n    } else if (value > maxValue) {\n        return 1;\n    } else {\n        var percentage = (value - minValue) / (maxValue - minValue);\n        return percentage;\n    }\n};\nexports.calculatePercentage = calculatePercentage;\nvar isEmptyObject = function(obj) {\n    return Object.keys(obj).length === 0 && obj.constructor === Object;\n};\nexports.isEmptyObject = isEmptyObject;\nvar mergeObjects = function(obj1, obj2) {\n    var mergedObj = __assign({}, obj1);\n    Object.keys(obj2).forEach(function(key) {\n        var val1 = obj1[key];\n        var val2 = obj2[key];\n        if (Array.isArray(val1) && Array.isArray(val2)) {\n            mergedObj[key] = val2;\n        } else if (typeof val1 === \"object\" && typeof val2 === \"object\") {\n            mergedObj[key] = (0, exports.mergeObjects)(val1, val2);\n        } else if (val2 !== undefined) {\n            mergedObj[key] = val2;\n        }\n    });\n    return mergedObj;\n};\nexports.mergeObjects = mergeObjects;\n//Returns the angle (in rad) for the given 'percent' value where percent = 1 means 100% and is 180 degree angle\nvar percentToRad = function(percent, angle) {\n    return percent * (Math.PI / angle);\n};\nexports.percentToRad = percentToRad;\nvar floatingNumber = function(value, maxDigits) {\n    if (maxDigits === void 0) {\n        maxDigits = 2;\n    }\n    return Math.round(value * Math.pow(10, maxDigits)) / Math.pow(10, maxDigits);\n};\nexports.floatingNumber = floatingNumber;\n// Function to normalize a value between a new min and max\nfunction normalize(value, min, max) {\n    return (value - min) / (max - min) * 100;\n}\nexports.normalize = normalize;\nvar degToRad = function(degrees) {\n    return degrees * (Math.PI / 180);\n};\nexports.degToRad = degToRad;\nvar getCurrentGaugePercentageByValue = function(value, gauge) {\n    return (0, exports.calculatePercentage)(gauge.minValue, gauge.maxValue, value);\n};\nexports.getCurrentGaugePercentageByValue = getCurrentGaugePercentageByValue;\nvar getCurrentGaugeValueByPercentage = function(percentage, gauge) {\n    var minValue = gauge.props.minValue;\n    var maxValue = gauge.props.maxValue;\n    var value = minValue + percentage * (maxValue - minValue);\n    return value;\n};\nexports.getCurrentGaugeValueByPercentage = getCurrentGaugeValueByPercentage;\nvar camelCaseToKebabCase = function(str) {\n    return str.replace(/[A-Z]/g, function(letter) {\n        return \"-\".concat(letter.toLowerCase());\n    });\n};\nexports.camelCaseToKebabCase = camelCaseToKebabCase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __assign = (void 0) && (void 0).__assign || function() {\n    __assign = Object.assign || function(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaugeComponent = void 0;\nvar react_1 = __importStar(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar d3_1 = __webpack_require__(/*! d3 */ \"(ssr)/../../node_modules/d3/src/index.js\");\nvar GaugeComponentProps_1 = __webpack_require__(/*! ./types/GaugeComponentProps */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nvar chartHooks = __importStar(__webpack_require__(/*! ./hooks/chart */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/chart.js\"));\nvar arcHooks = __importStar(__webpack_require__(/*! ./hooks/arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/arc.js\"));\nvar utils_1 = __webpack_require__(/*! ./hooks/utils */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/hooks/utils.js\");\nvar Dimensions_1 = __webpack_require__(/*! ./types/Dimensions */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js\");\nvar Pointer_1 = __webpack_require__(/*! ./types/Pointer */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\");\nvar Arc_1 = __webpack_require__(/*! ./types/Arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js\");\n/*\nGaugeComponent creates a gauge chart using D3\nThe chart is responsive and will have the same width as the \"container\"\nThe radius of the gauge depends on the width and height of the container\nIt will use whichever is smallest of width or height\nThe svg element surrounding the gauge will always be square\n\"container\" is the div where the chart should be placed\n*/ var GaugeComponent = function(props) {\n    var svg = (0, react_1.useRef)({});\n    var tooltip = (0, react_1.useRef)({});\n    var g = (0, react_1.useRef)({});\n    var doughnut = (0, react_1.useRef)({});\n    var isFirstRun = (0, react_1.useRef)(true);\n    var currentProgress = (0, react_1.useRef)(0);\n    var pointer = (0, react_1.useRef)(__assign({}, Pointer_1.defaultPointerRef));\n    var container = (0, react_1.useRef)({});\n    var arcData = (0, react_1.useRef)([]);\n    var pieChart = (0, react_1.useRef)((0, d3_1.pie)());\n    var dimensions = (0, react_1.useRef)(__assign({}, Dimensions_1.defaultDimensions));\n    var mergedProps = (0, react_1.useRef)(props);\n    var prevProps = (0, react_1.useRef)({});\n    var resizeObserver = (0, react_1.useRef)({});\n    var selectedRef = (0, react_1.useRef)(null);\n    var gauge = {\n        props: mergedProps.current,\n        resizeObserver: resizeObserver,\n        prevProps: prevProps,\n        svg: svg,\n        g: g,\n        dimensions: dimensions,\n        doughnut: doughnut,\n        isFirstRun: isFirstRun,\n        currentProgress: currentProgress,\n        pointer: pointer,\n        container: container,\n        arcData: arcData,\n        pieChart: pieChart,\n        tooltip: tooltip\n    };\n    //Merged properties will get the default props and overwrite by the user's defined props\n    //To keep the original default props in the object\n    var updateMergedProps = function() {\n        var _a, _b;\n        var defaultValues = __assign({}, GaugeComponentProps_1.defaultGaugeProps);\n        gauge.props = mergedProps.current = (0, utils_1.mergeObjects)(defaultValues, props);\n        if (((_a = gauge.props.arc) === null || _a === void 0 ? void 0 : _a.width) == ((_b = GaugeComponentProps_1.defaultGaugeProps.arc) === null || _b === void 0 ? void 0 : _b.width)) {\n            var mergedArc = mergedProps.current.arc;\n            mergedArc.width = (0, Arc_1.getArcWidthByType)(gauge.props.type);\n        }\n        if (gauge.props.marginInPercent == GaugeComponentProps_1.defaultGaugeProps.marginInPercent) mergedProps.current.marginInPercent = (0, GaugeComponentProps_1.getGaugeMarginByType)(gauge.props.type);\n        arcHooks.validateArcs(gauge);\n    };\n    var shouldInitChart = function() {\n        var arcsPropsChanged = JSON.stringify(prevProps.current.arc) !== JSON.stringify(mergedProps.current.arc);\n        var pointerPropsChanged = JSON.stringify(prevProps.current.pointer) !== JSON.stringify(mergedProps.current.pointer);\n        var valueChanged = JSON.stringify(prevProps.current.value) !== JSON.stringify(mergedProps.current.value);\n        var minValueChanged = JSON.stringify(prevProps.current.minValue) !== JSON.stringify(mergedProps.current.minValue);\n        var maxValueChanged = JSON.stringify(prevProps.current.maxValue) !== JSON.stringify(mergedProps.current.maxValue);\n        return arcsPropsChanged || pointerPropsChanged || valueChanged || minValueChanged || maxValueChanged;\n    };\n    (0, react_1.useLayoutEffect)(function() {\n        updateMergedProps();\n        isFirstRun.current = (0, utils_1.isEmptyObject)(container.current);\n        if (isFirstRun.current) container.current = (0, d3_1.select)(selectedRef.current);\n        if (shouldInitChart()) chartHooks.initChart(gauge, isFirstRun.current);\n        gauge.prevProps.current = mergedProps.current;\n    }, [\n        props\n    ]);\n    // useEffect(() => {\n    //   const observer = new MutationObserver(function () {\n    //     setTimeout(() => window.dispatchEvent(new Event('resize')), 10);\n    //     if (!selectedRef.current?.offsetParent) return;\n    //     chartHooks.renderChart(gauge, true);\n    //     observer.disconnect()\n    //   });\n    //   observer.observe(selectedRef.current?.parentNode, {attributes: true, subtree: false});\n    //   return () => observer.disconnect();\n    // }, [selectedRef.current?.parentNode?.offsetWidth, selectedRef.current?.parentNode?.offsetHeight]);\n    (0, react_1.useEffect)(function() {\n        var handleResize = function() {\n            return chartHooks.renderChart(gauge, true);\n        };\n        //Set up resize event listener to re-render the chart everytime the window is resized\n        window.addEventListener(\"resize\", handleResize);\n        return function() {\n            return window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        props\n    ]);\n    // useEffect(() => {\n    //   console.log(selectedRef.current?.offsetWidth)\n    //   // workaround to trigger recomputing of gauge size on first load (e.g. F5)\n    //   setTimeout(() => window.dispatchEvent(new Event('resize')), 10);\n    // }, [selectedRef.current?.parentNode]);\n    (0, react_1.useEffect)(function() {\n        var element = selectedRef.current;\n        if (!element) return;\n        // Create observer instance\n        var observer = new ResizeObserver(function() {\n            chartHooks.renderChart(gauge, true);\n        });\n        // Store observer reference\n        gauge.resizeObserver.current = observer;\n        // Observe parent node\n        if (element.parentNode) {\n            observer.observe(element.parentNode);\n        }\n        // Cleanup\n        return function() {\n            var _a;\n            if (gauge.resizeObserver) {\n                (_a = gauge.resizeObserver.current) === null || _a === void 0 ? void 0 : _a.disconnect();\n                delete gauge.resizeObserver.current;\n            }\n        };\n    }, []);\n    var id = props.id, style = props.style, className = props.className, type = props.type;\n    return react_1.default.createElement(\"div\", {\n        id: id,\n        className: \"\".concat(gauge.props.type, \"-gauge\").concat(className ? \" \" + className : \"\"),\n        style: style,\n        ref: function(svg) {\n            return selectedRef.current = svg;\n        }\n    });\n};\nexports.GaugeComponent = GaugeComponent;\nexports[\"default\"] = GaugeComponent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultArc = exports.getArcWidthByType = exports.defaultSubArcs = void 0;\nvar GaugeComponentProps_1 = __webpack_require__(/*! ./GaugeComponentProps */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\");\nexports.defaultSubArcs = [\n    {\n        limit: 33,\n        color: \"#5BE12C\"\n    },\n    {\n        limit: 66,\n        color: \"#F5CD19\"\n    },\n    {\n        color: \"#EA4228\"\n    }\n];\nvar getArcWidthByType = function(type) {\n    var _a;\n    var gaugeTypesWidth = (_a = {}, _a[GaugeComponentProps_1.GaugeType.Grafana] = 0.25, _a[GaugeComponentProps_1.GaugeType.Semicircle] = 0.15, _a[GaugeComponentProps_1.GaugeType.Radial] = 0.2, _a);\n    if (!type) type = GaugeComponentProps_1.defaultGaugeProps.type;\n    return gaugeTypesWidth[type];\n};\nexports.getArcWidthByType = getArcWidthByType;\nexports.defaultArc = {\n    padding: 0.05,\n    width: 0.25,\n    cornerRadius: 7,\n    nbSubArcs: undefined,\n    emptyColor: \"#5C5C5C\",\n    colorArray: undefined,\n    subArcs: exports.defaultSubArcs,\n    gradient: false\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultDimensions = exports.defaultAngles = exports.defaultMargins = void 0;\nexports.defaultMargins = {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n};\nexports.defaultAngles = {\n    startAngle: 0,\n    endAngle: 0,\n    startAngleDeg: 0,\n    endAngleDeg: 0\n};\nexports.defaultDimensions = {\n    width: 0,\n    height: 0,\n    margin: exports.defaultMargins,\n    outerRadius: 0,\n    innerRadius: 0,\n    angles: exports.defaultAngles,\n    fixedHeight: 0\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9HYXVnZUNvbXBvbmVudC90eXBlcy9EaW1lbnNpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdERCx5QkFBeUIsR0FBR0EscUJBQXFCLEdBQUdBLHNCQUFzQixHQUFHLEtBQUs7QUFDbEZBLHNCQUFzQixHQUFHO0lBQ3JCSyxLQUFLO0lBQ0xDLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxNQUFNO0FBQ1Y7QUFDQVIscUJBQXFCLEdBQUc7SUFDcEJTLFlBQVk7SUFDWkMsVUFBVTtJQUNWQyxlQUFlO0lBQ2ZDLGFBQWE7QUFDakI7QUFDQVoseUJBQXlCLEdBQUc7SUFDeEJhLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxRQUFRZixRQUFRSSxjQUFjO0lBQzlCWSxhQUFhO0lBQ2JDLGFBQWE7SUFDYkMsUUFBUWxCLFFBQVFHLGFBQWE7SUFDN0JnQixhQUFhO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9HYXVnZUNvbXBvbmVudC90eXBlcy9EaW1lbnNpb25zLmpzPzZjMzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRlZmF1bHREaW1lbnNpb25zID0gZXhwb3J0cy5kZWZhdWx0QW5nbGVzID0gZXhwb3J0cy5kZWZhdWx0TWFyZ2lucyA9IHZvaWQgMDtcbmV4cG9ydHMuZGVmYXVsdE1hcmdpbnMgPSB7XG4gICAgdG9wOiAwLFxuICAgIHJpZ2h0OiAwLFxuICAgIGJvdHRvbTogMCxcbiAgICBsZWZ0OiAwXG59O1xuZXhwb3J0cy5kZWZhdWx0QW5nbGVzID0ge1xuICAgIHN0YXJ0QW5nbGU6IDAsXG4gICAgZW5kQW5nbGU6IDAsXG4gICAgc3RhcnRBbmdsZURlZzogMCxcbiAgICBlbmRBbmdsZURlZzogMFxufTtcbmV4cG9ydHMuZGVmYXVsdERpbWVuc2lvbnMgPSB7XG4gICAgd2lkdGg6IDAsXG4gICAgaGVpZ2h0OiAwLFxuICAgIG1hcmdpbjogZXhwb3J0cy5kZWZhdWx0TWFyZ2lucyxcbiAgICBvdXRlclJhZGl1czogMCxcbiAgICBpbm5lclJhZGl1czogMCxcbiAgICBhbmdsZXM6IGV4cG9ydHMuZGVmYXVsdEFuZ2xlcyxcbiAgICBmaXhlZEhlaWdodDogMFxufTtcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImRlZmF1bHREaW1lbnNpb25zIiwiZGVmYXVsdEFuZ2xlcyIsImRlZmF1bHRNYXJnaW5zIiwidG9wIiwicmlnaHQiLCJib3R0b20iLCJsZWZ0Iiwic3RhcnRBbmdsZSIsImVuZEFuZ2xlIiwic3RhcnRBbmdsZURlZyIsImVuZEFuZ2xlRGVnIiwid2lkdGgiLCJoZWlnaHQiLCJtYXJnaW4iLCJvdXRlclJhZGl1cyIsImlubmVyUmFkaXVzIiwiYW5nbGVzIiwiZml4ZWRIZWlnaHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Dimensions.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getGaugeMarginByType = exports.defaultGaugeProps = exports.GaugeType = void 0;\nvar Arc_1 = __webpack_require__(/*! ./Arc */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Arc.js\");\nvar Labels_1 = __webpack_require__(/*! ./Labels */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js\");\nvar Pointer_1 = __webpack_require__(/*! ./Pointer */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\");\nvar GaugeType;\n(function(GaugeType) {\n    GaugeType[\"Semicircle\"] = \"semicircle\";\n    GaugeType[\"Radial\"] = \"radial\";\n    GaugeType[\"Grafana\"] = \"grafana\";\n})(GaugeType || (exports.GaugeType = GaugeType = {}));\nexports.defaultGaugeProps = {\n    id: \"\",\n    className: \"gauge-component-class\",\n    style: {\n        width: \"100%\"\n    },\n    marginInPercent: 0.07,\n    value: 33,\n    minValue: 0,\n    maxValue: 100,\n    arc: Arc_1.defaultArc,\n    labels: Labels_1.defaultLabels,\n    pointer: Pointer_1.defaultPointer,\n    type: GaugeType.Grafana\n};\nvar getGaugeMarginByType = function(type) {\n    var _a;\n    var gaugeTypesMargin = (_a = {}, _a[GaugeType.Grafana] = {\n        top: 0.12,\n        bottom: 0.00,\n        left: 0.07,\n        right: 0.07\n    }, _a[GaugeType.Semicircle] = {\n        top: 0.08,\n        bottom: 0.00,\n        left: 0.08,\n        right: 0.08\n    }, _a[GaugeType.Radial] = {\n        top: 0.07,\n        bottom: 0.00,\n        left: 0.07,\n        right: 0.07\n    }, _a);\n    return gaugeTypesMargin[type];\n};\nexports.getGaugeMarginByType = getGaugeMarginByType;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/GaugeComponentProps.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultLabels = exports.defaultValueLabel = void 0;\nvar Tick_1 = __webpack_require__(/*! ./Tick */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js\");\nexports.defaultValueLabel = {\n    formatTextValue: undefined,\n    matchColorWithArc: false,\n    maxDecimalDigits: 2,\n    style: {\n        fontSize: \"35px\",\n        fill: \"#fff\",\n        textShadow: \"black 1px 0.5px 0px, black 0px 0px 0.03em, black 0px 0px 0.01em\"\n    },\n    hide: false\n};\nexports.defaultLabels = {\n    valueLabel: exports.defaultValueLabel,\n    tickLabels: Tick_1.defaultTickLabels\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9HYXVnZUNvbXBvbmVudC90eXBlcy9MYWJlbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELHFCQUFxQixHQUFHQSx5QkFBeUIsR0FBRyxLQUFLO0FBQ3pELElBQUlJLFNBQVNDLG1CQUFPQSxDQUFDLG9HQUFRO0FBQzdCTCx5QkFBeUIsR0FBRztJQUN4Qk0saUJBQWlCQztJQUNqQkMsbUJBQW1CO0lBQ25CQyxrQkFBa0I7SUFDbEJDLE9BQU87UUFDSEMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFlBQVk7SUFDaEI7SUFDQUMsTUFBTTtBQUNWO0FBQ0FkLHFCQUFxQixHQUFHO0lBQ3BCZSxZQUFZZixRQUFRRyxpQkFBaUI7SUFDckNhLFlBQVlaLE9BQU9hLGlCQUFpQjtBQUN4QyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1nYXVnZS1jb21wb25lbnQvZGlzdC9saWIvR2F1Z2VDb21wb25lbnQvdHlwZXMvTGFiZWxzLmpzPzYzNDEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRlZmF1bHRMYWJlbHMgPSBleHBvcnRzLmRlZmF1bHRWYWx1ZUxhYmVsID0gdm9pZCAwO1xudmFyIFRpY2tfMSA9IHJlcXVpcmUoXCIuL1RpY2tcIik7XG5leHBvcnRzLmRlZmF1bHRWYWx1ZUxhYmVsID0ge1xuICAgIGZvcm1hdFRleHRWYWx1ZTogdW5kZWZpbmVkLFxuICAgIG1hdGNoQ29sb3JXaXRoQXJjOiBmYWxzZSxcbiAgICBtYXhEZWNpbWFsRGlnaXRzOiAyLFxuICAgIHN0eWxlOiB7XG4gICAgICAgIGZvbnRTaXplOiBcIjM1cHhcIixcbiAgICAgICAgZmlsbDogJyNmZmYnLFxuICAgICAgICB0ZXh0U2hhZG93OiBcImJsYWNrIDFweCAwLjVweCAwcHgsIGJsYWNrIDBweCAwcHggMC4wM2VtLCBibGFjayAwcHggMHB4IDAuMDFlbVwiXG4gICAgfSxcbiAgICBoaWRlOiBmYWxzZVxufTtcbmV4cG9ydHMuZGVmYXVsdExhYmVscyA9IHtcbiAgICB2YWx1ZUxhYmVsOiBleHBvcnRzLmRlZmF1bHRWYWx1ZUxhYmVsLFxuICAgIHRpY2tMYWJlbHM6IFRpY2tfMS5kZWZhdWx0VGlja0xhYmVsc1xufTtcbiJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImRlZmF1bHRMYWJlbHMiLCJkZWZhdWx0VmFsdWVMYWJlbCIsIlRpY2tfMSIsInJlcXVpcmUiLCJmb3JtYXRUZXh0VmFsdWUiLCJ1bmRlZmluZWQiLCJtYXRjaENvbG9yV2l0aEFyYyIsIm1heERlY2ltYWxEaWdpdHMiLCJzdHlsZSIsImZvbnRTaXplIiwiZmlsbCIsInRleHRTaGFkb3ciLCJoaWRlIiwidmFsdWVMYWJlbCIsInRpY2tMYWJlbHMiLCJkZWZhdWx0VGlja0xhYmVscyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Labels.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultPointer = exports.defaultPointerRef = exports.defaultPointerContext = exports.PointerType = void 0;\nvar PointerType;\n(function(PointerType) {\n    PointerType[\"Needle\"] = \"needle\";\n    PointerType[\"Blob\"] = \"blob\";\n    PointerType[\"Arrow\"] = \"arrow\";\n})(PointerType || (exports.PointerType = PointerType = {}));\nexports.defaultPointerContext = {\n    centerPoint: [\n        0,\n        0\n    ],\n    pointerRadius: 0,\n    pathLength: 0,\n    currentPercent: 0,\n    prevPercent: 0,\n    prevProgress: 0,\n    pathStr: \"\",\n    shouldDrawPath: false,\n    prevColor: \"\"\n};\nexports.defaultPointerRef = {\n    element: undefined,\n    path: undefined,\n    context: exports.defaultPointerContext\n};\nexports.defaultPointer = {\n    type: PointerType.Needle,\n    color: \"#5A5A5A\",\n    baseColor: \"white\",\n    length: 0.70,\n    width: 20,\n    animate: true,\n    elastic: false,\n    hide: false,\n    animationDuration: 3000,\n    animationDelay: 100,\n    strokeWidth: 8\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9HYXVnZUNvbXBvbmVudC90eXBlcy9Qb2ludGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdERCxzQkFBc0IsR0FBR0EseUJBQXlCLEdBQUdBLDZCQUE2QixHQUFHQSxtQkFBbUIsR0FBRyxLQUFLO0FBQ2hILElBQUlLO0FBQ0gsVUFBVUEsV0FBVztJQUNsQkEsV0FBVyxDQUFDLFNBQVMsR0FBRztJQUN4QkEsV0FBVyxDQUFDLE9BQU8sR0FBRztJQUN0QkEsV0FBVyxDQUFDLFFBQVEsR0FBRztBQUMzQixHQUFHQSxlQUFnQkwsQ0FBQUEsbUJBQW1CLEdBQUdLLGNBQWMsQ0FBQztBQUN4REwsNkJBQTZCLEdBQUc7SUFDNUJNLGFBQWE7UUFBQztRQUFHO0tBQUU7SUFDbkJDLGVBQWU7SUFDZkMsWUFBWTtJQUNaQyxnQkFBZ0I7SUFDaEJDLGFBQWE7SUFDYkMsY0FBYztJQUNkQyxTQUFTO0lBQ1RDLGdCQUFnQjtJQUNoQkMsV0FBVztBQUNmO0FBQ0FkLHlCQUF5QixHQUFHO0lBQ3hCZSxTQUFTQztJQUNUQyxNQUFNRDtJQUNORSxTQUFTbEIsUUFBUUkscUJBQXFCO0FBQzFDO0FBQ0FKLHNCQUFzQixHQUFHO0lBQ3JCbUIsTUFBTWQsWUFBWWUsTUFBTTtJQUN4QkMsT0FBTztJQUNQQyxXQUFXO0lBQ1hDLFFBQVE7SUFDUkMsT0FBTztJQUNQQyxTQUFTO0lBQ1RDLFNBQVM7SUFDVEMsTUFBTTtJQUNOQyxtQkFBbUI7SUFDbkJDLGdCQUFnQjtJQUNoQkMsYUFBYTtBQUNqQiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1nYXVnZS1jb21wb25lbnQvZGlzdC9saWIvR2F1Z2VDb21wb25lbnQvdHlwZXMvUG9pbnRlci5qcz8xMWI1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5kZWZhdWx0UG9pbnRlciA9IGV4cG9ydHMuZGVmYXVsdFBvaW50ZXJSZWYgPSBleHBvcnRzLmRlZmF1bHRQb2ludGVyQ29udGV4dCA9IGV4cG9ydHMuUG9pbnRlclR5cGUgPSB2b2lkIDA7XG52YXIgUG9pbnRlclR5cGU7XG4oZnVuY3Rpb24gKFBvaW50ZXJUeXBlKSB7XG4gICAgUG9pbnRlclR5cGVbXCJOZWVkbGVcIl0gPSBcIm5lZWRsZVwiO1xuICAgIFBvaW50ZXJUeXBlW1wiQmxvYlwiXSA9IFwiYmxvYlwiO1xuICAgIFBvaW50ZXJUeXBlW1wiQXJyb3dcIl0gPSBcImFycm93XCI7XG59KShQb2ludGVyVHlwZSB8fCAoZXhwb3J0cy5Qb2ludGVyVHlwZSA9IFBvaW50ZXJUeXBlID0ge30pKTtcbmV4cG9ydHMuZGVmYXVsdFBvaW50ZXJDb250ZXh0ID0ge1xuICAgIGNlbnRlclBvaW50OiBbMCwgMF0sXG4gICAgcG9pbnRlclJhZGl1czogMCxcbiAgICBwYXRoTGVuZ3RoOiAwLFxuICAgIGN1cnJlbnRQZXJjZW50OiAwLFxuICAgIHByZXZQZXJjZW50OiAwLFxuICAgIHByZXZQcm9ncmVzczogMCxcbiAgICBwYXRoU3RyOiBcIlwiLFxuICAgIHNob3VsZERyYXdQYXRoOiBmYWxzZSxcbiAgICBwcmV2Q29sb3I6IFwiXCJcbn07XG5leHBvcnRzLmRlZmF1bHRQb2ludGVyUmVmID0ge1xuICAgIGVsZW1lbnQ6IHVuZGVmaW5lZCxcbiAgICBwYXRoOiB1bmRlZmluZWQsXG4gICAgY29udGV4dDogZXhwb3J0cy5kZWZhdWx0UG9pbnRlckNvbnRleHRcbn07XG5leHBvcnRzLmRlZmF1bHRQb2ludGVyID0ge1xuICAgIHR5cGU6IFBvaW50ZXJUeXBlLk5lZWRsZSxcbiAgICBjb2xvcjogXCIjNUE1QTVBXCIsXG4gICAgYmFzZUNvbG9yOiBcIndoaXRlXCIsXG4gICAgbGVuZ3RoOiAwLjcwLFxuICAgIHdpZHRoOiAyMCxcbiAgICBhbmltYXRlOiB0cnVlLFxuICAgIGVsYXN0aWM6IGZhbHNlLFxuICAgIGhpZGU6IGZhbHNlLFxuICAgIGFuaW1hdGlvbkR1cmF0aW9uOiAzMDAwLFxuICAgIGFuaW1hdGlvbkRlbGF5OiAxMDAsXG4gICAgc3Ryb2tlV2lkdGg6IDhcbn07XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZhdWx0UG9pbnRlciIsImRlZmF1bHRQb2ludGVyUmVmIiwiZGVmYXVsdFBvaW50ZXJDb250ZXh0IiwiUG9pbnRlclR5cGUiLCJjZW50ZXJQb2ludCIsInBvaW50ZXJSYWRpdXMiLCJwYXRoTGVuZ3RoIiwiY3VycmVudFBlcmNlbnQiLCJwcmV2UGVyY2VudCIsInByZXZQcm9ncmVzcyIsInBhdGhTdHIiLCJzaG91bGREcmF3UGF0aCIsInByZXZDb2xvciIsImVsZW1lbnQiLCJ1bmRlZmluZWQiLCJwYXRoIiwiY29udGV4dCIsInR5cGUiLCJOZWVkbGUiLCJjb2xvciIsImJhc2VDb2xvciIsImxlbmd0aCIsIndpZHRoIiwiYW5pbWF0ZSIsImVsYXN0aWMiLCJoaWRlIiwiYW5pbWF0aW9uRHVyYXRpb24iLCJhbmltYXRpb25EZWxheSIsInN0cm9rZVdpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Pointer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js":
/*!**************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultTickLabels = void 0;\nvar defaultTickLineConfig = {\n    color: \"rgb(173 172 171)\",\n    length: 7,\n    width: 1,\n    distanceFromArc: 3,\n    hide: false\n};\nvar defaultTickValueConfig = {\n    formatTextValue: undefined,\n    maxDecimalDigits: 2,\n    style: {\n        fontSize: \"10px\",\n        fill: \"rgb(173 172 171)\"\n    },\n    hide: false\n};\nvar defaultTickList = [];\nexports.defaultTickLabels = {\n    type: \"outer\",\n    hideMinMax: false,\n    ticks: defaultTickList,\n    defaultTickValueConfig: defaultTickValueConfig,\n    defaultTickLineConfig: defaultTickLineConfig\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tick.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultTooltipStyle = void 0;\nexports.defaultTooltipStyle = {\n    borderColor: \"#5A5A5A\",\n    borderStyle: \"solid\",\n    borderWidth: \"1px\",\n    borderRadius: \"5px\",\n    color: \"white\",\n    padding: \"5px\",\n    fontSize: \"15px\",\n    textShadow: \"1px 1px 2px black, 0 0 1em black, 0 0 0.2em black\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9HYXVnZUNvbXBvbmVudC90eXBlcy9Ub29sdGlwLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdERCwyQkFBMkIsR0FBRyxLQUFLO0FBQ25DQSwyQkFBMkIsR0FBRztJQUMxQkcsYUFBYTtJQUNiQyxhQUFhO0lBQ2JDLGFBQWE7SUFDYkMsY0FBYztJQUNkQyxPQUFPO0lBQ1BDLFNBQVM7SUFDVEMsVUFBVTtJQUNWQyxZQUFZO0FBRWhCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9HYXVnZUNvbXBvbmVudC90eXBlcy9Ub29sdGlwLmpzP2Y3NTEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRlZmF1bHRUb29sdGlwU3R5bGUgPSB2b2lkIDA7XG5leHBvcnRzLmRlZmF1bHRUb29sdGlwU3R5bGUgPSB7XG4gICAgYm9yZGVyQ29sb3I6ICcjNUE1QTVBJyxcbiAgICBib3JkZXJTdHlsZTogJ3NvbGlkJyxcbiAgICBib3JkZXJXaWR0aDogJzFweCcsXG4gICAgYm9yZGVyUmFkaXVzOiAnNXB4JyxcbiAgICBjb2xvcjogJ3doaXRlJyxcbiAgICBwYWRkaW5nOiAnNXB4JyxcbiAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgIHRleHRTaGFkb3c6ICcxcHggMXB4IDJweCBibGFjaywgMCAwIDFlbSBibGFjaywgMCAwIDAuMmVtIGJsYWNrJ1xuICAgIC8vIGZvbnRTaXplOiAnMTVweCdcbn07XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJkZWZhdWx0VG9vbHRpcFN0eWxlIiwiYm9yZGVyQ29sb3IiLCJib3JkZXJTdHlsZSIsImJvcmRlcldpZHRoIiwiYm9yZGVyUmFkaXVzIiwiY29sb3IiLCJwYWRkaW5nIiwiZm9udFNpemUiLCJ0ZXh0U2hhZG93Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/types/Tooltip.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-gauge-component/dist/lib/index.js":
/*!******************************************************************!*\
  !*** ../../node_modules/react-gauge-component/dist/lib/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaugeComponent = void 0;\nvar GaugeComponent_1 = __importDefault(__webpack_require__(/*! ./GaugeComponent */ \"(ssr)/../../node_modules/react-gauge-component/dist/lib/GaugeComponent/index.js\"));\nexports.GaugeComponent = GaugeComponent_1.default;\nexports[\"default\"] = GaugeComponent_1.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWdhdWdlLWNvbXBvbmVudC9kaXN0L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLElBQUlBLGtCQUFrQixDQUFDLE1BQUcsS0FBSyxPQUFHLEVBQUVBLGVBQWUsSUFBSyxTQUFVQyxHQUFHO0lBQ2pFLE9BQU8sT0FBUUEsSUFBSUMsVUFBVSxHQUFJRCxNQUFNO1FBQUUsV0FBV0E7SUFBSTtBQUM1RDtBQUNBRSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQsc0JBQXNCLEdBQUcsS0FBSztBQUM5QixJQUFJRyxtQkFBbUJSLGdCQUFnQlMsbUJBQU9BLENBQUMseUdBQWtCO0FBQ2pFSixzQkFBc0IsR0FBR0csaUJBQWlCRSxPQUFPO0FBQ2pETCxrQkFBZSxHQUFHRyxpQkFBaUJFLE9BQU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvcmVhY3QtZ2F1Z2UtY29tcG9uZW50L2Rpc3QvbGliL2luZGV4LmpzP2JkZDkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkdhdWdlQ29tcG9uZW50ID0gdm9pZCAwO1xudmFyIEdhdWdlQ29tcG9uZW50XzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIi4vR2F1Z2VDb21wb25lbnRcIikpO1xuZXhwb3J0cy5HYXVnZUNvbXBvbmVudCA9IEdhdWdlQ29tcG9uZW50XzEuZGVmYXVsdDtcbmV4cG9ydHMuZGVmYXVsdCA9IEdhdWdlQ29tcG9uZW50XzEuZGVmYXVsdDtcbiJdLCJuYW1lcyI6WyJfX2ltcG9ydERlZmF1bHQiLCJtb2QiLCJfX2VzTW9kdWxlIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJHYXVnZUNvbXBvbmVudCIsIkdhdWdlQ29tcG9uZW50XzEiLCJyZXF1aXJlIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-gauge-component/dist/lib/index.js\n");

/***/ })

};
;