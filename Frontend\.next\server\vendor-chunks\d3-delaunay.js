"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-delaunay";
exports.ids = ["vendor-chunks/d3-delaunay"];
exports.modules = {

/***/ "(ssr)/../../node_modules/d3-delaunay/src/delaunay.js":
/*!******************************************************!*\
  !*** ../../node_modules/d3-delaunay/src/delaunay.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Delaunay)\n/* harmony export */ });\n/* harmony import */ var delaunator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! delaunator */ \"(ssr)/../../node_modules/delaunator/index.js\");\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./path.js */ \"(ssr)/../../node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/../../node_modules/d3-delaunay/src/polygon.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/../../node_modules/d3-delaunay/src/voronoi.js\");\n\n\n\n\nconst tau = 2 * Math.PI, pow = Math.pow;\nfunction pointX(p) {\n    return p[0];\n}\nfunction pointY(p) {\n    return p[1];\n}\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n    const { triangles, coords } = d;\n    for(let i = 0; i < triangles.length; i += 3){\n        const a = 2 * triangles[i], b = 2 * triangles[i + 1], c = 2 * triangles[i + 2], cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1]) - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n        if (cross > 1e-10) return false;\n    }\n    return true;\n}\nfunction jitter(x, y, r) {\n    return [\n        x + Math.sin(x + y) * r,\n        y + Math.cos(x - y) * r\n    ];\n}\nclass Delaunay {\n    static from(points, fx = pointX, fy = pointY, that) {\n        return new Delaunay(\"length\" in points ? flatArray(points, fx, fy, that) : Float64Array.from(flatIterable(points, fx, fy, that)));\n    }\n    constructor(points){\n        this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n        this.inedges = new Int32Array(points.length / 2);\n        this._hullIndex = new Int32Array(points.length / 2);\n        this.points = this._delaunator.coords;\n        this._init();\n    }\n    update() {\n        this._delaunator.update();\n        this._init();\n        return this;\n    }\n    _init() {\n        const d = this._delaunator, points = this.points;\n        // check for collinear\n        if (d.hull && d.hull.length > 2 && collinear(d)) {\n            this.collinear = Int32Array.from({\n                length: points.length / 2\n            }, (_, i)=>i).sort((i, j)=>points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n            const e = this.collinear[0], f = this.collinear[this.collinear.length - 1], bounds = [\n                points[2 * e],\n                points[2 * e + 1],\n                points[2 * f],\n                points[2 * f + 1]\n            ], r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n            for(let i = 0, n = points.length / 2; i < n; ++i){\n                const p = jitter(points[2 * i], points[2 * i + 1], r);\n                points[2 * i] = p[0];\n                points[2 * i + 1] = p[1];\n            }\n            this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n        } else {\n            delete this.collinear;\n        }\n        const halfedges = this.halfedges = this._delaunator.halfedges;\n        const hull = this.hull = this._delaunator.hull;\n        const triangles = this.triangles = this._delaunator.triangles;\n        const inedges = this.inedges.fill(-1);\n        const hullIndex = this._hullIndex.fill(-1);\n        // Compute an index from each point to an (arbitrary) incoming halfedge\n        // Used to give the first neighbor of each point; for this reason,\n        // on the hull we give priority to exterior halfedges\n        for(let e = 0, n = halfedges.length; e < n; ++e){\n            const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n            if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n        }\n        for(let i = 0, n = hull.length; i < n; ++i){\n            hullIndex[hull[i]] = i;\n        }\n        // degenerate case: 1 or 2 (distinct) points\n        if (hull.length <= 2 && hull.length > 0) {\n            this.triangles = new Int32Array(3).fill(-1);\n            this.halfedges = new Int32Array(3).fill(-1);\n            this.triangles[0] = hull[0];\n            inedges[hull[0]] = 1;\n            if (hull.length === 2) {\n                inedges[hull[1]] = 0;\n                this.triangles[1] = hull[1];\n                this.triangles[2] = hull[1];\n            }\n        }\n    }\n    voronoi(bounds) {\n        return new _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, bounds);\n    }\n    *neighbors(i) {\n        const { inedges, hull, _hullIndex, halfedges, triangles, collinear } = this;\n        // degenerate case with several collinear points\n        if (collinear) {\n            const l = collinear.indexOf(i);\n            if (l > 0) yield collinear[l - 1];\n            if (l < collinear.length - 1) yield collinear[l + 1];\n            return;\n        }\n        const e0 = inedges[i];\n        if (e0 === -1) return; // coincident point\n        let e = e0, p0 = -1;\n        do {\n            yield p0 = triangles[e];\n            e = e % 3 === 2 ? e - 2 : e + 1;\n            if (triangles[e] !== i) return; // bad triangulation\n            e = halfedges[e];\n            if (e === -1) {\n                const p = hull[(_hullIndex[i] + 1) % hull.length];\n                if (p !== p0) yield p;\n                return;\n            }\n        }while (e !== e0);\n    }\n    find(x, y, i = 0) {\n        if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n        const i0 = i;\n        let c;\n        while((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0)i = c;\n        return c;\n    }\n    _step(i, x, y) {\n        const { inedges, hull, _hullIndex, halfedges, triangles, points } = this;\n        if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n        let c = i;\n        let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n        const e0 = inedges[i];\n        let e = e0;\n        do {\n            let t = triangles[e];\n            const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n            if (dt < dc) dc = dt, c = t;\n            e = e % 3 === 2 ? e - 2 : e + 1;\n            if (triangles[e] !== i) break; // bad triangulation\n            e = halfedges[e];\n            if (e === -1) {\n                e = hull[(_hullIndex[i] + 1) % hull.length];\n                if (e !== t) {\n                    if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n                }\n                break;\n            }\n        }while (e !== e0);\n        return c;\n    }\n    render(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { points, halfedges, triangles } = this;\n        for(let i = 0, n = halfedges.length; i < n; ++i){\n            const j = halfedges[i];\n            if (j < i) continue;\n            const ti = triangles[i] * 2;\n            const tj = triangles[j] * 2;\n            context.moveTo(points[ti], points[ti + 1]);\n            context.lineTo(points[tj], points[tj + 1]);\n        }\n        this.renderHull(context);\n        return buffer && buffer.value();\n    }\n    renderPoints(context, r) {\n        if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n        r = r == undefined ? 2 : +r;\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { points } = this;\n        for(let i = 0, n = points.length; i < n; i += 2){\n            const x = points[i], y = points[i + 1];\n            context.moveTo(x + r, y);\n            context.arc(x, y, r, 0, tau);\n        }\n        return buffer && buffer.value();\n    }\n    renderHull(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { hull, points } = this;\n        const h = hull[0] * 2, n = hull.length;\n        context.moveTo(points[h], points[h + 1]);\n        for(let i = 1; i < n; ++i){\n            const h = 2 * hull[i];\n            context.lineTo(points[h], points[h + 1]);\n        }\n        context.closePath();\n        return buffer && buffer.value();\n    }\n    hullPolygon() {\n        const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        this.renderHull(polygon);\n        return polygon.value();\n    }\n    renderTriangle(i, context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n        const { points, triangles } = this;\n        const t0 = triangles[i *= 3] * 2;\n        const t1 = triangles[i + 1] * 2;\n        const t2 = triangles[i + 2] * 2;\n        context.moveTo(points[t0], points[t0 + 1]);\n        context.lineTo(points[t1], points[t1 + 1]);\n        context.lineTo(points[t2], points[t2 + 1]);\n        context.closePath();\n        return buffer && buffer.value();\n    }\n    *trianglePolygons() {\n        const { triangles } = this;\n        for(let i = 0, n = triangles.length / 3; i < n; ++i){\n            yield this.trianglePolygon(i);\n        }\n    }\n    trianglePolygon(i) {\n        const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        this.renderTriangle(i, polygon);\n        return polygon.value();\n    }\n}\nfunction flatArray(points, fx, fy, that) {\n    const n = points.length;\n    const array = new Float64Array(n * 2);\n    for(let i = 0; i < n; ++i){\n        const p = points[i];\n        array[i * 2] = fx.call(that, p, i, points);\n        array[i * 2 + 1] = fy.call(that, p, i, points);\n    }\n    return array;\n}\nfunction* flatIterable(points, fx, fy, that) {\n    let i = 0;\n    for (const p of points){\n        yield fx.call(that, p, i, points);\n        yield fy.call(that, p, i, points);\n        ++i;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWRlbGF1bmF5L3NyYy9kZWxhdW5heS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQztBQUNQO0FBQ007QUFDQTtBQUVuQyxNQUFNSSxNQUFNLElBQUlDLEtBQUtDLEVBQUUsRUFBRUMsTUFBTUYsS0FBS0UsR0FBRztBQUV2QyxTQUFTQyxPQUFPQyxDQUFDO0lBQ2YsT0FBT0EsQ0FBQyxDQUFDLEVBQUU7QUFDYjtBQUVBLFNBQVNDLE9BQU9ELENBQUM7SUFDZixPQUFPQSxDQUFDLENBQUMsRUFBRTtBQUNiO0FBRUEseUVBQXlFO0FBQ3pFLFNBQVNFLFVBQVVDLENBQUM7SUFDbEIsTUFBTSxFQUFDQyxTQUFTLEVBQUVDLE1BQU0sRUFBQyxHQUFHRjtJQUM1QixJQUFLLElBQUlHLElBQUksR0FBR0EsSUFBSUYsVUFBVUcsTUFBTSxFQUFFRCxLQUFLLEVBQUc7UUFDNUMsTUFBTUUsSUFBSSxJQUFJSixTQUFTLENBQUNFLEVBQUUsRUFDcEJHLElBQUksSUFBSUwsU0FBUyxDQUFDRSxJQUFJLEVBQUUsRUFDeEJJLElBQUksSUFBSU4sU0FBUyxDQUFDRSxJQUFJLEVBQUUsRUFDeEJLLFFBQVEsQ0FBQ04sTUFBTSxDQUFDSyxFQUFFLEdBQUdMLE1BQU0sQ0FBQ0csRUFBRSxJQUFLSCxDQUFBQSxNQUFNLENBQUNJLElBQUksRUFBRSxHQUFHSixNQUFNLENBQUNHLElBQUksRUFBRSxJQUN4RCxDQUFDSCxNQUFNLENBQUNJLEVBQUUsR0FBR0osTUFBTSxDQUFDRyxFQUFFLElBQUtILENBQUFBLE1BQU0sQ0FBQ0ssSUFBSSxFQUFFLEdBQUdMLE1BQU0sQ0FBQ0csSUFBSSxFQUFFO1FBQ3RFLElBQUlHLFFBQVEsT0FBTyxPQUFPO0lBQzVCO0lBQ0EsT0FBTztBQUNUO0FBRUEsU0FBU0MsT0FBT0MsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUM7SUFDckIsT0FBTztRQUFDRixJQUFJakIsS0FBS29CLEdBQUcsQ0FBQ0gsSUFBSUMsS0FBS0M7UUFBR0QsSUFBSWxCLEtBQUtxQixHQUFHLENBQUNKLElBQUlDLEtBQUtDO0tBQUU7QUFDM0Q7QUFFZSxNQUFNRztJQUNuQixPQUFPQyxLQUFLQyxNQUFNLEVBQUVDLEtBQUt0QixNQUFNLEVBQUV1QixLQUFLckIsTUFBTSxFQUFFc0IsSUFBSSxFQUFFO1FBQ2xELE9BQU8sSUFBSUwsU0FBUyxZQUFZRSxTQUMxQkksVUFBVUosUUFBUUMsSUFBSUMsSUFBSUMsUUFDMUJFLGFBQWFOLElBQUksQ0FBQ08sYUFBYU4sUUFBUUMsSUFBSUMsSUFBSUM7SUFDdkQ7SUFDQUksWUFBWVAsTUFBTSxDQUFFO1FBQ2xCLElBQUksQ0FBQ1EsV0FBVyxHQUFHLElBQUlyQyxrREFBVUEsQ0FBQzZCO1FBQ2xDLElBQUksQ0FBQ1MsT0FBTyxHQUFHLElBQUlDLFdBQVdWLE9BQU9iLE1BQU0sR0FBRztRQUM5QyxJQUFJLENBQUN3QixVQUFVLEdBQUcsSUFBSUQsV0FBV1YsT0FBT2IsTUFBTSxHQUFHO1FBQ2pELElBQUksQ0FBQ2EsTUFBTSxHQUFHLElBQUksQ0FBQ1EsV0FBVyxDQUFDdkIsTUFBTTtRQUNyQyxJQUFJLENBQUMyQixLQUFLO0lBQ1o7SUFDQUMsU0FBUztRQUNQLElBQUksQ0FBQ0wsV0FBVyxDQUFDSyxNQUFNO1FBQ3ZCLElBQUksQ0FBQ0QsS0FBSztRQUNWLE9BQU8sSUFBSTtJQUNiO0lBQ0FBLFFBQVE7UUFDTixNQUFNN0IsSUFBSSxJQUFJLENBQUN5QixXQUFXLEVBQUVSLFNBQVMsSUFBSSxDQUFDQSxNQUFNO1FBRWhELHNCQUFzQjtRQUN0QixJQUFJakIsRUFBRStCLElBQUksSUFBSS9CLEVBQUUrQixJQUFJLENBQUMzQixNQUFNLEdBQUcsS0FBS0wsVUFBVUMsSUFBSTtZQUMvQyxJQUFJLENBQUNELFNBQVMsR0FBRzRCLFdBQVdYLElBQUksQ0FBQztnQkFBQ1osUUFBUWEsT0FBT2IsTUFBTSxHQUFDO1lBQUMsR0FBRyxDQUFDNEIsR0FBRTdCLElBQU1BLEdBQ2xFOEIsSUFBSSxDQUFDLENBQUM5QixHQUFHK0IsSUFBTWpCLE1BQU0sQ0FBQyxJQUFJZCxFQUFFLEdBQUdjLE1BQU0sQ0FBQyxJQUFJaUIsRUFBRSxJQUFJakIsTUFBTSxDQUFDLElBQUlkLElBQUksRUFBRSxHQUFHYyxNQUFNLENBQUMsSUFBSWlCLElBQUksRUFBRSxHQUFHLHNCQUFzQjtZQUNqSCxNQUFNQyxJQUFJLElBQUksQ0FBQ3BDLFNBQVMsQ0FBQyxFQUFFLEVBQUVxQyxJQUFJLElBQUksQ0FBQ3JDLFNBQVMsQ0FBQyxJQUFJLENBQUNBLFNBQVMsQ0FBQ0ssTUFBTSxHQUFHLEVBQUUsRUFDeEVpQyxTQUFTO2dCQUFFcEIsTUFBTSxDQUFDLElBQUlrQixFQUFFO2dCQUFFbEIsTUFBTSxDQUFDLElBQUlrQixJQUFJLEVBQUU7Z0JBQUVsQixNQUFNLENBQUMsSUFBSW1CLEVBQUU7Z0JBQUVuQixNQUFNLENBQUMsSUFBSW1CLElBQUksRUFBRTthQUFFLEVBQy9FeEIsSUFBSSxPQUFPbkIsS0FBSzZDLEtBQUssQ0FBQ0QsTUFBTSxDQUFDLEVBQUUsR0FBR0EsTUFBTSxDQUFDLEVBQUUsRUFBRUEsTUFBTSxDQUFDLEVBQUUsR0FBR0EsTUFBTSxDQUFDLEVBQUU7WUFDcEUsSUFBSyxJQUFJbEMsSUFBSSxHQUFHb0MsSUFBSXRCLE9BQU9iLE1BQU0sR0FBRyxHQUFHRCxJQUFJb0MsR0FBRyxFQUFFcEMsRUFBRztnQkFDakQsTUFBTU4sSUFBSVksT0FBT1EsTUFBTSxDQUFDLElBQUlkLEVBQUUsRUFBRWMsTUFBTSxDQUFDLElBQUlkLElBQUksRUFBRSxFQUFFUztnQkFDbkRLLE1BQU0sQ0FBQyxJQUFJZCxFQUFFLEdBQUdOLENBQUMsQ0FBQyxFQUFFO2dCQUNwQm9CLE1BQU0sQ0FBQyxJQUFJZCxJQUFJLEVBQUUsR0FBR04sQ0FBQyxDQUFDLEVBQUU7WUFDMUI7WUFDQSxJQUFJLENBQUM0QixXQUFXLEdBQUcsSUFBSXJDLGtEQUFVQSxDQUFDNkI7UUFDcEMsT0FBTztZQUNMLE9BQU8sSUFBSSxDQUFDbEIsU0FBUztRQUN2QjtRQUVBLE1BQU15QyxZQUFZLElBQUksQ0FBQ0EsU0FBUyxHQUFHLElBQUksQ0FBQ2YsV0FBVyxDQUFDZSxTQUFTO1FBQzdELE1BQU1ULE9BQU8sSUFBSSxDQUFDQSxJQUFJLEdBQUcsSUFBSSxDQUFDTixXQUFXLENBQUNNLElBQUk7UUFDOUMsTUFBTTlCLFlBQVksSUFBSSxDQUFDQSxTQUFTLEdBQUcsSUFBSSxDQUFDd0IsV0FBVyxDQUFDeEIsU0FBUztRQUM3RCxNQUFNeUIsVUFBVSxJQUFJLENBQUNBLE9BQU8sQ0FBQ2UsSUFBSSxDQUFDLENBQUM7UUFDbkMsTUFBTUMsWUFBWSxJQUFJLENBQUNkLFVBQVUsQ0FBQ2EsSUFBSSxDQUFDLENBQUM7UUFFeEMsdUVBQXVFO1FBQ3ZFLGtFQUFrRTtRQUNsRSxxREFBcUQ7UUFDckQsSUFBSyxJQUFJTixJQUFJLEdBQUdJLElBQUlDLFVBQVVwQyxNQUFNLEVBQUUrQixJQUFJSSxHQUFHLEVBQUVKLEVBQUc7WUFDaEQsTUFBTXRDLElBQUlJLFNBQVMsQ0FBQ2tDLElBQUksTUFBTSxJQUFJQSxJQUFJLElBQUlBLElBQUksRUFBRTtZQUNoRCxJQUFJSyxTQUFTLENBQUNMLEVBQUUsS0FBSyxDQUFDLEtBQUtULE9BQU8sQ0FBQzdCLEVBQUUsS0FBSyxDQUFDLEdBQUc2QixPQUFPLENBQUM3QixFQUFFLEdBQUdzQztRQUM3RDtRQUNBLElBQUssSUFBSWhDLElBQUksR0FBR29DLElBQUlSLEtBQUszQixNQUFNLEVBQUVELElBQUlvQyxHQUFHLEVBQUVwQyxFQUFHO1lBQzNDdUMsU0FBUyxDQUFDWCxJQUFJLENBQUM1QixFQUFFLENBQUMsR0FBR0E7UUFDdkI7UUFFQSw0Q0FBNEM7UUFDNUMsSUFBSTRCLEtBQUszQixNQUFNLElBQUksS0FBSzJCLEtBQUszQixNQUFNLEdBQUcsR0FBRztZQUN2QyxJQUFJLENBQUNILFNBQVMsR0FBRyxJQUFJMEIsV0FBVyxHQUFHYyxJQUFJLENBQUMsQ0FBQztZQUN6QyxJQUFJLENBQUNELFNBQVMsR0FBRyxJQUFJYixXQUFXLEdBQUdjLElBQUksQ0FBQyxDQUFDO1lBQ3pDLElBQUksQ0FBQ3hDLFNBQVMsQ0FBQyxFQUFFLEdBQUc4QixJQUFJLENBQUMsRUFBRTtZQUMzQkwsT0FBTyxDQUFDSyxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUc7WUFDbkIsSUFBSUEsS0FBSzNCLE1BQU0sS0FBSyxHQUFHO2dCQUNyQnNCLE9BQU8sQ0FBQ0ssSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHO2dCQUNuQixJQUFJLENBQUM5QixTQUFTLENBQUMsRUFBRSxHQUFHOEIsSUFBSSxDQUFDLEVBQUU7Z0JBQzNCLElBQUksQ0FBQzlCLFNBQVMsQ0FBQyxFQUFFLEdBQUc4QixJQUFJLENBQUMsRUFBRTtZQUM3QjtRQUNGO0lBQ0Y7SUFDQVksUUFBUU4sTUFBTSxFQUFFO1FBQ2QsT0FBTyxJQUFJOUMsbURBQU9BLENBQUMsSUFBSSxFQUFFOEM7SUFDM0I7SUFDQSxDQUFDTyxVQUFVekMsQ0FBQyxFQUFFO1FBQ1osTUFBTSxFQUFDdUIsT0FBTyxFQUFFSyxJQUFJLEVBQUVILFVBQVUsRUFBRVksU0FBUyxFQUFFdkMsU0FBUyxFQUFFRixTQUFTLEVBQUMsR0FBRyxJQUFJO1FBRXpFLGdEQUFnRDtRQUNoRCxJQUFJQSxXQUFXO1lBQ2IsTUFBTThDLElBQUk5QyxVQUFVK0MsT0FBTyxDQUFDM0M7WUFDNUIsSUFBSTBDLElBQUksR0FBRyxNQUFNOUMsU0FBUyxDQUFDOEMsSUFBSSxFQUFFO1lBQ2pDLElBQUlBLElBQUk5QyxVQUFVSyxNQUFNLEdBQUcsR0FBRyxNQUFNTCxTQUFTLENBQUM4QyxJQUFJLEVBQUU7WUFDcEQ7UUFDRjtRQUVBLE1BQU1FLEtBQUtyQixPQUFPLENBQUN2QixFQUFFO1FBQ3JCLElBQUk0QyxPQUFPLENBQUMsR0FBRyxRQUFRLG1CQUFtQjtRQUMxQyxJQUFJWixJQUFJWSxJQUFJQyxLQUFLLENBQUM7UUFDbEIsR0FBRztZQUNELE1BQU1BLEtBQUsvQyxTQUFTLENBQUNrQyxFQUFFO1lBQ3ZCQSxJQUFJQSxJQUFJLE1BQU0sSUFBSUEsSUFBSSxJQUFJQSxJQUFJO1lBQzlCLElBQUlsQyxTQUFTLENBQUNrQyxFQUFFLEtBQUtoQyxHQUFHLFFBQVEsb0JBQW9CO1lBQ3BEZ0MsSUFBSUssU0FBUyxDQUFDTCxFQUFFO1lBQ2hCLElBQUlBLE1BQU0sQ0FBQyxHQUFHO2dCQUNaLE1BQU10QyxJQUFJa0MsSUFBSSxDQUFDLENBQUNILFVBQVUsQ0FBQ3pCLEVBQUUsR0FBRyxLQUFLNEIsS0FBSzNCLE1BQU0sQ0FBQztnQkFDakQsSUFBSVAsTUFBTW1ELElBQUksTUFBTW5EO2dCQUNwQjtZQUNGO1FBQ0YsUUFBU3NDLE1BQU1ZLElBQUk7SUFDckI7SUFDQUUsS0FBS3ZDLENBQUMsRUFBRUMsQ0FBQyxFQUFFUixJQUFJLENBQUMsRUFBRTtRQUNoQixJQUFJLENBQUNPLElBQUksQ0FBQ0EsR0FBR0EsTUFBTUEsQ0FBQUEsS0FBT0MsQ0FBQUEsSUFBSSxDQUFDQSxHQUFHQSxNQUFNQSxDQUFBQSxHQUFJLE9BQU8sQ0FBQztRQUNwRCxNQUFNdUMsS0FBSy9DO1FBQ1gsSUFBSUk7UUFDSixNQUFPLENBQUNBLElBQUksSUFBSSxDQUFDNEMsS0FBSyxDQUFDaEQsR0FBR08sR0FBR0MsRUFBQyxLQUFNLEtBQUtKLE1BQU1KLEtBQUtJLE1BQU0yQyxHQUFJL0MsSUFBSUk7UUFDbEUsT0FBT0E7SUFDVDtJQUNBNEMsTUFBTWhELENBQUMsRUFBRU8sQ0FBQyxFQUFFQyxDQUFDLEVBQUU7UUFDYixNQUFNLEVBQUNlLE9BQU8sRUFBRUssSUFBSSxFQUFFSCxVQUFVLEVBQUVZLFNBQVMsRUFBRXZDLFNBQVMsRUFBRWdCLE1BQU0sRUFBQyxHQUFHLElBQUk7UUFDdEUsSUFBSVMsT0FBTyxDQUFDdkIsRUFBRSxLQUFLLENBQUMsS0FBSyxDQUFDYyxPQUFPYixNQUFNLEVBQUUsT0FBTyxDQUFDRCxJQUFJLEtBQU1jLENBQUFBLE9BQU9iLE1BQU0sSUFBSTtRQUM1RSxJQUFJRyxJQUFJSjtRQUNSLElBQUlpRCxLQUFLekQsSUFBSWUsSUFBSU8sTUFBTSxDQUFDZCxJQUFJLEVBQUUsRUFBRSxLQUFLUixJQUFJZ0IsSUFBSU0sTUFBTSxDQUFDZCxJQUFJLElBQUksRUFBRSxFQUFFO1FBQ2hFLE1BQU00QyxLQUFLckIsT0FBTyxDQUFDdkIsRUFBRTtRQUNyQixJQUFJZ0MsSUFBSVk7UUFDUixHQUFHO1lBQ0QsSUFBSU0sSUFBSXBELFNBQVMsQ0FBQ2tDLEVBQUU7WUFDcEIsTUFBTW1CLEtBQUszRCxJQUFJZSxJQUFJTyxNQUFNLENBQUNvQyxJQUFJLEVBQUUsRUFBRSxLQUFLMUQsSUFBSWdCLElBQUlNLE1BQU0sQ0FBQ29DLElBQUksSUFBSSxFQUFFLEVBQUU7WUFDbEUsSUFBSUMsS0FBS0YsSUFBSUEsS0FBS0UsSUFBSS9DLElBQUk4QztZQUMxQmxCLElBQUlBLElBQUksTUFBTSxJQUFJQSxJQUFJLElBQUlBLElBQUk7WUFDOUIsSUFBSWxDLFNBQVMsQ0FBQ2tDLEVBQUUsS0FBS2hDLEdBQUcsT0FBTyxvQkFBb0I7WUFDbkRnQyxJQUFJSyxTQUFTLENBQUNMLEVBQUU7WUFDaEIsSUFBSUEsTUFBTSxDQUFDLEdBQUc7Z0JBQ1pBLElBQUlKLElBQUksQ0FBQyxDQUFDSCxVQUFVLENBQUN6QixFQUFFLEdBQUcsS0FBSzRCLEtBQUszQixNQUFNLENBQUM7Z0JBQzNDLElBQUkrQixNQUFNa0IsR0FBRztvQkFDWCxJQUFJMUQsSUFBSWUsSUFBSU8sTUFBTSxDQUFDa0IsSUFBSSxFQUFFLEVBQUUsS0FBS3hDLElBQUlnQixJQUFJTSxNQUFNLENBQUNrQixJQUFJLElBQUksRUFBRSxFQUFFLEtBQUtpQixJQUFJLE9BQU9qQjtnQkFDN0U7Z0JBQ0E7WUFDRjtRQUNGLFFBQVNBLE1BQU1ZLElBQUk7UUFDbkIsT0FBT3hDO0lBQ1Q7SUFDQWdELE9BQU9DLE9BQU8sRUFBRTtRQUNkLE1BQU1DLFNBQVNELFdBQVcsT0FBT0EsVUFBVSxJQUFJbkUsZ0RBQUlBLEdBQUdxRTtRQUN0RCxNQUFNLEVBQUN6QyxNQUFNLEVBQUV1QixTQUFTLEVBQUV2QyxTQUFTLEVBQUMsR0FBRyxJQUFJO1FBQzNDLElBQUssSUFBSUUsSUFBSSxHQUFHb0MsSUFBSUMsVUFBVXBDLE1BQU0sRUFBRUQsSUFBSW9DLEdBQUcsRUFBRXBDLEVBQUc7WUFDaEQsTUFBTStCLElBQUlNLFNBQVMsQ0FBQ3JDLEVBQUU7WUFDdEIsSUFBSStCLElBQUkvQixHQUFHO1lBQ1gsTUFBTXdELEtBQUsxRCxTQUFTLENBQUNFLEVBQUUsR0FBRztZQUMxQixNQUFNeUQsS0FBSzNELFNBQVMsQ0FBQ2lDLEVBQUUsR0FBRztZQUMxQnNCLFFBQVFLLE1BQU0sQ0FBQzVDLE1BQU0sQ0FBQzBDLEdBQUcsRUFBRTFDLE1BQU0sQ0FBQzBDLEtBQUssRUFBRTtZQUN6Q0gsUUFBUU0sTUFBTSxDQUFDN0MsTUFBTSxDQUFDMkMsR0FBRyxFQUFFM0MsTUFBTSxDQUFDMkMsS0FBSyxFQUFFO1FBQzNDO1FBQ0EsSUFBSSxDQUFDRyxVQUFVLENBQUNQO1FBQ2hCLE9BQU9DLFVBQVVBLE9BQU9PLEtBQUs7SUFDL0I7SUFDQUMsYUFBYVQsT0FBTyxFQUFFNUMsQ0FBQyxFQUFFO1FBQ3ZCLElBQUlBLE1BQU04QyxhQUFjLEVBQUNGLFdBQVcsT0FBT0EsUUFBUUssTUFBTSxLQUFLLFVBQVMsR0FBSWpELElBQUk0QyxTQUFTQSxVQUFVO1FBQ2xHNUMsSUFBSUEsS0FBSzhDLFlBQVksSUFBSSxDQUFDOUM7UUFDMUIsTUFBTTZDLFNBQVNELFdBQVcsT0FBT0EsVUFBVSxJQUFJbkUsZ0RBQUlBLEdBQUdxRTtRQUN0RCxNQUFNLEVBQUN6QyxNQUFNLEVBQUMsR0FBRyxJQUFJO1FBQ3JCLElBQUssSUFBSWQsSUFBSSxHQUFHb0MsSUFBSXRCLE9BQU9iLE1BQU0sRUFBRUQsSUFBSW9DLEdBQUdwQyxLQUFLLEVBQUc7WUFDaEQsTUFBTU8sSUFBSU8sTUFBTSxDQUFDZCxFQUFFLEVBQUVRLElBQUlNLE1BQU0sQ0FBQ2QsSUFBSSxFQUFFO1lBQ3RDcUQsUUFBUUssTUFBTSxDQUFDbkQsSUFBSUUsR0FBR0Q7WUFDdEI2QyxRQUFRVSxHQUFHLENBQUN4RCxHQUFHQyxHQUFHQyxHQUFHLEdBQUdwQjtRQUMxQjtRQUNBLE9BQU9pRSxVQUFVQSxPQUFPTyxLQUFLO0lBQy9CO0lBQ0FELFdBQVdQLE9BQU8sRUFBRTtRQUNsQixNQUFNQyxTQUFTRCxXQUFXLE9BQU9BLFVBQVUsSUFBSW5FLGdEQUFJQSxHQUFHcUU7UUFDdEQsTUFBTSxFQUFDM0IsSUFBSSxFQUFFZCxNQUFNLEVBQUMsR0FBRyxJQUFJO1FBQzNCLE1BQU1rRCxJQUFJcEMsSUFBSSxDQUFDLEVBQUUsR0FBRyxHQUFHUSxJQUFJUixLQUFLM0IsTUFBTTtRQUN0Q29ELFFBQVFLLE1BQU0sQ0FBQzVDLE1BQU0sQ0FBQ2tELEVBQUUsRUFBRWxELE1BQU0sQ0FBQ2tELElBQUksRUFBRTtRQUN2QyxJQUFLLElBQUloRSxJQUFJLEdBQUdBLElBQUlvQyxHQUFHLEVBQUVwQyxFQUFHO1lBQzFCLE1BQU1nRSxJQUFJLElBQUlwQyxJQUFJLENBQUM1QixFQUFFO1lBQ3JCcUQsUUFBUU0sTUFBTSxDQUFDN0MsTUFBTSxDQUFDa0QsRUFBRSxFQUFFbEQsTUFBTSxDQUFDa0QsSUFBSSxFQUFFO1FBQ3pDO1FBQ0FYLFFBQVFZLFNBQVM7UUFDakIsT0FBT1gsVUFBVUEsT0FBT08sS0FBSztJQUMvQjtJQUNBSyxjQUFjO1FBQ1osTUFBTUMsVUFBVSxJQUFJaEYsbURBQU9BO1FBQzNCLElBQUksQ0FBQ3lFLFVBQVUsQ0FBQ087UUFDaEIsT0FBT0EsUUFBUU4sS0FBSztJQUN0QjtJQUNBTyxlQUFlcEUsQ0FBQyxFQUFFcUQsT0FBTyxFQUFFO1FBQ3pCLE1BQU1DLFNBQVNELFdBQVcsT0FBT0EsVUFBVSxJQUFJbkUsZ0RBQUlBLEdBQUdxRTtRQUN0RCxNQUFNLEVBQUN6QyxNQUFNLEVBQUVoQixTQUFTLEVBQUMsR0FBRyxJQUFJO1FBQ2hDLE1BQU11RSxLQUFLdkUsU0FBUyxDQUFDRSxLQUFLLEVBQUUsR0FBRztRQUMvQixNQUFNc0UsS0FBS3hFLFNBQVMsQ0FBQ0UsSUFBSSxFQUFFLEdBQUc7UUFDOUIsTUFBTXVFLEtBQUt6RSxTQUFTLENBQUNFLElBQUksRUFBRSxHQUFHO1FBQzlCcUQsUUFBUUssTUFBTSxDQUFDNUMsTUFBTSxDQUFDdUQsR0FBRyxFQUFFdkQsTUFBTSxDQUFDdUQsS0FBSyxFQUFFO1FBQ3pDaEIsUUFBUU0sTUFBTSxDQUFDN0MsTUFBTSxDQUFDd0QsR0FBRyxFQUFFeEQsTUFBTSxDQUFDd0QsS0FBSyxFQUFFO1FBQ3pDakIsUUFBUU0sTUFBTSxDQUFDN0MsTUFBTSxDQUFDeUQsR0FBRyxFQUFFekQsTUFBTSxDQUFDeUQsS0FBSyxFQUFFO1FBQ3pDbEIsUUFBUVksU0FBUztRQUNqQixPQUFPWCxVQUFVQSxPQUFPTyxLQUFLO0lBQy9CO0lBQ0EsQ0FBQ1csbUJBQW1CO1FBQ2xCLE1BQU0sRUFBQzFFLFNBQVMsRUFBQyxHQUFHLElBQUk7UUFDeEIsSUFBSyxJQUFJRSxJQUFJLEdBQUdvQyxJQUFJdEMsVUFBVUcsTUFBTSxHQUFHLEdBQUdELElBQUlvQyxHQUFHLEVBQUVwQyxFQUFHO1lBQ3BELE1BQU0sSUFBSSxDQUFDeUUsZUFBZSxDQUFDekU7UUFDN0I7SUFDRjtJQUNBeUUsZ0JBQWdCekUsQ0FBQyxFQUFFO1FBQ2pCLE1BQU1tRSxVQUFVLElBQUloRixtREFBT0E7UUFDM0IsSUFBSSxDQUFDaUYsY0FBYyxDQUFDcEUsR0FBR21FO1FBQ3ZCLE9BQU9BLFFBQVFOLEtBQUs7SUFDdEI7QUFDRjtBQUVBLFNBQVMzQyxVQUFVSixNQUFNLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxJQUFJO0lBQ3JDLE1BQU1tQixJQUFJdEIsT0FBT2IsTUFBTTtJQUN2QixNQUFNeUUsUUFBUSxJQUFJdkQsYUFBYWlCLElBQUk7SUFDbkMsSUFBSyxJQUFJcEMsSUFBSSxHQUFHQSxJQUFJb0MsR0FBRyxFQUFFcEMsRUFBRztRQUMxQixNQUFNTixJQUFJb0IsTUFBTSxDQUFDZCxFQUFFO1FBQ25CMEUsS0FBSyxDQUFDMUUsSUFBSSxFQUFFLEdBQUdlLEdBQUc0RCxJQUFJLENBQUMxRCxNQUFNdkIsR0FBR00sR0FBR2M7UUFDbkM0RCxLQUFLLENBQUMxRSxJQUFJLElBQUksRUFBRSxHQUFHZ0IsR0FBRzJELElBQUksQ0FBQzFELE1BQU12QixHQUFHTSxHQUFHYztJQUN6QztJQUNBLE9BQU80RDtBQUNUO0FBRUEsVUFBVXRELGFBQWFOLE1BQU0sRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLElBQUk7SUFDekMsSUFBSWpCLElBQUk7SUFDUixLQUFLLE1BQU1OLEtBQUtvQixPQUFRO1FBQ3RCLE1BQU1DLEdBQUc0RCxJQUFJLENBQUMxRCxNQUFNdkIsR0FBR00sR0FBR2M7UUFDMUIsTUFBTUUsR0FBRzJELElBQUksQ0FBQzFELE1BQU12QixHQUFHTSxHQUFHYztRQUMxQixFQUFFZDtJQUNKO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL2RlbGF1bmF5LmpzP2Y4YTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IERlbGF1bmF0b3IgZnJvbSBcImRlbGF1bmF0b3JcIjtcbmltcG9ydCBQYXRoIGZyb20gXCIuL3BhdGguanNcIjtcbmltcG9ydCBQb2x5Z29uIGZyb20gXCIuL3BvbHlnb24uanNcIjtcbmltcG9ydCBWb3Jvbm9pIGZyb20gXCIuL3Zvcm9ub2kuanNcIjtcblxuY29uc3QgdGF1ID0gMiAqIE1hdGguUEksIHBvdyA9IE1hdGgucG93O1xuXG5mdW5jdGlvbiBwb2ludFgocCkge1xuICByZXR1cm4gcFswXTtcbn1cblxuZnVuY3Rpb24gcG9pbnRZKHApIHtcbiAgcmV0dXJuIHBbMV07XG59XG5cbi8vIEEgdHJpYW5ndWxhdGlvbiBpcyBjb2xsaW5lYXIgaWYgYWxsIGl0cyB0cmlhbmdsZXMgaGF2ZSBhIG5vbi1udWxsIGFyZWFcbmZ1bmN0aW9uIGNvbGxpbmVhcihkKSB7XG4gIGNvbnN0IHt0cmlhbmdsZXMsIGNvb3Jkc30gPSBkO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHRyaWFuZ2xlcy5sZW5ndGg7IGkgKz0gMykge1xuICAgIGNvbnN0IGEgPSAyICogdHJpYW5nbGVzW2ldLFxuICAgICAgICAgIGIgPSAyICogdHJpYW5nbGVzW2kgKyAxXSxcbiAgICAgICAgICBjID0gMiAqIHRyaWFuZ2xlc1tpICsgMl0sXG4gICAgICAgICAgY3Jvc3MgPSAoY29vcmRzW2NdIC0gY29vcmRzW2FdKSAqIChjb29yZHNbYiArIDFdIC0gY29vcmRzW2EgKyAxXSlcbiAgICAgICAgICAgICAgICAtIChjb29yZHNbYl0gLSBjb29yZHNbYV0pICogKGNvb3Jkc1tjICsgMV0gLSBjb29yZHNbYSArIDFdKTtcbiAgICBpZiAoY3Jvc3MgPiAxZS0xMCkgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHJldHVybiB0cnVlO1xufVxuXG5mdW5jdGlvbiBqaXR0ZXIoeCwgeSwgcikge1xuICByZXR1cm4gW3ggKyBNYXRoLnNpbih4ICsgeSkgKiByLCB5ICsgTWF0aC5jb3MoeCAtIHkpICogcl07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGNsYXNzIERlbGF1bmF5IHtcbiAgc3RhdGljIGZyb20ocG9pbnRzLCBmeCA9IHBvaW50WCwgZnkgPSBwb2ludFksIHRoYXQpIHtcbiAgICByZXR1cm4gbmV3IERlbGF1bmF5KFwibGVuZ3RoXCIgaW4gcG9pbnRzXG4gICAgICAgID8gZmxhdEFycmF5KHBvaW50cywgZngsIGZ5LCB0aGF0KVxuICAgICAgICA6IEZsb2F0NjRBcnJheS5mcm9tKGZsYXRJdGVyYWJsZShwb2ludHMsIGZ4LCBmeSwgdGhhdCkpKTtcbiAgfVxuICBjb25zdHJ1Y3Rvcihwb2ludHMpIHtcbiAgICB0aGlzLl9kZWxhdW5hdG9yID0gbmV3IERlbGF1bmF0b3IocG9pbnRzKTtcbiAgICB0aGlzLmluZWRnZXMgPSBuZXcgSW50MzJBcnJheShwb2ludHMubGVuZ3RoIC8gMik7XG4gICAgdGhpcy5faHVsbEluZGV4ID0gbmV3IEludDMyQXJyYXkocG9pbnRzLmxlbmd0aCAvIDIpO1xuICAgIHRoaXMucG9pbnRzID0gdGhpcy5fZGVsYXVuYXRvci5jb29yZHM7XG4gICAgdGhpcy5faW5pdCgpO1xuICB9XG4gIHVwZGF0ZSgpIHtcbiAgICB0aGlzLl9kZWxhdW5hdG9yLnVwZGF0ZSgpO1xuICAgIHRoaXMuX2luaXQoKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICBfaW5pdCgpIHtcbiAgICBjb25zdCBkID0gdGhpcy5fZGVsYXVuYXRvciwgcG9pbnRzID0gdGhpcy5wb2ludHM7XG5cbiAgICAvLyBjaGVjayBmb3IgY29sbGluZWFyXG4gICAgaWYgKGQuaHVsbCAmJiBkLmh1bGwubGVuZ3RoID4gMiAmJiBjb2xsaW5lYXIoZCkpIHtcbiAgICAgIHRoaXMuY29sbGluZWFyID0gSW50MzJBcnJheS5mcm9tKHtsZW5ndGg6IHBvaW50cy5sZW5ndGgvMn0sIChfLGkpID0+IGkpXG4gICAgICAgIC5zb3J0KChpLCBqKSA9PiBwb2ludHNbMiAqIGldIC0gcG9pbnRzWzIgKiBqXSB8fCBwb2ludHNbMiAqIGkgKyAxXSAtIHBvaW50c1syICogaiArIDFdKTsgLy8gZm9yIGV4YWN0IG5laWdoYm9yc1xuICAgICAgY29uc3QgZSA9IHRoaXMuY29sbGluZWFyWzBdLCBmID0gdGhpcy5jb2xsaW5lYXJbdGhpcy5jb2xsaW5lYXIubGVuZ3RoIC0gMV0sXG4gICAgICAgIGJvdW5kcyA9IFsgcG9pbnRzWzIgKiBlXSwgcG9pbnRzWzIgKiBlICsgMV0sIHBvaW50c1syICogZl0sIHBvaW50c1syICogZiArIDFdIF0sXG4gICAgICAgIHIgPSAxZS04ICogTWF0aC5oeXBvdChib3VuZHNbM10gLSBib3VuZHNbMV0sIGJvdW5kc1syXSAtIGJvdW5kc1swXSk7XG4gICAgICBmb3IgKGxldCBpID0gMCwgbiA9IHBvaW50cy5sZW5ndGggLyAyOyBpIDwgbjsgKytpKSB7XG4gICAgICAgIGNvbnN0IHAgPSBqaXR0ZXIocG9pbnRzWzIgKiBpXSwgcG9pbnRzWzIgKiBpICsgMV0sIHIpO1xuICAgICAgICBwb2ludHNbMiAqIGldID0gcFswXTtcbiAgICAgICAgcG9pbnRzWzIgKiBpICsgMV0gPSBwWzFdO1xuICAgICAgfVxuICAgICAgdGhpcy5fZGVsYXVuYXRvciA9IG5ldyBEZWxhdW5hdG9yKHBvaW50cyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGRlbGV0ZSB0aGlzLmNvbGxpbmVhcjtcbiAgICB9XG5cbiAgICBjb25zdCBoYWxmZWRnZXMgPSB0aGlzLmhhbGZlZGdlcyA9IHRoaXMuX2RlbGF1bmF0b3IuaGFsZmVkZ2VzO1xuICAgIGNvbnN0IGh1bGwgPSB0aGlzLmh1bGwgPSB0aGlzLl9kZWxhdW5hdG9yLmh1bGw7XG4gICAgY29uc3QgdHJpYW5nbGVzID0gdGhpcy50cmlhbmdsZXMgPSB0aGlzLl9kZWxhdW5hdG9yLnRyaWFuZ2xlcztcbiAgICBjb25zdCBpbmVkZ2VzID0gdGhpcy5pbmVkZ2VzLmZpbGwoLTEpO1xuICAgIGNvbnN0IGh1bGxJbmRleCA9IHRoaXMuX2h1bGxJbmRleC5maWxsKC0xKTtcblxuICAgIC8vIENvbXB1dGUgYW4gaW5kZXggZnJvbSBlYWNoIHBvaW50IHRvIGFuIChhcmJpdHJhcnkpIGluY29taW5nIGhhbGZlZGdlXG4gICAgLy8gVXNlZCB0byBnaXZlIHRoZSBmaXJzdCBuZWlnaGJvciBvZiBlYWNoIHBvaW50OyBmb3IgdGhpcyByZWFzb24sXG4gICAgLy8gb24gdGhlIGh1bGwgd2UgZ2l2ZSBwcmlvcml0eSB0byBleHRlcmlvciBoYWxmZWRnZXNcbiAgICBmb3IgKGxldCBlID0gMCwgbiA9IGhhbGZlZGdlcy5sZW5ndGg7IGUgPCBuOyArK2UpIHtcbiAgICAgIGNvbnN0IHAgPSB0cmlhbmdsZXNbZSAlIDMgPT09IDIgPyBlIC0gMiA6IGUgKyAxXTtcbiAgICAgIGlmIChoYWxmZWRnZXNbZV0gPT09IC0xIHx8IGluZWRnZXNbcF0gPT09IC0xKSBpbmVkZ2VzW3BdID0gZTtcbiAgICB9XG4gICAgZm9yIChsZXQgaSA9IDAsIG4gPSBodWxsLmxlbmd0aDsgaSA8IG47ICsraSkge1xuICAgICAgaHVsbEluZGV4W2h1bGxbaV1dID0gaTtcbiAgICB9XG5cbiAgICAvLyBkZWdlbmVyYXRlIGNhc2U6IDEgb3IgMiAoZGlzdGluY3QpIHBvaW50c1xuICAgIGlmIChodWxsLmxlbmd0aCA8PSAyICYmIGh1bGwubGVuZ3RoID4gMCkge1xuICAgICAgdGhpcy50cmlhbmdsZXMgPSBuZXcgSW50MzJBcnJheSgzKS5maWxsKC0xKTtcbiAgICAgIHRoaXMuaGFsZmVkZ2VzID0gbmV3IEludDMyQXJyYXkoMykuZmlsbCgtMSk7XG4gICAgICB0aGlzLnRyaWFuZ2xlc1swXSA9IGh1bGxbMF07XG4gICAgICBpbmVkZ2VzW2h1bGxbMF1dID0gMTtcbiAgICAgIGlmIChodWxsLmxlbmd0aCA9PT0gMikge1xuICAgICAgICBpbmVkZ2VzW2h1bGxbMV1dID0gMDtcbiAgICAgICAgdGhpcy50cmlhbmdsZXNbMV0gPSBodWxsWzFdO1xuICAgICAgICB0aGlzLnRyaWFuZ2xlc1syXSA9IGh1bGxbMV07XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHZvcm9ub2koYm91bmRzKSB7XG4gICAgcmV0dXJuIG5ldyBWb3Jvbm9pKHRoaXMsIGJvdW5kcyk7XG4gIH1cbiAgKm5laWdoYm9ycyhpKSB7XG4gICAgY29uc3Qge2luZWRnZXMsIGh1bGwsIF9odWxsSW5kZXgsIGhhbGZlZGdlcywgdHJpYW5nbGVzLCBjb2xsaW5lYXJ9ID0gdGhpcztcblxuICAgIC8vIGRlZ2VuZXJhdGUgY2FzZSB3aXRoIHNldmVyYWwgY29sbGluZWFyIHBvaW50c1xuICAgIGlmIChjb2xsaW5lYXIpIHtcbiAgICAgIGNvbnN0IGwgPSBjb2xsaW5lYXIuaW5kZXhPZihpKTtcbiAgICAgIGlmIChsID4gMCkgeWllbGQgY29sbGluZWFyW2wgLSAxXTtcbiAgICAgIGlmIChsIDwgY29sbGluZWFyLmxlbmd0aCAtIDEpIHlpZWxkIGNvbGxpbmVhcltsICsgMV07XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgZTAgPSBpbmVkZ2VzW2ldO1xuICAgIGlmIChlMCA9PT0gLTEpIHJldHVybjsgLy8gY29pbmNpZGVudCBwb2ludFxuICAgIGxldCBlID0gZTAsIHAwID0gLTE7XG4gICAgZG8ge1xuICAgICAgeWllbGQgcDAgPSB0cmlhbmdsZXNbZV07XG4gICAgICBlID0gZSAlIDMgPT09IDIgPyBlIC0gMiA6IGUgKyAxO1xuICAgICAgaWYgKHRyaWFuZ2xlc1tlXSAhPT0gaSkgcmV0dXJuOyAvLyBiYWQgdHJpYW5ndWxhdGlvblxuICAgICAgZSA9IGhhbGZlZGdlc1tlXTtcbiAgICAgIGlmIChlID09PSAtMSkge1xuICAgICAgICBjb25zdCBwID0gaHVsbFsoX2h1bGxJbmRleFtpXSArIDEpICUgaHVsbC5sZW5ndGhdO1xuICAgICAgICBpZiAocCAhPT0gcDApIHlpZWxkIHA7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9IHdoaWxlIChlICE9PSBlMCk7XG4gIH1cbiAgZmluZCh4LCB5LCBpID0gMCkge1xuICAgIGlmICgoeCA9ICt4LCB4ICE9PSB4KSB8fCAoeSA9ICt5LCB5ICE9PSB5KSkgcmV0dXJuIC0xO1xuICAgIGNvbnN0IGkwID0gaTtcbiAgICBsZXQgYztcbiAgICB3aGlsZSAoKGMgPSB0aGlzLl9zdGVwKGksIHgsIHkpKSA+PSAwICYmIGMgIT09IGkgJiYgYyAhPT0gaTApIGkgPSBjO1xuICAgIHJldHVybiBjO1xuICB9XG4gIF9zdGVwKGksIHgsIHkpIHtcbiAgICBjb25zdCB7aW5lZGdlcywgaHVsbCwgX2h1bGxJbmRleCwgaGFsZmVkZ2VzLCB0cmlhbmdsZXMsIHBvaW50c30gPSB0aGlzO1xuICAgIGlmIChpbmVkZ2VzW2ldID09PSAtMSB8fCAhcG9pbnRzLmxlbmd0aCkgcmV0dXJuIChpICsgMSkgJSAocG9pbnRzLmxlbmd0aCA+PiAxKTtcbiAgICBsZXQgYyA9IGk7XG4gICAgbGV0IGRjID0gcG93KHggLSBwb2ludHNbaSAqIDJdLCAyKSArIHBvdyh5IC0gcG9pbnRzW2kgKiAyICsgMV0sIDIpO1xuICAgIGNvbnN0IGUwID0gaW5lZGdlc1tpXTtcbiAgICBsZXQgZSA9IGUwO1xuICAgIGRvIHtcbiAgICAgIGxldCB0ID0gdHJpYW5nbGVzW2VdO1xuICAgICAgY29uc3QgZHQgPSBwb3coeCAtIHBvaW50c1t0ICogMl0sIDIpICsgcG93KHkgLSBwb2ludHNbdCAqIDIgKyAxXSwgMik7XG4gICAgICBpZiAoZHQgPCBkYykgZGMgPSBkdCwgYyA9IHQ7XG4gICAgICBlID0gZSAlIDMgPT09IDIgPyBlIC0gMiA6IGUgKyAxO1xuICAgICAgaWYgKHRyaWFuZ2xlc1tlXSAhPT0gaSkgYnJlYWs7IC8vIGJhZCB0cmlhbmd1bGF0aW9uXG4gICAgICBlID0gaGFsZmVkZ2VzW2VdO1xuICAgICAgaWYgKGUgPT09IC0xKSB7XG4gICAgICAgIGUgPSBodWxsWyhfaHVsbEluZGV4W2ldICsgMSkgJSBodWxsLmxlbmd0aF07XG4gICAgICAgIGlmIChlICE9PSB0KSB7XG4gICAgICAgICAgaWYgKHBvdyh4IC0gcG9pbnRzW2UgKiAyXSwgMikgKyBwb3coeSAtIHBvaW50c1tlICogMiArIDFdLCAyKSA8IGRjKSByZXR1cm4gZTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9IHdoaWxlIChlICE9PSBlMCk7XG4gICAgcmV0dXJuIGM7XG4gIH1cbiAgcmVuZGVyKGNvbnRleHQpIHtcbiAgICBjb25zdCBidWZmZXIgPSBjb250ZXh0ID09IG51bGwgPyBjb250ZXh0ID0gbmV3IFBhdGggOiB1bmRlZmluZWQ7XG4gICAgY29uc3Qge3BvaW50cywgaGFsZmVkZ2VzLCB0cmlhbmdsZXN9ID0gdGhpcztcbiAgICBmb3IgKGxldCBpID0gMCwgbiA9IGhhbGZlZGdlcy5sZW5ndGg7IGkgPCBuOyArK2kpIHtcbiAgICAgIGNvbnN0IGogPSBoYWxmZWRnZXNbaV07XG4gICAgICBpZiAoaiA8IGkpIGNvbnRpbnVlO1xuICAgICAgY29uc3QgdGkgPSB0cmlhbmdsZXNbaV0gKiAyO1xuICAgICAgY29uc3QgdGogPSB0cmlhbmdsZXNbal0gKiAyO1xuICAgICAgY29udGV4dC5tb3ZlVG8ocG9pbnRzW3RpXSwgcG9pbnRzW3RpICsgMV0pO1xuICAgICAgY29udGV4dC5saW5lVG8ocG9pbnRzW3RqXSwgcG9pbnRzW3RqICsgMV0pO1xuICAgIH1cbiAgICB0aGlzLnJlbmRlckh1bGwoY29udGV4dCk7XG4gICAgcmV0dXJuIGJ1ZmZlciAmJiBidWZmZXIudmFsdWUoKTtcbiAgfVxuICByZW5kZXJQb2ludHMoY29udGV4dCwgcikge1xuICAgIGlmIChyID09PSB1bmRlZmluZWQgJiYgKCFjb250ZXh0IHx8IHR5cGVvZiBjb250ZXh0Lm1vdmVUbyAhPT0gXCJmdW5jdGlvblwiKSkgciA9IGNvbnRleHQsIGNvbnRleHQgPSBudWxsO1xuICAgIHIgPSByID09IHVuZGVmaW5lZCA/IDIgOiArcjtcbiAgICBjb25zdCBidWZmZXIgPSBjb250ZXh0ID09IG51bGwgPyBjb250ZXh0ID0gbmV3IFBhdGggOiB1bmRlZmluZWQ7XG4gICAgY29uc3Qge3BvaW50c30gPSB0aGlzO1xuICAgIGZvciAobGV0IGkgPSAwLCBuID0gcG9pbnRzLmxlbmd0aDsgaSA8IG47IGkgKz0gMikge1xuICAgICAgY29uc3QgeCA9IHBvaW50c1tpXSwgeSA9IHBvaW50c1tpICsgMV07XG4gICAgICBjb250ZXh0Lm1vdmVUbyh4ICsgciwgeSk7XG4gICAgICBjb250ZXh0LmFyYyh4LCB5LCByLCAwLCB0YXUpO1xuICAgIH1cbiAgICByZXR1cm4gYnVmZmVyICYmIGJ1ZmZlci52YWx1ZSgpO1xuICB9XG4gIHJlbmRlckh1bGwoY29udGV4dCkge1xuICAgIGNvbnN0IGJ1ZmZlciA9IGNvbnRleHQgPT0gbnVsbCA/IGNvbnRleHQgPSBuZXcgUGF0aCA6IHVuZGVmaW5lZDtcbiAgICBjb25zdCB7aHVsbCwgcG9pbnRzfSA9IHRoaXM7XG4gICAgY29uc3QgaCA9IGh1bGxbMF0gKiAyLCBuID0gaHVsbC5sZW5ndGg7XG4gICAgY29udGV4dC5tb3ZlVG8ocG9pbnRzW2hdLCBwb2ludHNbaCArIDFdKTtcbiAgICBmb3IgKGxldCBpID0gMTsgaSA8IG47ICsraSkge1xuICAgICAgY29uc3QgaCA9IDIgKiBodWxsW2ldO1xuICAgICAgY29udGV4dC5saW5lVG8ocG9pbnRzW2hdLCBwb2ludHNbaCArIDFdKTtcbiAgICB9XG4gICAgY29udGV4dC5jbG9zZVBhdGgoKTtcbiAgICByZXR1cm4gYnVmZmVyICYmIGJ1ZmZlci52YWx1ZSgpO1xuICB9XG4gIGh1bGxQb2x5Z29uKCkge1xuICAgIGNvbnN0IHBvbHlnb24gPSBuZXcgUG9seWdvbjtcbiAgICB0aGlzLnJlbmRlckh1bGwocG9seWdvbik7XG4gICAgcmV0dXJuIHBvbHlnb24udmFsdWUoKTtcbiAgfVxuICByZW5kZXJUcmlhbmdsZShpLCBjb250ZXh0KSB7XG4gICAgY29uc3QgYnVmZmVyID0gY29udGV4dCA9PSBudWxsID8gY29udGV4dCA9IG5ldyBQYXRoIDogdW5kZWZpbmVkO1xuICAgIGNvbnN0IHtwb2ludHMsIHRyaWFuZ2xlc30gPSB0aGlzO1xuICAgIGNvbnN0IHQwID0gdHJpYW5nbGVzW2kgKj0gM10gKiAyO1xuICAgIGNvbnN0IHQxID0gdHJpYW5nbGVzW2kgKyAxXSAqIDI7XG4gICAgY29uc3QgdDIgPSB0cmlhbmdsZXNbaSArIDJdICogMjtcbiAgICBjb250ZXh0Lm1vdmVUbyhwb2ludHNbdDBdLCBwb2ludHNbdDAgKyAxXSk7XG4gICAgY29udGV4dC5saW5lVG8ocG9pbnRzW3QxXSwgcG9pbnRzW3QxICsgMV0pO1xuICAgIGNvbnRleHQubGluZVRvKHBvaW50c1t0Ml0sIHBvaW50c1t0MiArIDFdKTtcbiAgICBjb250ZXh0LmNsb3NlUGF0aCgpO1xuICAgIHJldHVybiBidWZmZXIgJiYgYnVmZmVyLnZhbHVlKCk7XG4gIH1cbiAgKnRyaWFuZ2xlUG9seWdvbnMoKSB7XG4gICAgY29uc3Qge3RyaWFuZ2xlc30gPSB0aGlzO1xuICAgIGZvciAobGV0IGkgPSAwLCBuID0gdHJpYW5nbGVzLmxlbmd0aCAvIDM7IGkgPCBuOyArK2kpIHtcbiAgICAgIHlpZWxkIHRoaXMudHJpYW5nbGVQb2x5Z29uKGkpO1xuICAgIH1cbiAgfVxuICB0cmlhbmdsZVBvbHlnb24oaSkge1xuICAgIGNvbnN0IHBvbHlnb24gPSBuZXcgUG9seWdvbjtcbiAgICB0aGlzLnJlbmRlclRyaWFuZ2xlKGksIHBvbHlnb24pO1xuICAgIHJldHVybiBwb2x5Z29uLnZhbHVlKCk7XG4gIH1cbn1cblxuZnVuY3Rpb24gZmxhdEFycmF5KHBvaW50cywgZngsIGZ5LCB0aGF0KSB7XG4gIGNvbnN0IG4gPSBwb2ludHMubGVuZ3RoO1xuICBjb25zdCBhcnJheSA9IG5ldyBGbG9hdDY0QXJyYXkobiAqIDIpO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IG47ICsraSkge1xuICAgIGNvbnN0IHAgPSBwb2ludHNbaV07XG4gICAgYXJyYXlbaSAqIDJdID0gZnguY2FsbCh0aGF0LCBwLCBpLCBwb2ludHMpO1xuICAgIGFycmF5W2kgKiAyICsgMV0gPSBmeS5jYWxsKHRoYXQsIHAsIGksIHBvaW50cyk7XG4gIH1cbiAgcmV0dXJuIGFycmF5O1xufVxuXG5mdW5jdGlvbiogZmxhdEl0ZXJhYmxlKHBvaW50cywgZngsIGZ5LCB0aGF0KSB7XG4gIGxldCBpID0gMDtcbiAgZm9yIChjb25zdCBwIG9mIHBvaW50cykge1xuICAgIHlpZWxkIGZ4LmNhbGwodGhhdCwgcCwgaSwgcG9pbnRzKTtcbiAgICB5aWVsZCBmeS5jYWxsKHRoYXQsIHAsIGksIHBvaW50cyk7XG4gICAgKytpO1xuICB9XG59XG4iXSwibmFtZXMiOlsiRGVsYXVuYXRvciIsIlBhdGgiLCJQb2x5Z29uIiwiVm9yb25vaSIsInRhdSIsIk1hdGgiLCJQSSIsInBvdyIsInBvaW50WCIsInAiLCJwb2ludFkiLCJjb2xsaW5lYXIiLCJkIiwidHJpYW5nbGVzIiwiY29vcmRzIiwiaSIsImxlbmd0aCIsImEiLCJiIiwiYyIsImNyb3NzIiwiaml0dGVyIiwieCIsInkiLCJyIiwic2luIiwiY29zIiwiRGVsYXVuYXkiLCJmcm9tIiwicG9pbnRzIiwiZngiLCJmeSIsInRoYXQiLCJmbGF0QXJyYXkiLCJGbG9hdDY0QXJyYXkiLCJmbGF0SXRlcmFibGUiLCJjb25zdHJ1Y3RvciIsIl9kZWxhdW5hdG9yIiwiaW5lZGdlcyIsIkludDMyQXJyYXkiLCJfaHVsbEluZGV4IiwiX2luaXQiLCJ1cGRhdGUiLCJodWxsIiwiXyIsInNvcnQiLCJqIiwiZSIsImYiLCJib3VuZHMiLCJoeXBvdCIsIm4iLCJoYWxmZWRnZXMiLCJmaWxsIiwiaHVsbEluZGV4Iiwidm9yb25vaSIsIm5laWdoYm9ycyIsImwiLCJpbmRleE9mIiwiZTAiLCJwMCIsImZpbmQiLCJpMCIsIl9zdGVwIiwiZGMiLCJ0IiwiZHQiLCJyZW5kZXIiLCJjb250ZXh0IiwiYnVmZmVyIiwidW5kZWZpbmVkIiwidGkiLCJ0aiIsIm1vdmVUbyIsImxpbmVUbyIsInJlbmRlckh1bGwiLCJ2YWx1ZSIsInJlbmRlclBvaW50cyIsImFyYyIsImgiLCJjbG9zZVBhdGgiLCJodWxsUG9seWdvbiIsInBvbHlnb24iLCJyZW5kZXJUcmlhbmdsZSIsInQwIiwidDEiLCJ0MiIsInRyaWFuZ2xlUG9seWdvbnMiLCJ0cmlhbmdsZVBvbHlnb24iLCJhcnJheSIsImNhbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-delaunay/src/delaunay.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-delaunay/src/index.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-delaunay/src/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Delaunay: () => (/* reexport safe */ _delaunay_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Voronoi: () => (/* reexport safe */ _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _delaunay_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delaunay.js */ \"(ssr)/../../node_modules/d3-delaunay/src/delaunay.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/../../node_modules/d3-delaunay/src/voronoi.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWRlbGF1bmF5L3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtEO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL2luZGV4LmpzPzU0ZGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtkZWZhdWx0IGFzIERlbGF1bmF5fSBmcm9tIFwiLi9kZWxhdW5heS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIFZvcm9ub2l9IGZyb20gXCIuL3Zvcm9ub2kuanNcIjtcbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiRGVsYXVuYXkiLCJWb3Jvbm9pIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-delaunay/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-delaunay/src/path.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-delaunay/src/path.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Path)\n/* harmony export */ });\nconst epsilon = 1e-6;\nclass Path {\n    constructor(){\n        this._x0 = this._y0 = this._x1 = this._y1 = null; // end of current subpath\n        this._ = \"\";\n    }\n    moveTo(x, y) {\n        this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n    }\n    closePath() {\n        if (this._x1 !== null) {\n            this._x1 = this._x0, this._y1 = this._y0;\n            this._ += \"Z\";\n        }\n    }\n    lineTo(x, y) {\n        this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n    }\n    arc(x, y, r) {\n        x = +x, y = +y, r = +r;\n        const x0 = x + r;\n        const y0 = y;\n        if (r < 0) throw new Error(\"negative radius\");\n        if (this._x1 === null) this._ += `M${x0},${y0}`;\n        else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n        if (!r) return;\n        this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n    }\n    rect(x, y, w, h) {\n        this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n    }\n    value() {\n        return this._ || null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-delaunay/src/path.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-delaunay/src/polygon.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-delaunay/src/polygon.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Polygon)\n/* harmony export */ });\nclass Polygon {\n    constructor(){\n        this._ = [];\n    }\n    moveTo(x, y) {\n        this._.push([\n            x,\n            y\n        ]);\n    }\n    closePath() {\n        this._.push(this._[0].slice());\n    }\n    lineTo(x, y) {\n        this._.push([\n            x,\n            y\n        ]);\n    }\n    value() {\n        return this._.length ? this._ : null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWRlbGF1bmF5L3NyYy9wb2x5Z29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxNQUFNQTtJQUNuQkMsYUFBYztRQUNaLElBQUksQ0FBQ0MsQ0FBQyxHQUFHLEVBQUU7SUFDYjtJQUNBQyxPQUFPQyxDQUFDLEVBQUVDLENBQUMsRUFBRTtRQUNYLElBQUksQ0FBQ0gsQ0FBQyxDQUFDSSxJQUFJLENBQUM7WUFBQ0Y7WUFBR0M7U0FBRTtJQUNwQjtJQUNBRSxZQUFZO1FBQ1YsSUFBSSxDQUFDTCxDQUFDLENBQUNJLElBQUksQ0FBQyxJQUFJLENBQUNKLENBQUMsQ0FBQyxFQUFFLENBQUNNLEtBQUs7SUFDN0I7SUFDQUMsT0FBT0wsQ0FBQyxFQUFFQyxDQUFDLEVBQUU7UUFDWCxJQUFJLENBQUNILENBQUMsQ0FBQ0ksSUFBSSxDQUFDO1lBQUNGO1lBQUdDO1NBQUU7SUFDcEI7SUFDQUssUUFBUTtRQUNOLE9BQU8sSUFBSSxDQUFDUixDQUFDLENBQUNTLE1BQU0sR0FBRyxJQUFJLENBQUNULENBQUMsR0FBRztJQUNsQztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLWRlbGF1bmF5L3NyYy9wb2x5Z29uLmpzP2FkNTgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgY2xhc3MgUG9seWdvbiB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuXyA9IFtdO1xuICB9XG4gIG1vdmVUbyh4LCB5KSB7XG4gICAgdGhpcy5fLnB1c2goW3gsIHldKTtcbiAgfVxuICBjbG9zZVBhdGgoKSB7XG4gICAgdGhpcy5fLnB1c2godGhpcy5fWzBdLnNsaWNlKCkpO1xuICB9XG4gIGxpbmVUbyh4LCB5KSB7XG4gICAgdGhpcy5fLnB1c2goW3gsIHldKTtcbiAgfVxuICB2YWx1ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fLmxlbmd0aCA/IHRoaXMuXyA6IG51bGw7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJQb2x5Z29uIiwiY29uc3RydWN0b3IiLCJfIiwibW92ZVRvIiwieCIsInkiLCJwdXNoIiwiY2xvc2VQYXRoIiwic2xpY2UiLCJsaW5lVG8iLCJ2YWx1ZSIsImxlbmd0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-delaunay/src/polygon.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-delaunay/src/voronoi.js":
/*!*****************************************************!*\
  !*** ../../node_modules/d3-delaunay/src/voronoi.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Voronoi)\n/* harmony export */ });\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./path.js */ \"(ssr)/../../node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/../../node_modules/d3-delaunay/src/polygon.js\");\n\n\nclass Voronoi {\n    constructor(delaunay, [xmin, ymin, xmax, ymax] = [\n        0,\n        0,\n        960,\n        500\n    ]){\n        if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n        this.delaunay = delaunay;\n        this._circumcenters = new Float64Array(delaunay.points.length * 2);\n        this.vectors = new Float64Array(delaunay.points.length * 2);\n        this.xmax = xmax, this.xmin = xmin;\n        this.ymax = ymax, this.ymin = ymin;\n        this._init();\n    }\n    update() {\n        this.delaunay.update();\n        this._init();\n        return this;\n    }\n    _init() {\n        const { delaunay: { points, hull, triangles }, vectors } = this;\n        let bx, by; // lazily computed barycenter of the hull\n        // Compute circumcenters.\n        const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n        for(let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2){\n            const t1 = triangles[i] * 2;\n            const t2 = triangles[i + 1] * 2;\n            const t3 = triangles[i + 2] * 2;\n            const x1 = points[t1];\n            const y1 = points[t1 + 1];\n            const x2 = points[t2];\n            const y2 = points[t2 + 1];\n            const x3 = points[t3];\n            const y3 = points[t3 + 1];\n            const dx = x2 - x1;\n            const dy = y2 - y1;\n            const ex = x3 - x1;\n            const ey = y3 - y1;\n            const ab = (dx * ey - dy * ex) * 2;\n            if (Math.abs(ab) < 1e-9) {\n                // For a degenerate triangle, the circumcenter is at the infinity, in a\n                // direction orthogonal to the halfedge and away from the “center” of\n                // the diagram <bx, by>, defined as the hull’s barycenter.\n                if (bx === undefined) {\n                    bx = by = 0;\n                    for (const i of hull)bx += points[i * 2], by += points[i * 2 + 1];\n                    bx /= hull.length, by /= hull.length;\n                }\n                const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n                x = (x1 + x3) / 2 - a * ey;\n                y = (y1 + y3) / 2 + a * ex;\n            } else {\n                const d = 1 / ab;\n                const bl = dx * dx + dy * dy;\n                const cl = ex * ex + ey * ey;\n                x = x1 + (ey * bl - dy * cl) * d;\n                y = y1 + (dx * cl - ex * bl) * d;\n            }\n            circumcenters[j] = x;\n            circumcenters[j + 1] = y;\n        }\n        // Compute exterior cell rays.\n        let h = hull[hull.length - 1];\n        let p0, p1 = h * 4;\n        let x0, x1 = points[2 * h];\n        let y0, y1 = points[2 * h + 1];\n        vectors.fill(0);\n        for(let i = 0; i < hull.length; ++i){\n            h = hull[i];\n            p0 = p1, x0 = x1, y0 = y1;\n            p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n            vectors[p0 + 2] = vectors[p1] = y0 - y1;\n            vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n        }\n    }\n    render(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n        const { delaunay: { halfedges, inedges, hull }, circumcenters, vectors } = this;\n        if (hull.length <= 1) return null;\n        for(let i = 0, n = halfedges.length; i < n; ++i){\n            const j = halfedges[i];\n            if (j < i) continue;\n            const ti = Math.floor(i / 3) * 2;\n            const tj = Math.floor(j / 3) * 2;\n            const xi = circumcenters[ti];\n            const yi = circumcenters[ti + 1];\n            const xj = circumcenters[tj];\n            const yj = circumcenters[tj + 1];\n            this._renderSegment(xi, yi, xj, yj, context);\n        }\n        let h0, h1 = hull[hull.length - 1];\n        for(let i = 0; i < hull.length; ++i){\n            h0 = h1, h1 = hull[i];\n            const t = Math.floor(inedges[h1] / 3) * 2;\n            const x = circumcenters[t];\n            const y = circumcenters[t + 1];\n            const v = h0 * 4;\n            const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n            if (p) this._renderSegment(x, y, p[0], p[1], context);\n        }\n        return buffer && buffer.value();\n    }\n    renderBounds(context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n        context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n        return buffer && buffer.value();\n    }\n    renderCell(i, context) {\n        const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n        const points = this._clip(i);\n        if (points === null || !points.length) return;\n        context.moveTo(points[0], points[1]);\n        let n = points.length;\n        while(points[0] === points[n - 2] && points[1] === points[n - 1] && n > 1)n -= 2;\n        for(let i = 2; i < n; i += 2){\n            if (points[i] !== points[i - 2] || points[i + 1] !== points[i - 1]) context.lineTo(points[i], points[i + 1]);\n        }\n        context.closePath();\n        return buffer && buffer.value();\n    }\n    *cellPolygons() {\n        const { delaunay: { points } } = this;\n        for(let i = 0, n = points.length / 2; i < n; ++i){\n            const cell = this.cellPolygon(i);\n            if (cell) cell.index = i, yield cell;\n        }\n    }\n    cellPolygon(i) {\n        const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        this.renderCell(i, polygon);\n        return polygon.value();\n    }\n    _renderSegment(x0, y0, x1, y1, context) {\n        let S;\n        const c0 = this._regioncode(x0, y0);\n        const c1 = this._regioncode(x1, y1);\n        if (c0 === 0 && c1 === 0) {\n            context.moveTo(x0, y0);\n            context.lineTo(x1, y1);\n        } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n            context.moveTo(S[0], S[1]);\n            context.lineTo(S[2], S[3]);\n        }\n    }\n    contains(i, x, y) {\n        if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n        return this.delaunay._step(i, x, y) === i;\n    }\n    *neighbors(i) {\n        const ci = this._clip(i);\n        if (ci) for (const j of this.delaunay.neighbors(i)){\n            const cj = this._clip(j);\n            // find the common edge\n            if (cj) loop: for(let ai = 0, li = ci.length; ai < li; ai += 2){\n                for(let aj = 0, lj = cj.length; aj < lj; aj += 2){\n                    if (ci[ai] === cj[aj] && ci[ai + 1] === cj[aj + 1] && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj] && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n                        yield j;\n                        break loop;\n                    }\n                }\n            }\n        }\n    }\n    _cell(i) {\n        const { circumcenters, delaunay: { inedges, halfedges, triangles } } = this;\n        const e0 = inedges[i];\n        if (e0 === -1) return null; // coincident point\n        const points = [];\n        let e = e0;\n        do {\n            const t = Math.floor(e / 3);\n            points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n            e = e % 3 === 2 ? e - 2 : e + 1;\n            if (triangles[e] !== i) break; // bad triangulation\n            e = halfedges[e];\n        }while (e !== e0 && e !== -1);\n        return points;\n    }\n    _clip(i) {\n        // degenerate case (1 valid point: return the box)\n        if (i === 0 && this.delaunay.hull.length === 1) {\n            return [\n                this.xmax,\n                this.ymin,\n                this.xmax,\n                this.ymax,\n                this.xmin,\n                this.ymax,\n                this.xmin,\n                this.ymin\n            ];\n        }\n        const points = this._cell(i);\n        if (points === null) return null;\n        const { vectors: V } = this;\n        const v = i * 4;\n        return this._simplify(V[v] || V[v + 1] ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3]) : this._clipFinite(i, points));\n    }\n    _clipFinite(i, points) {\n        const n = points.length;\n        let P = null;\n        let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n        let c0, c1 = this._regioncode(x1, y1);\n        let e0, e1 = 0;\n        for(let j = 0; j < n; j += 2){\n            x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n            c0 = c1, c1 = this._regioncode(x1, y1);\n            if (c0 === 0 && c1 === 0) {\n                e0 = e1, e1 = 0;\n                if (P) P.push(x1, y1);\n                else P = [\n                    x1,\n                    y1\n                ];\n            } else {\n                let S, sx0, sy0, sx1, sy1;\n                if (c0 === 0) {\n                    if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n                    [sx0, sy0, sx1, sy1] = S;\n                } else {\n                    if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n                    [sx1, sy1, sx0, sy0] = S;\n                    e0 = e1, e1 = this._edgecode(sx0, sy0);\n                    if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n                    if (P) P.push(sx0, sy0);\n                    else P = [\n                        sx0,\n                        sy0\n                    ];\n                }\n                e0 = e1, e1 = this._edgecode(sx1, sy1);\n                if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n                if (P) P.push(sx1, sy1);\n                else P = [\n                    sx1,\n                    sy1\n                ];\n            }\n        }\n        if (P) {\n            e0 = e1, e1 = this._edgecode(P[0], P[1]);\n            if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n            return [\n                this.xmax,\n                this.ymin,\n                this.xmax,\n                this.ymax,\n                this.xmin,\n                this.ymax,\n                this.xmin,\n                this.ymin\n            ];\n        }\n        return P;\n    }\n    _clipSegment(x0, y0, x1, y1, c0, c1) {\n        // for more robustness, always consider the segment in the same order\n        const flip = c0 < c1;\n        if (flip) [x0, y0, x1, y1, c0, c1] = [\n            x1,\n            y1,\n            x0,\n            y0,\n            c1,\n            c0\n        ];\n        while(true){\n            if (c0 === 0 && c1 === 0) return flip ? [\n                x1,\n                y1,\n                x0,\n                y0\n            ] : [\n                x0,\n                y0,\n                x1,\n                y1\n            ];\n            if (c0 & c1) return null;\n            let x, y, c = c0 || c1;\n            if (c & 8) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n            else if (c & 4) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n            else if (c & 2) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n            else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n            if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n            else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n        }\n    }\n    _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n        let P = Array.from(points), p;\n        if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n        if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n        if (P = this._clipFinite(i, P)) {\n            for(let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2){\n                c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n                if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n            }\n        } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n            P = [\n                this.xmin,\n                this.ymin,\n                this.xmax,\n                this.ymin,\n                this.xmax,\n                this.ymax,\n                this.xmin,\n                this.ymax\n            ];\n        }\n        return P;\n    }\n    _edge(i, e0, e1, P, j) {\n        while(e0 !== e1){\n            let x, y;\n            switch(e0){\n                case 5:\n                    e0 = 4;\n                    continue; // top-left\n                case 4:\n                    e0 = 6, x = this.xmax, y = this.ymin;\n                    break; // top\n                case 6:\n                    e0 = 2;\n                    continue; // top-right\n                case 2:\n                    e0 = 10, x = this.xmax, y = this.ymax;\n                    break; // right\n                case 10:\n                    e0 = 8;\n                    continue; // bottom-right\n                case 8:\n                    e0 = 9, x = this.xmin, y = this.ymax;\n                    break; // bottom\n                case 9:\n                    e0 = 1;\n                    continue; // bottom-left\n                case 1:\n                    e0 = 5, x = this.xmin, y = this.ymin;\n                    break; // left\n            }\n            // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n            // undefined, the conditional statement will be executed.\n            if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n                P.splice(j, 0, x, y), j += 2;\n            }\n        }\n        return j;\n    }\n    _project(x0, y0, vx, vy) {\n        let t = Infinity, c, x, y;\n        if (vy < 0) {\n            if (y0 <= this.ymin) return null;\n            if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n        } else if (vy > 0) {\n            if (y0 >= this.ymax) return null;\n            if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n        }\n        if (vx > 0) {\n            if (x0 >= this.xmax) return null;\n            if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n        } else if (vx < 0) {\n            if (x0 <= this.xmin) return null;\n            if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n        }\n        return [\n            x,\n            y\n        ];\n    }\n    _edgecode(x, y) {\n        return (x === this.xmin ? 1 : x === this.xmax ? 2 : 0) | (y === this.ymin ? 4 : y === this.ymax ? 8 : 0);\n    }\n    _regioncode(x, y) {\n        return (x < this.xmin ? 1 : x > this.xmax ? 2 : 0) | (y < this.ymin ? 4 : y > this.ymax ? 8 : 0);\n    }\n    _simplify(P) {\n        if (P && P.length > 4) {\n            for(let i = 0; i < P.length; i += 2){\n                const j = (i + 2) % P.length, k = (i + 4) % P.length;\n                if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n                    P.splice(j, 2), i -= 2;\n                }\n            }\n            if (!P.length) P = null;\n        }\n        return P;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-delaunay/src/voronoi.js\n");

/***/ })

};
;