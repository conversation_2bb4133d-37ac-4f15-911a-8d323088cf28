"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-quadtree";
exports.ids = ["vendor-chunks/d3-quadtree"];
exports.modules = {

/***/ "(ssr)/../../node_modules/d3-quadtree/src/add.js":
/*!*************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/add.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addAll: () => (/* binding */ addAll),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(d) {\n    const x = +this._x.call(null, d), y = +this._y.call(null, d);\n    return add(this.cover(x, y), x, y, d);\n}\nfunction add(tree, x, y, d) {\n    if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n    var parent, node = tree._root, leaf = {\n        data: d\n    }, x0 = tree._x0, y0 = tree._y0, x1 = tree._x1, y1 = tree._y1, xm, ym, xp, yp, right, bottom, i, j;\n    // If the tree is empty, initialize the root as a leaf.\n    if (!node) return tree._root = leaf, tree;\n    // Find the existing leaf for the new point, or add it.\n    while(node.length){\n        if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;\n        else x1 = xm;\n        if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;\n        else y1 = ym;\n        if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n    }\n    // Is the new point is exactly coincident with the existing point?\n    xp = +tree._x.call(null, node.data);\n    yp = +tree._y.call(null, node.data);\n    if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n    // Otherwise, split the leaf node until the old and new point are separated.\n    do {\n        parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n        if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;\n        else x1 = xm;\n        if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;\n        else y1 = ym;\n    }while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | xp >= xm));\n    return parent[j] = node, parent[i] = leaf, tree;\n}\nfunction addAll(data) {\n    var d, i, n = data.length, x, y, xz = new Array(n), yz = new Array(n), x0 = Infinity, y0 = Infinity, x1 = -Infinity, y1 = -Infinity;\n    // Compute the points and their extent.\n    for(i = 0; i < n; ++i){\n        if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n        xz[i] = x;\n        yz[i] = y;\n        if (x < x0) x0 = x;\n        if (x > x1) x1 = x;\n        if (y < y0) y0 = y;\n        if (y > y1) y1 = y;\n    }\n    // If there were no (valid) points, abort.\n    if (x0 > x1 || y0 > y1) return this;\n    // Expand the tree to cover the new points.\n    this.cover(x0, y0).cover(x1, y1);\n    // Add the new points.\n    for(i = 0; i < n; ++i){\n        add(this, xz[i], yz[i], data[i]);\n    }\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/add.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/cover.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/cover.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, y) {\n    if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n    var x0 = this._x0, y0 = this._y0, x1 = this._x1, y1 = this._y1;\n    // If the quadtree has no extent, initialize them.\n    // Integer extent are necessary so that if we later double the extent,\n    // the existing quadrant boundaries don’t change due to floating point error!\n    if (isNaN(x0)) {\n        x1 = (x0 = Math.floor(x)) + 1;\n        y1 = (y0 = Math.floor(y)) + 1;\n    } else {\n        var z = x1 - x0 || 1, node = this._root, parent, i;\n        while(x0 > x || x >= x1 || y0 > y || y >= y1){\n            i = (y < y0) << 1 | x < x0;\n            parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n            switch(i){\n                case 0:\n                    x1 = x0 + z, y1 = y0 + z;\n                    break;\n                case 1:\n                    x0 = x1 - z, y1 = y0 + z;\n                    break;\n                case 2:\n                    x1 = x0 + z, y0 = y1 - z;\n                    break;\n                case 3:\n                    x0 = x1 - z, y0 = y1 - z;\n                    break;\n            }\n        }\n        if (this._root && this._root.length) this._root = node;\n    }\n    this._x0 = x0;\n    this._y0 = y0;\n    this._x1 = x1;\n    this._y1 = y1;\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/cover.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/data.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/data.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var data = [];\n    this.visit(function(node) {\n        if (!node.length) do data.push(node.data);\n        while (node = node.next);\n    });\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9kYXRhLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxzQ0FBVztJQUN4QixJQUFJQSxPQUFPLEVBQUU7SUFDYixJQUFJLENBQUNDLEtBQUssQ0FBQyxTQUFTQyxJQUFJO1FBQ3RCLElBQUksQ0FBQ0EsS0FBS0MsTUFBTSxFQUFFLEdBQUdILEtBQUtJLElBQUksQ0FBQ0YsS0FBS0YsSUFBSTtlQUFVRSxPQUFPQSxLQUFLRyxJQUFJLEVBQUM7SUFDckU7SUFDQSxPQUFPTDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9kYXRhLmpzPzhkYzQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHZhciBkYXRhID0gW107XG4gIHRoaXMudmlzaXQoZnVuY3Rpb24obm9kZSkge1xuICAgIGlmICghbm9kZS5sZW5ndGgpIGRvIGRhdGEucHVzaChub2RlLmRhdGEpOyB3aGlsZSAobm9kZSA9IG5vZGUubmV4dClcbiAgfSk7XG4gIHJldHVybiBkYXRhO1xufVxuIl0sIm5hbWVzIjpbImRhdGEiLCJ2aXNpdCIsIm5vZGUiLCJsZW5ndGgiLCJwdXNoIiwibmV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/data.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/extent.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/extent.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(_) {\n    return arguments.length ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1]) : isNaN(this._x0) ? undefined : [\n        [\n            this._x0,\n            this._y0\n        ],\n        [\n            this._x1,\n            this._y1\n        ]\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9leHRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxDQUFDO0lBQ3ZCLE9BQU9DLFVBQVVDLE1BQU0sR0FDakIsSUFBSSxDQUFDQyxLQUFLLENBQUMsQ0FBQ0gsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQ0EsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUVHLEtBQUssQ0FBQyxDQUFDSCxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDQSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFDdkRJLE1BQU0sSUFBSSxDQUFDQyxHQUFHLElBQUlDLFlBQVk7UUFBQztZQUFDLElBQUksQ0FBQ0QsR0FBRztZQUFFLElBQUksQ0FBQ0UsR0FBRztTQUFDO1FBQUU7WUFBQyxJQUFJLENBQUNDLEdBQUc7WUFBRSxJQUFJLENBQUNDLEdBQUc7U0FBQztLQUFDO0FBQ2xGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9leHRlbnQuanM/NWIxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihfKSB7XG4gIHJldHVybiBhcmd1bWVudHMubGVuZ3RoXG4gICAgICA/IHRoaXMuY292ZXIoK19bMF1bMF0sICtfWzBdWzFdKS5jb3ZlcigrX1sxXVswXSwgK19bMV1bMV0pXG4gICAgICA6IGlzTmFOKHRoaXMuX3gwKSA/IHVuZGVmaW5lZCA6IFtbdGhpcy5feDAsIHRoaXMuX3kwXSwgW3RoaXMuX3gxLCB0aGlzLl95MV1dO1xufVxuIl0sIm5hbWVzIjpbIl8iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJjb3ZlciIsImlzTmFOIiwiX3gwIiwidW5kZWZpbmVkIiwiX3kwIiwiX3gxIiwiX3kxIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/extent.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/find.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/find.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _quad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quad.js */ \"(ssr)/../../node_modules/d3-quadtree/src/quad.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, y, radius) {\n    var data, x0 = this._x0, y0 = this._y0, x1, y1, x2, y2, x3 = this._x1, y3 = this._y1, quads = [], node = this._root, q, i;\n    if (node) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node, x0, y0, x3, y3));\n    if (radius == null) radius = Infinity;\n    else {\n        x0 = x - radius, y0 = y - radius;\n        x3 = x + radius, y3 = y + radius;\n        radius *= radius;\n    }\n    while(q = quads.pop()){\n        // Stop searching if this quadrant can’t contain a closer node.\n        if (!(node = q.node) || (x1 = q.x0) > x3 || (y1 = q.y0) > y3 || (x2 = q.x1) < x0 || (y2 = q.y1) < y0) continue;\n        // Bisect the current quadrant.\n        if (node.length) {\n            var xm = (x1 + x2) / 2, ym = (y1 + y2) / 2;\n            quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[3], xm, ym, x2, y2), new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[2], x1, ym, xm, y2), new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[1], xm, y1, x2, ym), new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node[0], x1, y1, xm, ym));\n            // Visit the closest quadrant first.\n            if (i = (y >= ym) << 1 | x >= xm) {\n                q = quads[quads.length - 1];\n                quads[quads.length - 1] = quads[quads.length - 1 - i];\n                quads[quads.length - 1 - i] = q;\n            }\n        } else {\n            var dx = x - +this._x.call(null, node.data), dy = y - +this._y.call(null, node.data), d2 = dx * dx + dy * dy;\n            if (d2 < radius) {\n                var d = Math.sqrt(radius = d2);\n                x0 = x - d, y0 = y - d;\n                x3 = x + d, y3 = y + d;\n                data = node.data;\n            }\n        }\n    }\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/find.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/index.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quadtree: () => (/* reexport safe */ _quadtree_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _quadtree_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quadtree.js */ \"(ssr)/../../node_modules/d3-quadtree/src/quadtree.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1xdWFkdHJlZS9zcmMvaW5kZXguanM/NmRmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgcXVhZHRyZWV9IGZyb20gXCIuL3F1YWR0cmVlLmpzXCI7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsInF1YWR0cmVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/quad.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/quad.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(node, x0, y0, x1, y1) {\n    this.node = node;\n    this.x0 = x0;\n    this.y0 = y0;\n    this.x1 = x1;\n    this.y1 = y1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9xdWFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsSUFBSSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFO0lBQzFDLElBQUksQ0FBQ0osSUFBSSxHQUFHQTtJQUNaLElBQUksQ0FBQ0MsRUFBRSxHQUFHQTtJQUNWLElBQUksQ0FBQ0MsRUFBRSxHQUFHQTtJQUNWLElBQUksQ0FBQ0MsRUFBRSxHQUFHQTtJQUNWLElBQUksQ0FBQ0MsRUFBRSxHQUFHQTtBQUNaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9xdWFkLmpzPzc4ODAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24obm9kZSwgeDAsIHkwLCB4MSwgeTEpIHtcbiAgdGhpcy5ub2RlID0gbm9kZTtcbiAgdGhpcy54MCA9IHgwO1xuICB0aGlzLnkwID0geTA7XG4gIHRoaXMueDEgPSB4MTtcbiAgdGhpcy55MSA9IHkxO1xufVxuIl0sIm5hbWVzIjpbIm5vZGUiLCJ4MCIsInkwIiwieDEiLCJ5MSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/quad.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/quadtree.js":
/*!******************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/quadtree.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quadtree)\n/* harmony export */ });\n/* harmony import */ var _add_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./add.js */ \"(ssr)/../../node_modules/d3-quadtree/src/add.js\");\n/* harmony import */ var _cover_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cover.js */ \"(ssr)/../../node_modules/d3-quadtree/src/cover.js\");\n/* harmony import */ var _data_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./data.js */ \"(ssr)/../../node_modules/d3-quadtree/src/data.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/../../node_modules/d3-quadtree/src/extent.js\");\n/* harmony import */ var _find_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./find.js */ \"(ssr)/../../node_modules/d3-quadtree/src/find.js\");\n/* harmony import */ var _remove_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./remove.js */ \"(ssr)/../../node_modules/d3-quadtree/src/remove.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./root.js */ \"(ssr)/../../node_modules/d3-quadtree/src/root.js\");\n/* harmony import */ var _size_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./size.js */ \"(ssr)/../../node_modules/d3-quadtree/src/size.js\");\n/* harmony import */ var _visit_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./visit.js */ \"(ssr)/../../node_modules/d3-quadtree/src/visit.js\");\n/* harmony import */ var _visitAfter_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./visitAfter.js */ \"(ssr)/../../node_modules/d3-quadtree/src/visitAfter.js\");\n/* harmony import */ var _x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./x.js */ \"(ssr)/../../node_modules/d3-quadtree/src/x.js\");\n/* harmony import */ var _y_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./y.js */ \"(ssr)/../../node_modules/d3-quadtree/src/y.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction quadtree(nodes, x, y) {\n    var tree = new Quadtree(x == null ? _x_js__WEBPACK_IMPORTED_MODULE_0__.defaultX : x, y == null ? _y_js__WEBPACK_IMPORTED_MODULE_1__.defaultY : y, NaN, NaN, NaN, NaN);\n    return nodes == null ? tree : tree.addAll(nodes);\n}\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n    this._x = x;\n    this._y = y;\n    this._x0 = x0;\n    this._y0 = y0;\n    this._x1 = x1;\n    this._y1 = y1;\n    this._root = undefined;\n}\nfunction leaf_copy(leaf) {\n    var copy = {\n        data: leaf.data\n    }, next = copy;\n    while(leaf = leaf.next)next = next.next = {\n        data: leaf.data\n    };\n    return copy;\n}\nvar treeProto = quadtree.prototype = Quadtree.prototype;\ntreeProto.copy = function() {\n    var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1), node = this._root, nodes, child;\n    if (!node) return copy;\n    if (!node.length) return copy._root = leaf_copy(node), copy;\n    nodes = [\n        {\n            source: node,\n            target: copy._root = new Array(4)\n        }\n    ];\n    while(node = nodes.pop()){\n        for(var i = 0; i < 4; ++i){\n            if (child = node.source[i]) {\n                if (child.length) nodes.push({\n                    source: child,\n                    target: node.target[i] = new Array(4)\n                });\n                else node.target[i] = leaf_copy(child);\n            }\n        }\n    }\n    return copy;\n};\ntreeProto.add = _add_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\ntreeProto.addAll = _add_js__WEBPACK_IMPORTED_MODULE_2__.addAll;\ntreeProto.cover = _cover_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\ntreeProto.data = _data_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\ntreeProto.extent = _extent_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\ntreeProto.find = _find_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\ntreeProto.remove = _remove_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\ntreeProto.removeAll = _remove_js__WEBPACK_IMPORTED_MODULE_7__.removeAll;\ntreeProto.root = _root_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\ntreeProto.size = _size_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\ntreeProto.visit = _visit_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\ntreeProto.visitAfter = _visitAfter_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\ntreeProto.x = _x_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\ntreeProto.y = _y_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/quadtree.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/remove.js":
/*!****************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/remove.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   removeAll: () => (/* binding */ removeAll)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(d) {\n    if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n    var parent, node = this._root, retainer, previous, next, x0 = this._x0, y0 = this._y0, x1 = this._x1, y1 = this._y1, x, y, xm, ym, right, bottom, i, j;\n    // If the tree is empty, initialize the root as a leaf.\n    if (!node) return this;\n    // Find the leaf node for the point.\n    // While descending, also retain the deepest parent with a non-removed sibling.\n    if (node.length) while(true){\n        if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;\n        else x1 = xm;\n        if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;\n        else y1 = ym;\n        if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n        if (!node.length) break;\n        if (parent[i + 1 & 3] || parent[i + 2 & 3] || parent[i + 3 & 3]) retainer = parent, j = i;\n    }\n    // Find the point to remove.\n    while(node.data !== d)if (!(previous = node, node = node.next)) return this;\n    if (next = node.next) delete node.next;\n    // If there are multiple coincident points, remove just the point.\n    if (previous) return next ? previous.next = next : delete previous.next, this;\n    // If this is the root point, remove it.\n    if (!parent) return this._root = next, this;\n    // Remove this leaf.\n    next ? parent[i] = next : delete parent[i];\n    // If the parent now contains exactly one leaf, collapse superfluous parents.\n    if ((node = parent[0] || parent[1] || parent[2] || parent[3]) && node === (parent[3] || parent[2] || parent[1] || parent[0]) && !node.length) {\n        if (retainer) retainer[j] = node;\n        else this._root = node;\n    }\n    return this;\n}\nfunction removeAll(data) {\n    for(var i = 0, n = data.length; i < n; ++i)this.remove(data[i]);\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/remove.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/root.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/root.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return this._root;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxzQ0FBVztJQUN4QixPQUFPLElBQUksQ0FBQ0EsS0FBSztBQUNuQiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1xdWFkdHJlZS9zcmMvcm9vdC5qcz9mNThhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gdGhpcy5fcm9vdDtcbn1cbiJdLCJuYW1lcyI6WyJfcm9vdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/root.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/size.js":
/*!**************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/size.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var size = 0;\n    this.visit(function(node) {\n        if (!node.length) do ++size;\n        while (node = node.next);\n    });\n    return size;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy9zaXplLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxzQ0FBVztJQUN4QixJQUFJQSxPQUFPO0lBQ1gsSUFBSSxDQUFDQyxLQUFLLENBQUMsU0FBU0MsSUFBSTtRQUN0QixJQUFJLENBQUNBLEtBQUtDLE1BQU0sRUFBRSxHQUFHLEVBQUVIO2VBQWFFLE9BQU9BLEtBQUtFLElBQUksRUFBQztJQUN2RDtJQUNBLE9BQU9KO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3NpemUuanM/NGE1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgdmFyIHNpemUgPSAwO1xuICB0aGlzLnZpc2l0KGZ1bmN0aW9uKG5vZGUpIHtcbiAgICBpZiAoIW5vZGUubGVuZ3RoKSBkbyArK3NpemU7IHdoaWxlIChub2RlID0gbm9kZS5uZXh0KVxuICB9KTtcbiAgcmV0dXJuIHNpemU7XG59XG4iXSwibmFtZXMiOlsic2l6ZSIsInZpc2l0Iiwibm9kZSIsImxlbmd0aCIsIm5leHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/size.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/visit.js":
/*!***************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/visit.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _quad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quad.js */ \"(ssr)/../../node_modules/d3-quadtree/src/quad.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(callback) {\n    var quads = [], q, node = this._root, child, x0, y0, x1, y1;\n    if (node) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](node, this._x0, this._y0, this._x1, this._y1));\n    while(q = quads.pop()){\n        if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n            var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n            if (child = node[3]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, ym, x1, y1));\n            if (child = node[2]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, ym, xm, y1));\n            if (child = node[1]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, y0, x1, ym));\n            if (child = node[0]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, y0, xm, ym));\n        }\n    }\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/visit.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/visitAfter.js":
/*!********************************************************!*\
  !*** ../../node_modules/d3-quadtree/src/visitAfter.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _quad_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quad.js */ \"(ssr)/../../node_modules/d3-quadtree/src/quad.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(callback) {\n    var quads = [], next = [], q;\n    if (this._root) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](this._root, this._x0, this._y0, this._x1, this._y1));\n    while(q = quads.pop()){\n        var node = q.node;\n        if (node.length) {\n            var child, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n            if (child = node[0]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, y0, xm, ym));\n            if (child = node[1]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, y0, x1, ym));\n            if (child = node[2]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, x0, ym, xm, y1));\n            if (child = node[3]) quads.push(new _quad_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](child, xm, ym, x1, y1));\n        }\n        next.push(q);\n    }\n    while(q = next.pop()){\n        callback(q.node, q.x0, q.y0, q.x1, q.y1);\n    }\n    return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy92aXNpdEFmdGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZCO0FBRTdCLDZCQUFlLG9DQUFTQyxRQUFRO0lBQzlCLElBQUlDLFFBQVEsRUFBRSxFQUFFQyxPQUFPLEVBQUUsRUFBRUM7SUFDM0IsSUFBSSxJQUFJLENBQUNDLEtBQUssRUFBRUgsTUFBTUksSUFBSSxDQUFDLElBQUlOLGdEQUFJQSxDQUFDLElBQUksQ0FBQ0ssS0FBSyxFQUFFLElBQUksQ0FBQ0UsR0FBRyxFQUFFLElBQUksQ0FBQ0MsR0FBRyxFQUFFLElBQUksQ0FBQ0MsR0FBRyxFQUFFLElBQUksQ0FBQ0MsR0FBRztJQUN0RixNQUFPTixJQUFJRixNQUFNUyxHQUFHLEdBQUk7UUFDdEIsSUFBSUMsT0FBT1IsRUFBRVEsSUFBSTtRQUNqQixJQUFJQSxLQUFLQyxNQUFNLEVBQUU7WUFDZixJQUFJQyxPQUFPQyxLQUFLWCxFQUFFVyxFQUFFLEVBQUVDLEtBQUtaLEVBQUVZLEVBQUUsRUFBRUMsS0FBS2IsRUFBRWEsRUFBRSxFQUFFQyxLQUFLZCxFQUFFYyxFQUFFLEVBQUVDLEtBQUssQ0FBQ0osS0FBS0UsRUFBQyxJQUFLLEdBQUdHLEtBQUssQ0FBQ0osS0FBS0UsRUFBQyxJQUFLO1lBQzVGLElBQUlKLFFBQVFGLElBQUksQ0FBQyxFQUFFLEVBQUVWLE1BQU1JLElBQUksQ0FBQyxJQUFJTixnREFBSUEsQ0FBQ2MsT0FBT0MsSUFBSUMsSUFBSUcsSUFBSUM7WUFDNUQsSUFBSU4sUUFBUUYsSUFBSSxDQUFDLEVBQUUsRUFBRVYsTUFBTUksSUFBSSxDQUFDLElBQUlOLGdEQUFJQSxDQUFDYyxPQUFPSyxJQUFJSCxJQUFJQyxJQUFJRztZQUM1RCxJQUFJTixRQUFRRixJQUFJLENBQUMsRUFBRSxFQUFFVixNQUFNSSxJQUFJLENBQUMsSUFBSU4sZ0RBQUlBLENBQUNjLE9BQU9DLElBQUlLLElBQUlELElBQUlEO1lBQzVELElBQUlKLFFBQVFGLElBQUksQ0FBQyxFQUFFLEVBQUVWLE1BQU1JLElBQUksQ0FBQyxJQUFJTixnREFBSUEsQ0FBQ2MsT0FBT0ssSUFBSUMsSUFBSUgsSUFBSUM7UUFDOUQ7UUFDQWYsS0FBS0csSUFBSSxDQUFDRjtJQUNaO0lBQ0EsTUFBT0EsSUFBSUQsS0FBS1EsR0FBRyxHQUFJO1FBQ3JCVixTQUFTRyxFQUFFUSxJQUFJLEVBQUVSLEVBQUVXLEVBQUUsRUFBRVgsRUFBRVksRUFBRSxFQUFFWixFQUFFYSxFQUFFLEVBQUViLEVBQUVjLEVBQUU7SUFDekM7SUFDQSxPQUFPLElBQUk7QUFDYiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9kMy1xdWFkdHJlZS9zcmMvdmlzaXRBZnRlci5qcz9lZWM4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBRdWFkIGZyb20gXCIuL3F1YWQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oY2FsbGJhY2spIHtcbiAgdmFyIHF1YWRzID0gW10sIG5leHQgPSBbXSwgcTtcbiAgaWYgKHRoaXMuX3Jvb3QpIHF1YWRzLnB1c2gobmV3IFF1YWQodGhpcy5fcm9vdCwgdGhpcy5feDAsIHRoaXMuX3kwLCB0aGlzLl94MSwgdGhpcy5feTEpKTtcbiAgd2hpbGUgKHEgPSBxdWFkcy5wb3AoKSkge1xuICAgIHZhciBub2RlID0gcS5ub2RlO1xuICAgIGlmIChub2RlLmxlbmd0aCkge1xuICAgICAgdmFyIGNoaWxkLCB4MCA9IHEueDAsIHkwID0gcS55MCwgeDEgPSBxLngxLCB5MSA9IHEueTEsIHhtID0gKHgwICsgeDEpIC8gMiwgeW0gPSAoeTAgKyB5MSkgLyAyO1xuICAgICAgaWYgKGNoaWxkID0gbm9kZVswXSkgcXVhZHMucHVzaChuZXcgUXVhZChjaGlsZCwgeDAsIHkwLCB4bSwgeW0pKTtcbiAgICAgIGlmIChjaGlsZCA9IG5vZGVbMV0pIHF1YWRzLnB1c2gobmV3IFF1YWQoY2hpbGQsIHhtLCB5MCwgeDEsIHltKSk7XG4gICAgICBpZiAoY2hpbGQgPSBub2RlWzJdKSBxdWFkcy5wdXNoKG5ldyBRdWFkKGNoaWxkLCB4MCwgeW0sIHhtLCB5MSkpO1xuICAgICAgaWYgKGNoaWxkID0gbm9kZVszXSkgcXVhZHMucHVzaChuZXcgUXVhZChjaGlsZCwgeG0sIHltLCB4MSwgeTEpKTtcbiAgICB9XG4gICAgbmV4dC5wdXNoKHEpO1xuICB9XG4gIHdoaWxlIChxID0gbmV4dC5wb3AoKSkge1xuICAgIGNhbGxiYWNrKHEubm9kZSwgcS54MCwgcS55MCwgcS54MSwgcS55MSk7XG4gIH1cbiAgcmV0dXJuIHRoaXM7XG59XG4iXSwibmFtZXMiOlsiUXVhZCIsImNhbGxiYWNrIiwicXVhZHMiLCJuZXh0IiwicSIsIl9yb290IiwicHVzaCIsIl94MCIsIl95MCIsIl94MSIsIl95MSIsInBvcCIsIm5vZGUiLCJsZW5ndGgiLCJjaGlsZCIsIngwIiwieTAiLCJ4MSIsInkxIiwieG0iLCJ5bSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/visitAfter.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/x.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-quadtree/src/x.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultX: () => (/* binding */ defaultX)\n/* harmony export */ });\nfunction defaultX(d) {\n    return d[0];\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(_) {\n    return arguments.length ? (this._x = _, this) : this._x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy94LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sU0FBU0EsU0FBU0MsQ0FBQztJQUN4QixPQUFPQSxDQUFDLENBQUMsRUFBRTtBQUNiO0FBRUEsNkJBQWUsb0NBQVNDLENBQUM7SUFDdkIsT0FBT0MsVUFBVUMsTUFBTSxHQUFJLEtBQUksQ0FBQ0MsRUFBRSxHQUFHSCxHQUFHLElBQUksSUFBSSxJQUFJLENBQUNHLEVBQUU7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3guanM/NGVjMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZGVmYXVsdFgoZCkge1xuICByZXR1cm4gZFswXTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oXykge1xuICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh0aGlzLl94ID0gXywgdGhpcykgOiB0aGlzLl94O1xufVxuIl0sIm5hbWVzIjpbImRlZmF1bHRYIiwiZCIsIl8iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJfeCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/x.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/d3-quadtree/src/y.js":
/*!***********************************************!*\
  !*** ../../node_modules/d3-quadtree/src/y.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultY: () => (/* binding */ defaultY)\n/* harmony export */ });\nfunction defaultY(d) {\n    return d[1];\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(_) {\n    return arguments.length ? (this._y = _, this) : this._y;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2QzLXF1YWR0cmVlL3NyYy95LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sU0FBU0EsU0FBU0MsQ0FBQztJQUN4QixPQUFPQSxDQUFDLENBQUMsRUFBRTtBQUNiO0FBRUEsNkJBQWUsb0NBQVNDLENBQUM7SUFDdkIsT0FBT0MsVUFBVUMsTUFBTSxHQUFJLEtBQUksQ0FBQ0MsRUFBRSxHQUFHSCxHQUFHLElBQUksSUFBSSxJQUFJLENBQUNHLEVBQUU7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvZDMtcXVhZHRyZWUvc3JjL3kuanM/YWZlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZGVmYXVsdFkoZCkge1xuICByZXR1cm4gZFsxXTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oXykge1xuICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh0aGlzLl95ID0gXywgdGhpcykgOiB0aGlzLl95O1xufVxuIl0sIm5hbWVzIjpbImRlZmF1bHRZIiwiZCIsIl8iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJfeSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/d3-quadtree/src/y.js\n");

/***/ })

};
;